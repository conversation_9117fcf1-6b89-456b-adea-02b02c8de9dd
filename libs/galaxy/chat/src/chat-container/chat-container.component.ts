import {
  AfterViewInit,
  Component,
  ElementRef,
  HostBinding,
  input,
  OnChanges,
  OnDestroy,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { CdkScrollableModule } from '@angular/cdk/scrolling';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'glxy-chat-container',
  templateUrl: './chat-container.component.html',
  styleUrls: ['./chat-container.component.scss'],
  imports: [CdkScrollableModule, MatButtonModule, MatIconModule],
})
export class ChatContainerComponent implements AfterViewInit, OnChanges, OnDestroy {
  @HostBinding('class') class = 'glxy-chat-container';

  @ViewChild('chatContent') chatContent?: ElementRef<HTMLDivElement>;

  readonly chatId = input('');

  protected showNewMessageIndicator = false;

  get scrollable(): HTMLDivElement | undefined {
    return this.chatContent?.nativeElement;
  }

  get scrollTop(): number {
    return this.scrollable?.scrollTop ?? 0;
  }

  scrollTo({ top = 0, behavior = 'smooth' }: ScrollToOptions = {}): void {
    this.scrollable?.scrollTo({ top, behavior });
  }

  onNewMessageReceived(): void {
    if (this.scrollTop < 0) {
      this.showNewMessageIndicator = true;
    } else {
      this.mobileSafariScrollFix();
    }
  }

  onNewMessageSent(): void {
    // jump to bottom instantly so new message can animate into view
    this.scrollToBottom({ behavior: 'instant' });
    this.mobileSafariScrollFix();
  }

  scrollToBottom({ behavior }: ScrollToOptions = {}): void {
    // column-reverse means top: 0 is bottom
    // on iOS, always scroll to bottom instantly
    if (this.isIOS) {
      this.scrollTo({ top: 0, behavior: 'instant' });
    } else {
      this.scrollTo({ top: 0, behavior });
    }
  }

  onScroll(): void {
    if (this.scrollTop >= 0) {
      this.showNewMessageIndicator = false;
    }
  }

  mobileSafariScrollFix(): void {
    if (!this.isIOS) return;

    this.scrollTo({ top: this.scrollTop - 1 });
    window.setTimeout(() => {
      this.scrollToBottom();
    }, this.animationDuration() * 2);
  }
  isIOS = /iPad|iPhone|iPod/.test(window.navigator.userAgent);

  ngAfterViewInit(): void {
    this.resetAnimationDuration();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes?.chatId) {
      const isDifferentConversation = changes?.chatId.currentValue != changes?.chatId.previousValue;
      if (isDifferentConversation) {
        this.resetAnimationDuration();
      }
    }
  }

  private animationTimer = 0;
  animationDuration = input(0); // TODO(pascal): set this to 150 once µConversation updates are done
  currentAnimationDuration = 0;

  @HostBinding('style.--animation-duration')
  get getAnimationDurationStyle() {
    return `${this.currentAnimationDuration}ms`;
  }

  resetAnimationDuration(): void {
    window.clearTimeout(this.animationTimer);
    this.currentAnimationDuration = 0;
    this.animationTimer = window.setTimeout(() => {
      // always use 0 duration on iOS, so we don't have animation on the initial content
      const animationDuration = this.isIOS ? 0 : this.animationDuration();
      this.currentAnimationDuration = animationDuration;
    }, this.animationDuration() * 2);
    // timeout delay must be greater than the animation duration, so we don't
    // have animation on the initial content
  }

  ngOnDestroy(): void {
    window.clearTimeout(this.animationTimer);
  }
}
