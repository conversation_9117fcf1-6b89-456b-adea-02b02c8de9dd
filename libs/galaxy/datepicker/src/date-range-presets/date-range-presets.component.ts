import { ConnectionPositionPair } from '@angular/cdk/overlay';
import { booleanAttribute, Component, EventEmitter, HostBinding, Input, OnInit, Output } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { SelectInputOption } from '@vendasta/galaxy/input';
import {
  DateDuration,
  DateFormat,
  DateRange,
  calculateStartDate,
  differenceInDays,
  differenceInMonths,
  formatDate,
  formatUTCDate,
  formatUTCDateRange,
  getUTCEndOfDay,
  getUTCEndOfMonth,
  getUTCStartOfDay,
  subtractDays,
} from '@vendasta/galaxy/utility/date-utils';
import { BehaviorSubject, Observable } from 'rxjs';
import {
  BusinessQuarter,
  CollapseViewportWidth,
  DATE_RANGE_PERIODS,
  DateRangeDisplay,
  DateRangePresetPeriod,
} from './date-range-periods';

// creates an array of numbers that starts at "j" and increments by one and ends at the value of "k"
function createNumArray(j: number, k: number): number[] {
  const targetLength = Math.abs(k - j) + 1;
  const a = Array(targetLength);
  const b = [...a];
  const c = b.map((_: number, n: number) => n + j);
  return c;
}

const yearOptions = createNumArray(2000, new Date().getFullYear()).map((value) => ({
  value,
  label: value.toString(),
  selected: value === new Date().getFullYear(),
})) as SelectInputOption[];

const monthOptions = createNumArray(0, 11).map((value) => ({
  value,
  label: `GALAXY.DATE_RANGE.MONTHS.MONTH_${value}`,
  selected: value === 0,
})) as SelectInputOption[];

const quarterOptions = [
  { value: BusinessQuarter.Q1, label: 'Q1', selected: true },
  { value: BusinessQuarter.Q2, label: 'Q2' },
  { value: BusinessQuarter.Q3, label: 'Q3' },
  { value: BusinessQuarter.Q4, label: 'Q4' },
] as SelectInputOption[];

const OVERLAY_POSITIONS = [
  new ConnectionPositionPair({ originX: 'start', originY: 'bottom' }, { overlayX: 'start', overlayY: 'top' }),
  new ConnectionPositionPair({ originX: 'start', originY: 'top' }, { overlayX: 'start', overlayY: 'bottom' }),
];

@Component({
  selector: 'glxy-date-range-presets',
  templateUrl: './date-range-presets.component.html',
  styleUrls: ['./date-range-presets.component.scss'],
  standalone: false,
})
export class DateRangePresetsComponent implements OnInit {
  @HostBinding('class') class = 'glxy-date-range-presets';

  /* Whether to collapse this input when the window size is tablet-sized or large-tablet-sized */
  @Input() collapseViewportWidth: CollapseViewportWidth = 'tablet';

  /* Whether to show date comparisons of the previous period of the selected date range */
  @Input({ transform: booleanAttribute }) comparison = false;

  /* Whether to have a semi-transparent background on the overlay */
  @Input({ transform: booleanAttribute }) showBackdrop?: boolean = false;

  /* When a date range (start and end date) is selected that pair is emitted. */
  @Output() selectedDateRange = new EventEmitter<DateRange>();

  /* When a date range (start and end date) is selected the previous period date range is emitted. */
  @Output() selectedComparisonDateRange = new EventEmitter<DateRange>();

  /* When a date range period is selected that selected number is emitted. */
  @Output() selectedDateRangePeriod = new EventEmitter<DateRangePresetPeriod>();

  DATE_RANGE_PERIODS = DATE_RANGE_PERIODS;
  isOpen = false;
  dateRange?: DateRange;
  comparisonDateRange?: DateRange;

  yearOptions = yearOptions;
  monthOptions = monthOptions;
  quarterOptions = quarterOptions;

  selectedMonthYearControl = new UntypedFormControl(new Date().getFullYear());
  selectedQuarterYearControl = new UntypedFormControl(new Date().getFullYear());
  selectedMonthControl = new UntypedFormControl(0);
  selectedQuarterControl = new UntypedFormControl(BusinessQuarter.Q1);

  periodDisplay$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  periodDisplay$: Observable<string> = this.periodDisplay$$.asObservable();

  _positions = OVERLAY_POSITIONS;

  selectedPeriod?: DateRangePresetPeriod;
  selectedRange: string | null = null;
  selectedQuarter = 1;
  currentYear = new Date().getFullYear();

  dateRangeDisplay: DateRangeDisplay = {
    period: null,
    range: null,
  };

  /* Date range(start and end date) to be used if custom is set for defaultDatePeriod*/
  @Input() dateRangeForCustomDefault?: DateRange;

  @Input()
  set defaultDatePeriod(datePeriod: DateRangePresetPeriod) {
    this.selectedPeriod = datePeriod;
    const dateRange = this.DATE_RANGE_PERIODS.find((val) => val.id === Number(datePeriod));
    if (dateRange) {
      this.setDateRange(dateRange.value ?? '');
      if (this.dateRangeForCustomDefault && this.selectedPeriod === DateRangePresetPeriod.custom) {
        this.dateRange = this.dateRangeForCustomDefault;
      }
      this.handleOnApply();
    }
  }

  formatDateRange = formatUTCDateRange;

  ngOnInit(): void {
    if (!this.selectedPeriod) {
      this.defaultDatePeriod = DateRangePresetPeriod.lastSevenDays;
    }
    this.onMonthSelectChanges();
    this.onQuarterSelectChanges();
  }

  onMonthSelectChanges(): void {
    this.selectedMonthControl.valueChanges.subscribe((value) => {
      if (!this.dateRange?.start) return;
      this.currentYear = this.dateRange.start.getUTCFullYear();
      this.setMonthDateRange(value, this.currentYear);
    });
    this.selectedMonthYearControl.valueChanges.subscribe((value) => {
      this.setDateRangeYear('month', value);
    });
  }

  onQuarterSelectChanges(): void {
    this.selectedQuarterControl.valueChanges.subscribe((value) => {
      if (!this.dateRange?.start) return;
      this.selectedQuarter = value;
      this.currentYear = this.dateRange.start.getFullYear();
      this.setQuarterDateRange(value, this.currentYear);
    });
    this.selectedQuarterYearControl.valueChanges.subscribe((value) => {
      this.setDateRangeYear('quarter', value);
    });
  }

  toggleDropdown(): void {
    this.isOpen = !this.isOpen;
    this.currentYear = new Date().getFullYear();
  }

  handleOnPeriodChange(value: DateRangePresetPeriod): void {
    this.selectedPeriod = value;
    const datePeriod = this.DATE_RANGE_PERIODS.find((val) => val.id === Number(value));
    this.selectedRange = datePeriod?.value || null;
    if (this.selectedRange) {
      this.setDateRange(this.selectedRange);
    } else {
      if (value === DateRangePresetPeriod.byMonth) {
        // month selected
        const month = this.selectedMonthControl.value;
        const year = this.selectedMonthYearControl.value;
        this.setMonthDateRange(month, year);
      } else if (value === DateRangePresetPeriod.byQuarter) {
        // quarter selected
        const quarter = this.selectedQuarterControl.value;
        const year = this.selectedQuarterYearControl.value;
        this.setQuarterDateRange(quarter, year);
      }
    }
  }

  setMonthDateRange(month: number, year: number): void {
    this.dateRange = this.getMonthDateRange(month, year);
    this.comparisonDateRange = this.getMonthDateRange(month - 1, year);
  }

  getMonthDateRange(month: number, year: number): DateRange {
    const startDate = new Date(Date.UTC(year, month));
    return {
      start: startDate,
      end: new Date(getUTCEndOfMonth(startDate)),
    };
  }

  setQuarterDateRange(quarter: BusinessQuarter, year: number): void {
    this.dateRange = this.getQuarterDateRange(quarter, year);
    const previousQuarter = quarter - 1;
    this.comparisonDateRange = this.getQuarterDateRange(
      (quarter + 3) % 4, // don't have to deal with negative quarter numbers
      previousQuarter < 0 ? year - 1 : year,
    );
  }

  getQuarterStartMonth(quarter: number): number {
    switch (quarter) {
      case 0:
        return 0;
      case 1:
        return 3;
      case 2:
        return 6;
      case 3:
        return 9;
      default:
        return 0;
    }
  }

  setDateRangeYear(type: string, year: number): void {
    if (type === 'month') {
      if (!this.dateRange?.start) return;
      const tempMonth = this.dateRange.start.getUTCMonth();
      this.setMonthDateRange(tempMonth, year);
    } else {
      this.setQuarterDateRange(this.selectedQuarter, year);
    }
  }

  getQuarterDateRange(quarter: BusinessQuarter, year: number): DateRange {
    const startMonth = this.getQuarterStartMonth(quarter);
    const startDate = new Date(year, startMonth);
    const endDate = subtractDays(new Date(year, startMonth + 3), 1);
    return {
      start: startDate,
      end: endDate,
    };
  }

  setComparisonDateRange(startDate: Date, dur: number, unit: DateDuration): void {
    const comparisonEndDate = getUTCEndOfDay(subtractDays(startDate, 1));
    /* For 6 months and 12 months, the date should start with 1 day late for included day */
    const calculatedComparisonStartDate = calculateStartDate(startDate, dur, unit);
    if (unit !== DateDuration.days) {
      calculatedComparisonStartDate.setDate(calculatedComparisonStartDate.getDate() + 1);
    }
    this.comparisonDateRange = {
      start: calculatedComparisonStartDate,
      end: comparisonEndDate,
    };
  }

  setDateRange(dateRange: string): void {
    const [value, unitStr] = dateRange.split(' ');
    const today = new Date();
    // remove time from the start date (important so the selector works the same way in every timezone)
    const endDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const unit = unitStr === 'm' ? DateDuration.months : DateDuration.days;
    const dur = unit === DateDuration.days ? Number(value) - 1 : Number(value);
    /* For 6 months and 12 months, the date should start with 1 day late for included day */
    let startDate;
    const calculatedStartDate = calculateStartDate(endDate, dur, unit);
    if (unit === DateDuration.days) {
      startDate = calculatedStartDate;
    } else {
      calculatedStartDate.setDate(calculatedStartDate.getDate() + 1);
      startDate = calculatedStartDate;
    }
    // remove time from the end date (important so the selector works the same way in every timezone)
    startDate = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
    this.dateRange = {
      start: startDate,
      end: endDate,
    };
    this.setComparisonDateRange(startDate, dur, unit);
  }

  closeDropdown(): void {
    this.isOpen = false;
  }

  onBackdropClick(): void {
    this.closeDropdown();
  }

  handleOnApply(): void {
    const period = this.DATE_RANGE_PERIODS.find((period) => period.id === Number(this.selectedPeriod));
    this.dateRangeDisplay.period = period?.name || '';
    if (!this.dateRange) return;
    this.dateRangeDisplay.range = formatUTCDateRange(this.dateRange);
    this.setPeriodDisplay();
    this.emitDateRange();
    this.emitComparisonDateRange();
    this.emitDateRangePeriod();
    this.closeDropdown();
  }

  setPeriodDisplay(): void {
    if (this.selectedPeriod) {
      switch (this.selectedPeriod) {
        case DateRangePresetPeriod.byMonth:
          if (!this.dateRange?.start) return;
          this.periodDisplay$$.next(formatUTCDate(this.dateRange.start, DateFormat.longMonth));
          break;
        case DateRangePresetPeriod.byQuarter: {
          if (!this.dateRange?.start) return;
          const sect = 'Q' + (this.selectedQuarter + 1) + ' ' + this.dateRange.start.getFullYear();
          this.periodDisplay$$.next(sect);
          break;
        }
        default:
          if (this.dateRangeDisplay.period) this.periodDisplay$$.next(this.dateRangeDisplay.period);
      }
    }
  }

  emitDateRange(): void {
    // ensure the dates are emitted with a fully inclusive date / time
    // start: 2022-03-31T00:00:00.000Z
    // end: 2022-04-01T23:59:59.999Z
    if (!this.dateRange) return;
    const { start, end } = this.dateRange;
    if (!start || !end) return;
    this.selectedDateRange.emit({
      start: getUTCStartOfDay(start),
      end: getUTCEndOfDay(end),
    });
  }

  emitComparisonDateRange(): void {
    // ensure the dates are emitted with a fully inclusive date / time
    // start: 2022-03-31T00:00:00.000Z
    // end: 2022-04-01T23:59:59.999Z
    if (!this.comparisonDateRange) return;
    const { start, end } = this.comparisonDateRange;
    if (!start || !end) return;
    this.selectedComparisonDateRange.emit({
      start: getUTCStartOfDay(start),
      end: getUTCEndOfDay(end),
    });
  }

  emitDateRangePeriod(): void {
    const period = this.selectedPeriod;
    this.selectedDateRangePeriod.emit(period);
  }

  onDateRangeChange(value: DateRange): void {
    if (!value.start || !value.end) {
      return;
    }

    const today = new Date();
    const startDate = value.start;
    const endDate = value.end;
    const diffInDays = differenceInDays(endDate, startDate);
    const diffInMonths = differenceInMonths(endDate, startDate);

    if (formatDate(endDate) === formatDate(today)) {
      this.calculateSelectedPeriod(diffInDays, diffInMonths);
    } else {
      this.selectedPeriod = DateRangePresetPeriod.custom;
    }
    this.dateRange = {
      start: startDate,
      end: endDate,
    };
    this.setComparisonDateRange(startDate, diffInDays, DateDuration.days);
  }

  calculateSelectedPeriod(diffInDays: number, diffInMonths: number): void {
    if (diffInDays <= 90) {
      if (diffInDays === 7) {
        this.selectedPeriod = DateRangePresetPeriod.lastSevenDays;
      } else if (diffInDays === 30) {
        this.selectedPeriod = DateRangePresetPeriod.lastThirtyDays;
      } else if (diffInDays === 90) {
        this.selectedPeriod = DateRangePresetPeriod.lastNinetyDays;
      }
    } else if (diffInMonths > 3 || (diffInDays > 90 && diffInDays <= 366)) {
      if (diffInMonths === 6) {
        this.selectedPeriod = DateRangePresetPeriod.lastSixMonths;
      } else if (diffInMonths === 0) {
        this.selectedPeriod = DateRangePresetPeriod.lastTwentyMonths;
      }
    }
  }

  protected get dateContainerCollapseClass() {
    return 'date-container-' + this.collapseViewportWidth;
  }
}
