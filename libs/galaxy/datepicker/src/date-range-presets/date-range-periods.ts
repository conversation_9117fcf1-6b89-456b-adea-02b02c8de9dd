const I18N_DATE_RANGE_SELECTOR_PREFIX = 'GALAXY.DATE_RANGE.PERIOD_SELECTOR.';

export enum DateRangePresetPeriod {
  custom = 1,
  lastSevenDays = 2,
  lastThirtyDays = 3,
  lastNinetyDays = 4,
  lastSixMonths = 5,
  lastTwentyMonths = 6,
  byMonth = 7,
  byQuarter = 8,
}

export enum BusinessQuarter {
  Q1 = 0,
  Q2 = 1,
  Q3 = 2,
  Q4 = 3,
}

export const DATE_RANGE_PERIODS = [
  {
    id: DateRangePresetPeriod.lastSevenDays,
    name: `${I18N_DATE_RANGE_SELECTOR_PREFIX}LAST_7_DAYS`,
    value: '7 d',
  },
  {
    id: DateRangePresetPeriod.lastThirtyDays,
    name: `${I18N_DATE_RANGE_SELECTOR_PREFIX}LAST_30_DAYS`,
    value: '30 d',
  },
  {
    id: DateRangePresetPeriod.lastNinetyDays,
    name: `${I18N_DATE_RANGE_SELECTOR_PREFIX}LAST_90_DAYS`,
    value: '90 d',
  },
  {
    id: DateRangePresetPeriod.lastSixMonths,
    name: `${I18N_DATE_RANGE_SELECTOR_PREFIX}LAST_6_MONTHS`,
    value: '6 m',
  },
  {
    id: DateRangePresetPeriod.lastTwentyMonths,
    name: `${I18N_DATE_RANGE_SELECTOR_PREFIX}LAST_12_MONTHS`,
    value: '12 m',
  },
  {
    id: DateRangePresetPeriod.byMonth,
    name: `${I18N_DATE_RANGE_SELECTOR_PREFIX}BY_MONTH`,
  },
  {
    id: DateRangePresetPeriod.byQuarter,
    name: `${I18N_DATE_RANGE_SELECTOR_PREFIX}BY_QUARTER`,
  },
  {
    id: DateRangePresetPeriod.custom,
    name: `${I18N_DATE_RANGE_SELECTOR_PREFIX}CUSTOM`,
  },
];

export interface DateRangeDisplay {
  period: string | null;
  range: string | null;
}

export type CollapseViewportWidth = 'tablet' | 'tablet-large' | 'desktop-small';
