import { AfterViewInit, Component, ElementRef, HostBinding, inject, Input, signal, ViewChild } from '@angular/core';

import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { Clipboard } from '@angular/cdk/clipboard';
import hljs from 'highlight.js/lib/core';
import javascript from 'highlight.js/lib/languages/javascript';
import html from 'highlight.js/lib/languages/xml';

hljs.registerLanguage('javascript', javascript);
hljs.registerLanguage('html', html);

@Component({
  selector: 'glxy-code-sample',
  imports: [MatButtonModule, MatIconModule, TranslateModule],
  templateUrl: './code-sample.component.html',
  styleUrls: ['./code-sample.component.scss'],
})
export class CodeSampleComponent implements AfterViewInit {
  @HostBinding('class') class = 'glxy-code-sample';

  private readonly clipboard = inject(Clipboard);

  @Input() public title = '';
  @Input() public copyLabel = '';

  @ViewChild('snippet') codeElement?: ElementRef;

  readonly isCopying = signal(false);

  ngAfterViewInit(): void {
    hljs.highlightElement(this.codeElement?.nativeElement);
  }

  copyToClipboard(): void {
    this.clipboard.copy(this.codeElement?.nativeElement.innerText.trim());
    if (!this.isCopying()) {
      this.isCopying.set(true);
      window.setTimeout(() => {
        this.isCopying.set(false);
      }, 1200);
    }
  }
}
