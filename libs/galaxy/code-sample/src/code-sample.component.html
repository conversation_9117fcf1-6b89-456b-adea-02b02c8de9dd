<div class="code-container">
  <div class="header">
    <div class="title">
      {{ title }}
    </div>
    <div>
      @if (copyLabel) {
        <button mat-stroked-button (click)="copyToClipboard()">
          <span>{{ copyLabel | translate }}</span>
          <mat-icon>{{ isCopying() ? 'done' : 'content_copy' }}</mat-icon>
        </button>
      } @else {
        <button mat-icon-button (click)="copyToClipboard()">
          <mat-icon>{{ isCopying() ? 'done' : 'content_copy' }}</mat-icon>
        </button>
      }
    </div>
  </div>
  <div>
    <pre><code #snippet><ng-content  /></code></pre>
  </div>
</div>
