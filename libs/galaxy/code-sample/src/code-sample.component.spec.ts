import { createHostFactory, Spectator } from '@ngneat/spectator/jest';
import { CodeSampleComponent } from './code-sample.component';

// nx test galaxy --test-file=libs/galaxy/code-sample/src/code-sample.component.spec.ts --skip-nx-cache

describe('CodeSampleComponent', () => {
  let spectator: Spectator<CodeSampleComponent>;
  const createHost = createHostFactory({
    component: CodeSampleComponent,
  });

  test('should render', async () => {
    spectator = createHost(`
        <glxy-code-sample></glxy-code-sample>
    `);

    expect(spectator.element).toHaveClass('glxy-code-sample');
    expect('.glxy-code-sample').toExist();
  });
});
