@use 'design-tokens' as *;

:host {
  display: block;
  padding: 8px 0 8px;
}
.galaxy-form-field:not(:first-child) {
  margin-top: 8px;
}
.galaxy-password-strength {
  padding: 8px;

  .strength-title {
    font-size: $font-preset-5-size;

    span {
      text-transform: capitalize;
    }
  }
  .strength-bar-container {
    display: flex;
    flex-flow: row;
    margin-top: 4px;

    .strength-bar {
      flex: 1;
      height: 4px;
      background: #bdbdbd;
      transition: background-color 0.25s;

      &:nth-child(2) {
        margin: 0 2px;
      }
    }
    &.strength-weak {
      .strength-bar:nth-child(1) {
        background: #e53935;
      }
    }
    &.strength-medium {
      .strength-bar:nth-child(1),
      .strength-bar:nth-child(2) {
        background: #ffc107;
      }
    }
    &.strength-strong {
      .strength-bar {
        background: #4caf50;
      }
    }
  }
}
