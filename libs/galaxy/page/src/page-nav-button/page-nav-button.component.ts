import { Component, Input, OnInit, booleanAttribute } from '@angular/core';
import { Router } from '@angular/router';
import { PageService } from '../page.service';

@Component({
  selector: 'glxy-page-nav-button',
  templateUrl: './page-nav-button.component.html',
  styleUrls: ['./page-nav-button.component.scss'],
  standalone: false,
})
export class PageNavButtonComponent implements OnInit {
  @Input() previousPageUrl?: string;
  @Input() previousPageTitle?: string;
  @Input({ transform: booleanAttribute }) useHistory = false;
  @Input() historyBackButtonTitle = 'Back';

  defaultPageUrl = '';

  constructor(
    private readonly pageService: PageService,
    private readonly router: Router,
  ) {}

  ngOnInit(): void {
    if (this.previousPageUrl) {
      this.defaultPageUrl = this.previousPageUrl;
    }
  }

  navigateToPreviousUrl(): void {
    if (this.useHistory) {
      this.previousPageUrl = this.pageService.getPreviousPageUrl(this.defaultPageUrl);
      // if the previous page url is different than what was initially provided
      // then use the back button title text if provided
      if (this.previousPageUrl !== this.defaultPageUrl && this.historyBackButtonTitle) {
        this.previousPageTitle = this.historyBackButtonTitle;
      }
    }

    // pop will remove the last two items from the navigation history.
    // the page that was just visited and the page we are about to navigate back to
    // this prevents history looping
    this.pageService.pop();
    // Redirect with explicit query param and fragment assignment. If there are query params, remove it from the path
    const urlTree = this.router.parseUrl(this.previousPageUrl ?? '');
    const queryParamIndex = this.previousPageUrl?.indexOf('?');
    const path =
      (queryParamIndex ?? 0) > 0 ? this.previousPageUrl?.substring(0, queryParamIndex) : this.previousPageUrl;
    if (path) {
      this.router.navigate([path], { queryParams: urlTree.queryParams, fragment: urlTree.fragment ?? undefined });
    }
  }
}
