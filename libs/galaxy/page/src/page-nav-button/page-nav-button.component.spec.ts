import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { Router } from '@angular/router';
import { PageService } from '../page.service';
import { PageNavButtonComponent } from './page-nav-button.component';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Component } from '@angular/core';

@Component({ template: '', standalone: false })
class MockRouteComponent {}

describe('PageNavButtonComponent', () => {
  let component: PageNavButtonComponent;
  let fixture: ComponentFixture<PageNavButtonComponent>;
  const mockPageService = {
    getPreviousPageUrl: jest.fn(),
    pop: jest.fn(),
  };
  let router: Router;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        RouterTestingModule.withRoutes([
          { path: 'previous/url', component: MockRouteComponent },
          { path: 'default/url', component: MockRouteComponent },
        ]),
        MatIconModule,
        MatTooltipModule,
      ],
      declarations: [PageNavButtonComponent],
      providers: [{ provide: PageService, useValue: mockPageService }],
    }).compileComponents();

    router = TestBed.inject(Router);
    jest.spyOn(router, 'navigate');

    fixture = TestBed.createComponent(PageNavButtonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should set defaultPageUrl to previousPageUrl if provided', () => {
      component.previousPageUrl = 'default/url';
      component.ngOnInit();
      expect(component.defaultPageUrl).toBe('default/url');
    });

    it('should set defaultPageUrl to empty string if previousPageUrl is not provided', () => {
      component.previousPageUrl = undefined;
      component.ngOnInit();
      expect(component.defaultPageUrl).toBe('');
    });
  });

  describe('navigateToPreviousUrl', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should use PageService to get previous URL when useHistory is true', () => {
      component.useHistory = true;
      component.previousPageUrl = 'default/url';
      component.ngOnInit();
      mockPageService.getPreviousPageUrl.mockReturnValue('previous/url');

      component.navigateToPreviousUrl();

      expect(mockPageService.getPreviousPageUrl).toHaveBeenCalledWith('default/url');
      expect(router.navigate).toHaveBeenCalledWith(['previous/url'], expect.any(Object));
    });

    it('should not use PageService to get previous URL when useHistory is false', () => {
      component.useHistory = false;
      component.previousPageUrl = 'default/url';
      component.ngOnInit();
      component.navigateToPreviousUrl();

      expect(mockPageService.getPreviousPageUrl).not.toHaveBeenCalled();
      expect(router.navigate).toHaveBeenCalledWith(['default/url'], expect.any(Object));
    });

    it('should not navigate if both previousPageUrl and defaultPageUrl are falsey', () => {
      component.useHistory = true;
      component.ngOnInit();
      mockPageService.getPreviousPageUrl.mockReturnValue('');

      component.navigateToPreviousUrl();

      expect(mockPageService.getPreviousPageUrl).toHaveBeenCalledWith('');
      expect(router.navigate).not.toHaveBeenCalled();
    });

    it('should update previousPageTitle if previousPageUrl changes and historyBackButtonTitle is set', () => {
      component.useHistory = true;
      component.defaultPageUrl = 'default/url';
      component.historyBackButtonTitle = 'Go Back';
      component.ngOnInit();
      mockPageService.getPreviousPageUrl.mockReturnValue('previous/url');

      component.navigateToPreviousUrl();

      expect(component.previousPageTitle).toBe('Go Back');
    });

    it('should call pageService.pop on navigation', () => {
      component.useHistory = true;
      component.defaultPageUrl = 'default/url';
      mockPageService.getPreviousPageUrl.mockReturnValue('previous/url');

      component.navigateToPreviousUrl();

      expect(mockPageService.pop).toHaveBeenCalled();
    });
  });
});
