@use 'design-tokens' as *;
@use '../constants' as const;

// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
// Warning: ViewEncapsulation is disabled in this component
// Any styles defined here will affect the entire application
// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.glxy-page-toolbar {
  flex-grow: 0;
  flex-shrink: 0;

  display: flex;
  flex-wrap: wrap;
  align-items: center;

  border-bottom: 1px solid $border-color;
  min-height: $glxy-page-toolbar-min-height; // 60px
  box-sizing: border-box;
  transition: box-shadow 0.1s linear;
}

.scroll-shadow .glxy-page-toolbar {
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.glxy-page-toolbar .toolbar-wrapper {
  width: 100%;

  display: flex;
  flex-wrap: wrap;
}

.glxy-page-title-container {
  flex-grow: 1;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  min-height: $glxy-page-toolbar-min-height; // 60px
  box-sizing: border-box;
  margin-bottom: -1px;
  padding-left: $spacing-4;

  @media (max-width: const.$mobileLargeWidth) {
    padding-left: $spacing-3;
  }

  &:empty {
    display: none;
  }
}

.mat-icon-button.glxy-page-nav-toggle,
.mat-mdc-icon-button.glxy-page-nav-toggle {
  // TODO: remove legacy styles after MDC migration
  margin-left: -$spacing-2;
  margin-right: $spacing-2;
}

.glxy-page-title {
  font-size: 18px;
  font-weight: 500;
  display: flex;
  align-items: center;
}
.glxy-page-nav {
  display: flex;
  align-items: center;
}

.glxy-page-action-container {
  display: flex;
  align-items: center;
  flex-grow: 1000;
  flex-shrink: 1;
  min-height: $glxy-page-toolbar-min-height; // 60px
  margin-top: -1px;
  padding: $spacing-2 0;
  padding-right: $spacing-4;
  padding-left: $spacing-3;
  overflow-x: auto;

  @media (max-width: const.$mobileLargeWidth) {
    padding-right: $spacing-3;
  }

  &:empty {
    display: none;
  }
}

.glxy-page-title-actions-container {
  flex: 1 1 unset;

  &:empty {
    display: none;
  }
}

.glxy-page-main-actions-container {
  flex: 2 2;
  display: flex;
  justify-content: flex-end;

  &:empty {
    display: none;
  }
}

.glxy-page-actions {
  display: flex;
  align-items: center;

  & > button,
  & > .mat-mdc-button-base,
  & > .mat-button-base {
    // TODO: remove legacy styles after MDC migration
    margin-left: $spacing-2;
  }
}

.galaxy-page-extended-toolbar-container {
  flex-basis: 100%;

  display: flex;
  flex-wrap: wrap;
  align-items: center;
  overflow-x: auto;

  &:empty {
    display: none;
  }
}

.glxy-page-extended-toolbar {
  display: flex;
  flex-wrap: wrap;
  flex-basis: 100%;
  align-items: center;

  width: 100%;
  min-height: $glxy-page-extended-toolbar-min-height; // 52px
  border-top: 1px solid $border-color;
  background-color: rgba(0, 0, 0, 0.02);
  line-height: 1;

  .glxy-page-toolbar.extended-toolbar-has-padding & {
    padding: $spacing-2 $spacing-4;
  }
}

.glxy-page-extra-content {
  // Add back in padding to even both sides
  // .glxy-page-action-container has left padding of 16px
  // and right padding of 24px. When we are using custom content,
  // we want to make the padding an even 24px on either side
  padding-left: $spacing-2;
  width: 100%;

  display: flex;
  flex-wrap: wrap;
  align-items: center;

  &:empty {
    display: none;
  }
}
