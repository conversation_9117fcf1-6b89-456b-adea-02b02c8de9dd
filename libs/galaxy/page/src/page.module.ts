import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { TranslateModule } from '@ngx-translate/core';

import { PageComponent } from './page.component';
import { PageToolbarComponent } from './page-toolbar/page-toolbar.component';
import { PageNavButtonComponent } from './page-nav-button/page-nav-button.component';
import { PageTitleDirective } from './directives/page-title.directive';
import { PageNavDirective } from './directives/page-nav.directive';
import { PageActionsDirective } from './directives/page-actions.directive';
import { PageTitleActionsDirective } from './directives/page-title-actions.directive';
import { PageWrapperDirective } from './directives/page-wrapper.directive';
import { PageExtendedToolbarDirective } from './directives/page-extended-toolbar.directive';
import { PageBelowToolbarDirective } from './directives/page-below-toolbar';

export { PageComponent } from './page.component';
export { PageToolbarComponent } from './page-toolbar/page-toolbar.component';
export { PageNavButtonComponent } from './page-nav-button/page-nav-button.component';
export { PageTitleDirective } from './directives/page-title.directive';
export { PageNavDirective } from './directives/page-nav.directive';
export { PageActionsDirective } from './directives/page-actions.directive';
export { PageTitleActionsDirective } from './directives/page-title-actions.directive';
export { PageWrapperDirective } from './directives/page-wrapper.directive';
export { PageExtendedToolbarDirective } from './directives/page-extended-toolbar.directive';
export { PageBelowToolbarDirective } from './directives/page-below-toolbar';

export const MODULE_IMPORTS = [
  CommonModule,
  RouterModule,
  MatIconModule,
  MatTooltipModule,
  MatMenuModule,
  MatButtonModule,
  MatProgressBarModule,
  TranslateModule,
];

export const MODULE_EXPORTS = [
  PageComponent,
  PageToolbarComponent,
  PageTitleDirective,
  PageNavDirective,
  PageNavButtonComponent,
  PageActionsDirective,
  PageTitleActionsDirective,
  PageWrapperDirective,
  PageExtendedToolbarDirective,
  PageBelowToolbarDirective,
];

@NgModule({
  declarations: [...MODULE_EXPORTS],
  exports: MODULE_EXPORTS,
  imports: MODULE_IMPORTS,
})
export class GalaxyPageModule {}
