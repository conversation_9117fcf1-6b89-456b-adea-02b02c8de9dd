import { RouterTestingModule } from '@angular/router/testing';
import { Spectator, byTestId, byText, createHostFactory } from '@ngneat/spectator/jest';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { PageComponent } from './page.component';
import { MODULE_EXPORTS, MODULE_IMPORTS } from './page.module';

describe('PageComponent', () => {
  let spectator: Spectator<PageComponent>;

  const createHost = createHostFactory({
    component: PageComponent,
    imports: [...MODULE_IMPORTS, RouterTestingModule, TranslateTestingModule.withTranslations({})],
    declarations: [...MODULE_EXPORTS],
  });

  test('should display text passed in', async () => {
    spectator = createHost(
      `<glxy-page>
          <glxy-page-toolbar>
            <glxy-page-title>Page Title Content</glxy-page-title>
          </glxy-page-toolbar>
        </glxy-page>`,
    );

    expect(spectator.query(byText('Page Title Content'))).toBeDefined();
  });

  test('Should display all options, child directives and components', async () => {
    //const translateService = spectator.inject(TranslateService);
    spectator = createHost(
      `
        <glxy-page data-testid="glxy-page-id">
          <glxy-page-toolbar [showExtendedToolbar]="true" [showNavToggle]="true">

            <glxy-page-nav>
              <glxy-page-nav-button
                previousPageUrl=""
                previousPageTitle="Previous Page Title"
              >
              </glxy-page-nav-button>
            </glxy-page-nav>

            <glxy-page-title>
              Page Title Content
            </glxy-page-title>

            <glxy-page-title-actions>
              Page Title Action Content
            </glxy-page-title-actions>

            <glxy-page-actions>
                <button mat-flat-button color="primary">Galaxy Page Action</button>
            </glxy-page-actions>

            <glxy-page-extended-toolbar>
              Filter Bar Content
            </glxy-page-extended-toolbar>
          </glxy-page-toolbar>

          <glxy-page-wrapper data-testid="page-wrapper">
            Page Body Content
          </glxy-page-wrapper>

          <glxy-page-wrapper maxWidth="200" data-testid="page-wrapper-custom">
            Custom Width Page Wrapper
          </glxy-page-wrapper>

        </glxy-page>
      `,
    );
    // glxy-page-toolbar__nav-toggle
    expect(spectator.query(byText('menu'))).toBeDefined();
    // glxy-nav-button
    expect(spectator.query(byText('arrow_back'))).toBeDefined();
    // glxy-nav-button tooltip
    expect(spectator.query(byText('Previous Page Title'))).toBeDefined();
    // glxy-page-title
    expect(spectator.query(byText('Page Title Content'))).toBeDefined();
    // glxy-page-title-actions
    expect(spectator.query(byText('Page Title Action Content'))).toBeDefined();
    // glxy-page-actions
    expect(spectator.query(byText('Galaxy Page Action'))).toBeDefined();
    // glxy-page-extended-toolbar
    expect(spectator.query(byText('Filter Bar Content'))).toBeDefined();
    // page body main content
    expect(spectator.query(byText('Page Body Content'))).toBeDefined();
    // page wrapper custom width
    const pageWrapper = spectator.query(byTestId('page-wrapper'));
    expect(pageWrapper).toHaveStyle({ 'max-width': '900px' });
    // page wrapper custom width
    const pageWrapperCustom = spectator.query(byTestId('page-wrapper-custom'));
    expect(pageWrapperCustom).toHaveStyle({ 'max-width': '200px' });
  });
});
