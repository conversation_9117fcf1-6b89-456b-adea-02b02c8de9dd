<div class="table-filters-container">
  <div class="chip-filters">
    <mat-chip-set #filterChips>
      <div class="chip-set-container">
        <!--
      editFilterPopover needs to be accessed from template
      or else the popover position is incorrect
    -->
        @for (filter of filtersChips(); track filter; let index = $index) {
          <mat-chip
            (removed)="removeFilter(filter)"
            (click)="openEditFilter(index, filter); editFilterPopover.open()"
            [glxyPopover]="editFilterPopover"
            [attr.data-testid]="'chip-value-' + filter.fieldId"
            [removable]="!filter?.definition?.preset"
          >
            <span class="chip-wrapper">
              {{ filter.definition?.fieldName }}
              <span
                *ngIf="
                  filter.operatorSymbol &&
                  (filter.selectedValue || filter.canHaveEmptyValue || filter.customChip !== null)
                "
                class="chip-symbol"
              >
                {{ filter.operatorSymbol }}
              </span>
              <span *ngIf="filter.customChip; else withSelectedValue">
                <glxy-custom-filter-chip [filterChip]="filter.customChip" [initialValue]="filter.values" />
              </span>
              <ng-template #withSelectedValue>
                <span *ngIf="filter.selectedValue">
                  {{ filter.selectedValue }}
                </span>
              </ng-template>
            </span>
            <button
              *ngIf="(filter.selectedValue || filter.canHaveEmptyValue) && !filter.definition?.lockedValue"
              (click)="clearFilterValue(filter)"
              matChipRemove
            >
              <mat-icon>cancel</mat-icon>
            </button>

            <glxy-popover
              #editFilterPopover
              [closeOnBackdropClick]="true"
              [closeOnEscapeKey]="true"
              [positions]="popoverPositions"
              [keepOnScreen]="true"
              [maxWidth]="'100%'"
              [maxHeight]="'100%'"
              [padding]="'none'"
              (backdropClick)="closePopovers()"
            >
              @let selected = selectedFilterToEdit();
              @if (selected && selected.filter.filterId === filter.filterId) {
                <glxy-edit-filter-modal
                  #editFilterModal
                  [filterDefinition]="selected.definition"
                  [initialFilter]="selected.filter"
                  (applyFilterChanges)="updateSelectedFilter($event)"
                />
              }
            </glxy-popover>
          </mat-chip>
        }
      </div>
    </mat-chip-set>
  </div>

  <div class="filter-button-group" *ngIf="showAddFilter">
    <button
      class="add-filter-button"
      (click)="openAddFilter()"
      [glxyPopover]="addFilterPopover"
      data-testid="add-filter-button"
    >
      <mat-icon>add</mat-icon>
      {{ 'GALAXY.FILTER.CHIP.ADD_FILTER' | translate }}
      <glxy-popover
        #addFilterPopover
        data-testid="add-filter-modal"
        [closeOnBackdropClick]="true"
        [closeOnEscapeKey]="true"
        [positions]="popoverPositions"
        [keepOnScreen]="true"
        [maxWidth]="'100%'"
        [maxHeight]="'100%'"
        [padding]="'none'"
        (backdropClick)="closePopovers()"
      >
        @if (selectedFilterToAdd(); as selected) {
          <glxy-edit-filter-modal
            #addEditFilterModal
            [filterDefinition]="selected.definition"
            [initialFilter]="selected.filter"
            (applyFilterChanges)="addSelectedFilter($event)"
          />
        } @else {
          <glxy-add-filter-modal
            #addFilterModal
            [filtersHeader]="addFiltersHeader"
            (addFilter)="selectNewFilterFromDefinition($event)"
          />
        }
      </glxy-popover>
    </button>
    <button *ngIf="canClear()" class="clear-filters-button" (click)="clearFilters()">
      <mat-icon>clear</mat-icon>
      {{ 'GALAXY.FILTER.CHIP.CLEAR_FILTERS' | translate }}
    </button>
  </div>
</div>
