@use '../../../../styles/design-tokens' as dt;

:host {
  display: flex;
  flex-direction: column;
  max-height: 50vh;
}

.search-field {
  padding: dt.$spacing-3;
  border-bottom: 1px solid dt.$border-color;
}

.filters-section {
  width: 100%;
  overflow: auto;
  flex: 1;
  padding-bottom: dt.$spacing-2;
}

.filter-section-header {
  @include dt.text-preset-4--bold;
  color: dt.$primary-text-color;
  padding: dt.$spacing-3 dt.$spacing-3 dt.$spacing-1;
  margin: 0;
}

.filter-option {
  line-height: 1;
  padding: dt.$spacing-2 dt.$spacing-3 dt.$spacing-2 dt.$spacing-4;
  margin: 0;
  cursor: pointer;

  &:hover {
    background-color: dt.$secondary-background-color;
  }
}
