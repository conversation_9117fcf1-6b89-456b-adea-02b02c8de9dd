<div class="search-field" data-testid="search-input">
  <glxy-form-field suffixIcon="search" [showLabel]="false" [bottomSpacing]="false">
    <input
      #searchInput
      type="text"
      matInput
      [formControl]="searchControl"
      placeholder="{{ 'GALAXY.FILTER.CHIP.FIND_FILTER' | translate }}"
    />
  </glxy-form-field>
</div>

<div class="filters-section">
  <!-- we could make these headers / groups programmatically structured later -->
  <p class="filter-section-header">{{ filtersHeader | translate }}</p>
  <mat-action-list>
    @for (filterDefinition of filterDefinitions$ | async; track filterDefinition) {
      <button
        mat-list-item
        (click)="emitNewFilter(filterDefinition)"
        [attr.data-testid]="'filter-option-' + filterDefinition.fieldId"
      >
        {{ filterDefinition.fieldName }}
      </button>
    }
  </mat-action-list>
</div>
