import { CommonModule } from '@angular/common';
import { Component, ElementRef, EventEmitter, inject, Input, Output, ViewChild } from '@angular/core';
import { ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { Observable, startWith, switchMap } from 'rxjs';
import { GalaxyFilterDefinitionInterface, InnerGalaxyFilterChipInjectionToken } from '../interface';
import { MatActionList, MatListItem } from '@angular/material/list';

@Component({
  selector: 'glxy-add-filter-modal',
  templateUrl: './galaxy-add-filter-modal.component.html',
  styleUrls: ['./galaxy-add-filter-modal.component.scss'],
  imports: [
    CommonModule,
    MatSelectModule,
    ReactiveFormsModule,
    TranslateModule,
    GalaxyFormFieldModule,
    MatIconModule,
    MatDividerModule,
    MatInputModule,
    MatActionList,
    MatListItem,
  ],
})
export class GalaxyAddFilterModalComponent {
  @Input() filtersHeader = '';
  @Output() addFilter = new EventEmitter<GalaxyFilterDefinitionInterface>();

  @ViewChild('searchInput') searchInput?: ElementRef;

  searchControl: UntypedFormControl = new UntypedFormControl();

  filterDefinitions$: Observable<GalaxyFilterDefinitionInterface[]>;

  private readonly filtersDependencies = inject(InnerGalaxyFilterChipInjectionToken);
  constructor() {
    this.filterDefinitions$ = this.searchControl.valueChanges.pipe(
      startWith(''),
      switchMap((searchTerm) => {
        return this.filtersDependencies.listObjectFilters(searchTerm);
      }),
    );
  }

  emitNewFilter(filter: GalaxyFilterDefinitionInterface): void {
    this.addFilter.emit({
      ...filter,
    });
  }
}
