@use 'design-tokens' as *;
@import 'utilities';

.phone-filter-form {
  margin-bottom: $spacing-4;

  .glxy-label {
    font-size: $font-preset-4-size;
    font-weight: 500;
    display: block;
    margin-bottom: $spacing-2;
  }

  .phone-filter-code-prefix {
    padding-left: $spacing-2;
    padding-right: $spacing-2;

    @media (max-width: $mobile-breakpoint-max) {
      padding-right: $spacing-1;
    }
  }

  .phone-filter,
  .phone-filter-country {
    margin-bottom: 0;
    @media (max-width: $mobile-breakpoint-max) {
      margin-bottom: $spacing-2;
    }
  }

  .phone-filter-country {
    min-width: 0;
  }

  .phone-filter-extension {
    margin-bottom: 0;
    min-width: 0;
  }

  .glxy-form-row > .phone-filter,
  .glxy-form-row > .phone-filter-extension {
    padding-left: $spacing-2;
    @media (max-width: $mobile-breakpoint-max) {
      padding-left: $spacing-3;
    }
  }
}
