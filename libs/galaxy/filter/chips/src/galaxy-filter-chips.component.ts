import { CommonModule, DatePipe } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  Signal,
  Type,
  ViewChild,
  WritableSignal,
  booleanAttribute,
  computed,
  effect,
  inject,
  signal,
  ViewChildren,
  QueryList,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ReactiveFormsModule } from '@angular/forms';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyPopoverModule, PopoverComponent, PopoverPositions as pops } from '@vendasta/galaxy/popover';
import { of } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';
import { GalaxyAddFilterModalComponent } from './add-filter-modal/galaxy-add-filter-modal.component';
import { GalaxyCustomFilterChipComponent } from './custom-filter-chip.component';
import { GalaxyEditFilterModalComponent } from './edit-filter-modal/galaxy-edit-filter-modal.component';
import { GalaxyDateDefault, GalaxyFilterOperator, GalaxyFilterType } from './enums';
import {
  GalaxyAbstractFilterChip,
  GalaxyFilterChipDependencies,
  GalaxyFilterChipInjectionToken,
  GalaxyFilterDefinitionInterface,
  GalaxyFilterInputOverride,
  GalaxyFilterInterface,
  GalaxyStringFilterValue,
  GalaxySymbolsFilterOperators,
  InnerGalaxyFilterChipInjectionToken,
} from './interface';
import { FILTER_RANGE_OPERATORS } from './constants';
import { DEFAULT_COUNTRY_CODE } from './filter-inputs/phone-filter-input.component';
import { CountryCodeService, countryOptionsFactoryProvider } from '@vendasta/galaxy/utility/country-codes';
import { CountryCode } from 'libphonenumber-js';

interface SelectedFilterInterface {
  filter: GalaxyFilterInterface;
  definition: GalaxyFilterDefinitionInterface;
}

@Component({
  selector: 'glxy-filter-chips',
  templateUrl: './galaxy-filter-chips.component.html',
  styleUrls: ['./galaxy-filter-chips.component.scss'],
  imports: [
    CommonModule,
    MatSelectModule,
    ReactiveFormsModule,
    TranslateModule,
    GalaxyFormFieldModule,
    MatIconModule,
    GalaxyPopoverModule,
    GalaxyEditFilterModalComponent,
    MatChipsModule,
    GalaxyAddFilterModalComponent,
    GalaxyCustomFilterChipComponent,
  ],
  providers: [
    countryOptionsFactoryProvider,
    CountryCodeService,
    DatePipe,
    {
      provide: InnerGalaxyFilterChipInjectionToken,
      useFactory: () => {
        const deps = inject(GalaxyFilterChipInjectionToken);
        const depsWithDefaults: Required<GalaxyFilterChipDependencies> = {
          listObjectFilters: deps.listObjectFilters.bind(deps),
          getFieldOptions: deps.getFieldOptions?.bind?.(deps) ?? (() => of([])),
          getInitialAppliedFilters: deps.getInitialAppliedFilters?.bind?.(deps) ?? (async () => []),
          // eslint-disable-next-line @typescript-eslint/no-empty-function
          setInitialAppliedFilters: deps.setInitialAppliedFilters?.bind?.(deps) ?? (() => {}),
          getFilterInputOverrides: deps.getFilterInputOverrides?.bind?.(deps) ?? (() => of([])),
        };
        return depsWithDefaults;
      },
    },
  ],
})
export class GalaxyFilterChipsComponent implements OnInit {
  @ViewChild('addFilterPopover') addFilterPopover!: PopoverComponent;
  @ViewChild('addFilterModal') addFilterModal?: GalaxyAddFilterModalComponent;
  @ViewChild('editFilterPopover') editFilterPopover?: PopoverComponent;
  @ViewChild('addEditFilterModal') addEditFilterModal?: GalaxyEditFilterModalComponent;
  @ViewChildren('editFilterModal') editFilterModal?: QueryList<GalaxyEditFilterModalComponent>;

  @Input() addFiltersHeader = '';

  // showAddFilter defaults to true. It can be useful if you only wish to use preset filters.
  @Input({ transform: booleanAttribute }) showAddFilter = true;

  @Output() filtersChanged = new EventEmitter<GalaxyFilterInterface[]>();

  private readonly filtersDependencies = inject(InnerGalaxyFilterChipInjectionToken);

  filterDefinitions: Signal<GalaxyFilterDefinitionInterface[] | undefined> = toSignal(
    this.filtersDependencies.listObjectFilters(''),
  );
  filters: WritableSignal<GalaxyFilterInterface[]> = signal([]);

  selectedFilterToAdd: WritableSignal<SelectedFilterInterface | null> = signal(null);
  selectedFilterToEdit: WritableSignal<SelectedFilterInterface | null> = signal(null);
  filterInputOverrides = toSignal(this.filtersDependencies.getFilterInputOverrides());
  filterOverrideMap = computed(() => {
    const overrideMap = new Map<string, GalaxyFilterInputOverride>();
    for (const override of this.filterInputOverrides() || []) {
      overrideMap.set(override.fieldId, override);
    }
    return overrideMap;
  });

  popoverPositions = [pops.BottomLeft, pops.Bottom, pops.BottomRight, pops.TopLeft, pops.TopRight];
  filtersChips: Signal<FilterChipInterface[]> = computed(() =>
    this.filtersToFilterChips(this.filters(), this.filterOverrideMap()),
  );
  canClear = computed(() => {
    const nonPresetFilters = this.filters().filter((f) => {
      const def = this.getDefinition(f);
      return !def?.lockedValue || !def?.preset || f?.values?.length;
    });
    return nonPresetFilters.length >= 1;
  });

  private getDefinition(f: GalaxyFilterInterface) {
    return f.definition || this.filterDefinitions()?.find((d) => d.fieldId === f?.fieldId);
  }

  constructor(
    private readonly translate: TranslateService,
    private readonly countryCodeService: CountryCodeService,
    private readonly datePipe: DatePipe,
  ) {
    effect(() => this.filtersChanged.emit(this.filters()));
  }

  ngOnInit(): void {
    this.filtersDependencies
      .getInitialAppliedFilters()
      .then((filters) => this.filters.set(filters))
      .catch((err) => console.error(`failed to load default filters: ${err}`));
  }

  newFilterId(): string {
    return `filter-${uuidv4()}`;
  }

  clearFilters(): void {
    this.filters.update((filters) => {
      let presetFilters = filters.filter((f) => {
        const def = this.getDefinition(f);
        return def?.preset && !def?.lockedValue;
      });
      presetFilters = [...new Set(presetFilters)];
      const presetResettedFilterValues = presetFilters.map((f) => {
        const def = this.getDefinition(f);
        if (def) {
          f.operator = def.supportedOperators?.[0] ?? GalaxyFilterOperator.FILTER_OPERATOR_INVALID;
        }
        return { ...f, values: [] };
      });
      const lockedFilterValues = filters.filter((f) => {
        const def = this.getDefinition(f);
        return def?.lockedValue;
      });
      return [...presetResettedFilterValues, ...lockedFilterValues];
    });
  }

  clearFilterValue(filter: FilterChipInterface): void {
    this.filters.update((filters) => {
      return filters.map((f) => {
        if (f?.filterId === filter.filterId) {
          const def = this.getDefinition(f);
          if (def) {
            f.operator = def.supportedOperators?.[0] ?? GalaxyFilterOperator.FILTER_OPERATOR_INVALID;
          }
          return { ...f, values: [] };
        }
        return f;
      });
    });
  }

  removeFilter(filter: GalaxyFilterInterface): void {
    this.filters.update((filters) => filters.filter((f) => f?.filterId !== filter?.filterId));
  }

  // we won't actually add a filter until its details are filled in
  selectNewFilterFromDefinition(filterDefinition: GalaxyFilterDefinitionInterface): void {
    let defaultOperator = GalaxyFilterOperator.FILTER_OPERATOR_IS;
    if (filterDefinition?.type === GalaxyFilterType.FILTER_TYPE_TAG) {
      defaultOperator = GalaxyFilterOperator.FILTER_OPERATOR_IS_ALL;
    } else if (filterDefinition?.type === GalaxyFilterType.FILTER_TYPE_FLOAT) {
      defaultOperator = GalaxyFilterOperator.FILTER_OPERATOR_IS_EQUAL_TO;
      // Initially set dropdown to first supported operator if available
    } else if (filterDefinition?.supportedOperators?.[0]) {
      defaultOperator = filterDefinition.supportedOperators[0];
    }
    const filter: GalaxyFilterInterface = {
      fieldId: filterDefinition.fieldId ?? '',
      operator: defaultOperator,
    };
    this.selectFilterToAdd(filter);
  }

  private transformToSelectedFilter(
    filter: GalaxyFilterInterface,
    definition: GalaxyFilterDefinitionInterface,
  ): SelectedFilterInterface {
    return {
      filter: {
        fieldId: filter.fieldId,
        filterId: filter.filterId,
        operator: filter.operator,
        values: filter.values,
        floatTolerance: filter.floatTolerance,
        definition: filter.definition,
      },
      definition: definition,
    } as SelectedFilterInterface;
  }

  selectFilterToEdit(filter: GalaxyFilterInterface | null, index: number): void {
    this.selectFilterToAdd(null);
    if (!filter) {
      this.selectedFilterToEdit.set(null);
      return;
    }
    const definition = this.getDefinition(filter);
    if (!definition) {
      this.selectedFilterToEdit.set(null);
      return;
    }

    const selected = this.transformToSelectedFilter(filter, definition);
    this.selectedFilterToEdit.set(selected);
    setTimeout(() => {
      const element = this.editFilterModal?.get(index);
      if (element) {
        element.operatorSelect?.focus?.();
      }
    }, 0);
  }

  selectFilterToAdd(filter: GalaxyFilterInterface | null): void {
    this.selectedFilterToEdit.set(null);
    if (!filter) {
      this.selectedFilterToAdd.set(null);
      return;
    }
    const definition = this.getDefinition(filter);
    if (!definition) {
      this.selectedFilterToAdd.set(null);
      return;
    }

    const selected = this.transformToSelectedFilter(filter, definition);
    this.selectedFilterToAdd.set(selected);
    setTimeout(() => this.addEditFilterModal?.operatorSelect?.focus?.(), 0);
  }

  openAddFilter(): void {
    this.selectFilterToAdd(null);
    this.addFilterPopover.open();
    setTimeout(() => {
      this.addFilterModal?.searchInput?.nativeElement.focus();
    }, 0);
  }

  openEditFilter(index: number, filter: GalaxyFilterInterface): void {
    this.selectFilterToEdit(filter ?? this.selectedFilterToEdit()?.filter, index);
  }

  addSelectedFilter(filter: GalaxyFilterInterface): void {
    filter.filterId = filter.filterId || this.newFilterId();
    this.filters.update((filters) => [...filters, filter]);
    this.closePopovers();
  }

  updateSelectedFilter(filter: GalaxyFilterInterface): void {
    this.filters.update((filters) => filters.map((f) => (f?.filterId === filter.filterId ? filter : f)));
    this.closePopovers();
  }

  closePopovers() {
    // editing could occur from either popover, so try to close both
    this.editFilterPopover?.close();
    this.addFilterPopover?.close();
    this.selectedFilterToAdd.set(null);
    this.selectedFilterToEdit.set(null);
  }

  private getFilterChipOverride(
    filter: GalaxyFilterInterface,
    filterInputOverrides: Map<string, GalaxyFilterInputOverride>,
  ): Type<GalaxyAbstractFilterChip> | null {
    const filterOverride = filterInputOverrides.get(filter.fieldId);
    if (!filterOverride || !filterOverride.filterChip) {
      return null;
    }
    return filterOverride.filterChip;
  }

  private filtersToFilterChips(
    filters?: GalaxyFilterInterface[],
    filterOverrideMap?: Map<string, GalaxyFilterInputOverride>,
  ): FilterChipInterface[] {
    if (!filters) {
      return [];
    }
    const newFilters: FilterChipInterface[] = filters.map((filter) => {
      const definitionForFilter = this.getDefinition(filter);
      const filterValues = filter?.values;
      const operatorSymbol =
        GalaxySymbolsFilterOperators[filter?.operator || GalaxyFilterOperator.FILTER_OPERATOR_INVALID];
      const symbol = filterValues && filterValues.length > 0 ? this.translate.instant(operatorSymbol) : '';

      if (
        !filterValues?.length &&
        (filter.operator === GalaxyFilterOperator.FILTER_OPERATOR_IS_NOT_EMPTY ||
          filter.operator === GalaxyFilterOperator.FILTER_OPERATOR_IS_EMPTY ||
          filter.operator === GalaxyFilterOperator.FILTER_OPERATOR_IS_NOT_VALID ||
          filter.operator === GalaxyFilterOperator.FILTER_OPERATOR_IS_VALID)
      ) {
        return {
          ...filter,
          definition: definitionForFilter,
          operatorSymbol: this.translate.instant(operatorSymbol),
          canHaveEmptyValue: true,
        };
      }

      if (!filterValues?.length) {
        return {
          ...filter,
          definition: definitionForFilter,
          operatorSymbol: symbol,
        };
      }

      const hasStringProperty = Object.prototype.hasOwnProperty.call(filterValues[0], 'string');

      if (hasStringProperty) {
        if (filterValues.length <= 1) {
          return {
            ...filter,
            definition: definitionForFilter,
            operatorSymbol: symbol,
            selectedValue: filterValues[0].string,
          };
        } else {
          return {
            ...filter,
            definition: definitionForFilter,
            operatorSymbol: symbol,
            selectedValue: filterValues[0].string + ' +' + (filterValues.length - 1),
          };
        }
      }

      // handle float filter chips
      if (filterValues[0].float !== undefined) {
        const from = filterValues[0].float.toString();
        if (FILTER_RANGE_OPERATORS.includes(filter.operator)) {
          const to = filterValues[1]?.float?.toString() ?? '';
          return {
            ...filter,
            definition: definitionForFilter,
            operatorSymbol: symbol,
            selectedValue: `${from} - ${to}`,
          };
        }
        return {
          ...filter,
          definition: definitionForFilter,
          operatorSymbol: symbol,
          selectedValue: from,
        };
      }

      // handle integer filter chips
      if (filterValues[0].integer !== undefined) {
        const from = filterValues[0].integer.toString();
        if (FILTER_RANGE_OPERATORS.includes(filter.operator)) {
          const to = filterValues[1]?.integer?.toString() ?? '';
          return {
            ...filter,
            definition: definitionForFilter,
            operatorSymbol: symbol,
            selectedValue: `${from} - ${to}`,
          };
        }
        return {
          ...filter,
          definition: definitionForFilter,
          operatorSymbol: symbol,
          selectedValue: from,
        };
      }

      // Handle phone values
      if (filterValues[0].phone !== undefined) {
        let countryCode = '';
        const countryOption = this.countryCodeService.getCountryOption(
          (filterValues[0].phone.isoCountryCode || DEFAULT_COUNTRY_CODE) as CountryCode,
        );
        if (countryOption != null) {
          countryCode = countryOption.callingCode;
        }
        const phoneNumber = filterValues[0].phone.nationalNumber || '';
        let ext = '';
        const extension = filterValues[0].phone.extension || '';
        if (extension != null && extension.length > 0) {
          ext = ' #' + extension;
        }
        return {
          ...filter,
          definition: definitionForFilter,
          operatorSymbol: symbol,
          selectedValue: countryCode + phoneNumber + ext,
        };
      }

      const hasBooleanProperty = Object.prototype.hasOwnProperty.call(filterValues[0], 'boolean');
      if (hasBooleanProperty) {
        return {
          ...filter,
          definition: definitionForFilter,
          operatorSymbol: symbol,
          selectedValue: filterValues[0].boolean ? 'True' : 'False',
        };
      }

      const dateDefaultStringFilter = [
        GalaxyFilterOperator.FILTER_OPERATOR_IS,
        GalaxyFilterOperator.FILTER_OPERATOR_IS_NOT,
        GalaxyFilterOperator.FILTER_OPERATOR_IS_BEFORE,
        GalaxyFilterOperator.FILTER_OPERATOR_IS_BEFORE_OR_ON,
        GalaxyFilterOperator.FILTER_OPERATOR_IS_AFTER,
        GalaxyFilterOperator.FILTER_OPERATOR_IS_AFTER_OR_ON,
      ];
      const { dateDefault = GalaxyDateDefault.DATE_DEFAULT_INVALID } = filterValues[0];
      let translateFilter = this.translate.instant(GalaxyStringFilterValue[dateDefault]);
      if (filterValues[0].date) {
        translateFilter = this.datePipe.transform(filterValues[0].date?.toString(), 'mediumDate', 'UTC');
      }

      if (
        dateDefaultStringFilter.includes(filter.operator || GalaxyFilterOperator.FILTER_OPERATOR_INVALID) &&
        !hasStringProperty
      ) {
        return {
          ...filter,
          definition: definitionForFilter,
          operatorSymbol: symbol,
          selectedValue: translateFilter,
        };
      }

      if (
        filter.operator === GalaxyFilterOperator.FILTER_OPERATOR_IS_NOT_BETWEEN ||
        filter.operator === GalaxyFilterOperator.FILTER_OPERATOR_IS_BETWEEN
      ) {
        const fromDate = this.datePipe.transform(filterValues[0].date?.toString(), 'shortDate');
        const toDate = this.datePipe.transform(filterValues[1].date?.toString(), 'shortDate');

        return {
          ...filter,
          definition: definitionForFilter,
          operatorSymbol: symbol,
          selectedValue: fromDate + ' - ' + toDate,
        };
      }

      return {
        ...filter,
        definition: definitionForFilter,
      };
    });
    const filtersWithOverride: FilterChipInterface[] = newFilters.map((filter) => {
      const overrideChip = this.getFilterChipOverride(filter, filterOverrideMap ?? new Map());
      if (overrideChip !== null && !filter.canHaveEmptyValue) {
        return {
          ...filter,
          customChip: overrideChip,
        };
      }
      return filter;
    });
    return filtersWithOverride;
  }
}

interface FilterChipInterface extends GalaxyFilterInterface {
  operatorSymbol?: string;
  selectedValue?: string;
  canHaveEmptyValue?: boolean;
  customChip?: Type<GalaxyAbstractFilterChip>;
}
