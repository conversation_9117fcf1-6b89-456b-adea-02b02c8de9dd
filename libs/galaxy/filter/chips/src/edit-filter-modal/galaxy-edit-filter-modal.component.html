<ng-container *ngIf="_initialFilter() as _initialFilter">
  <div [attr.data-testid]="'edit-filter-' + _initialFilter.fieldId" class="filter-row-label">
    {{ filterDefinition?.fieldName ?? '' }}
  </div>
  <div class="filter-editor-container">
    <div class="row field-row">
      <div *ngIf="filterDefinition?.supportedOperators?.length > 1; else operatorLabel" class="col field-col">
        <glxy-form-field class="filter-operator-field" [bottomSpacing]="'none'">
          <mat-select
            #operatorSelect
            class="filter-operator-selector"
            [placeholder]="'GALAXY.FILTER.CHIP.SELECT_OPERATOR_PLACEHOLDER' | translate"
            [formControl]="selectedOperatorControl"
            data-testid="filter-operator-selector"
          >
            <ng-container *ngFor="let operatorOption of operatorOptions()">
              <mat-option [value]="operatorOption.operator">{{ operatorOption.label | translate }}</mat-option>
            </ng-container>
          </mat-select>
        </glxy-form-field>
      </div>
      <ng-template #operatorLabel>
        <div class="operator-label" *ngIf="operatorOptions()?.length === 1; else defaultOperator">
          {{ operatorOptions()[0].label | translate }}
        </div>
        <ng-template #defaultOperator>
          <div class="operator-label">
            {{ 'GALAXY.FILTER.CHIP.OPERATOR.IS' | translate }}
          </div>
        </ng-template>
      </ng-template>
      <ng-container [ngSwitch]="inputType()">
        <glxy-string-filter-input
          class="col field-col"
          *ngSwitchCase="FilterInputType.FILTER_INPUT_TYPE_SINGLE_SELECT_STRING"
          [initialValue]="_initialFilter?.values || []"
          [fieldId]="filterDefinition.fieldId"
          [disabled]="filterDefinition.lockedValue"
          (valueChanges)="filterValuesChanged($event)"
        />
        <glxy-tags-filter-input
          class="col field-col"
          *ngSwitchCase="FilterInputType.FILTER_INPUT_TYPE_MULTI_SELECT_STRING"
          [initialValue]="_initialFilter?.values || []"
          [tagsFieldId]="filterDefinition.fieldId ?? ''"
          [disabled]="filterDefinition.lockedValue"
          (valueChanges)="filterValuesChanged($event)"
        />
        <glxy-tags-filter-input
          class="col field-col"
          *ngSwitchCase="FilterInputType.FILTER_INPUT_TYPE_SINGLE_SELECT_TAG"
          [initialValue]="_initialFilter?.values || []"
          (valueChanges)="filterValuesChanged($event)"
          [tagsFieldId]="filterDefinition.fieldId ?? ''"
          [disabled]="filterDefinition.lockedValue"
          [maxTags]="1"
        />
        <glxy-tags-filter-input
          class="col field-col"
          *ngSwitchCase="FilterInputType.FILTER_INPUT_TYPE_MULTI_SELECT_TAG"
          [initialValue]="_initialFilter?.values || []"
          (valueChanges)="filterValuesChanged($event)"
          [tagsFieldId]="filterDefinition.fieldId ?? ''"
        />
        <glxy-radio-filter-input
          class="row field-col"
          *ngSwitchCase="FilterInputType.FILTER_INPUT_TYPE_SINGLE_SELECT_RADIO"
          [fieldId]="filterDefinition.fieldId ?? ''"
          [initialValue]="_initialFilter?.values || []"
          [disabled]="filterDefinition.lockedValue"
          (valueChanges)="filterValuesChanged($event)"
        />
        <glxy-checkbox-filter-input
          class="row field-col"
          *ngSwitchCase="FilterInputType.FILTER_INPUT_TYPE_MULTI_SELECT_CHECKBOX"
          [fieldId]="filterDefinition.fieldId ?? ''"
          [initialValue]="_initialFilter?.values || []"
          [disabled]="filterDefinition.lockedValue"
          (valueChanges)="filterValuesChanged($event)"
        />
        <glxy-no-input-filter-input
          class="col field-col"
          *ngSwitchCase="FilterInputType.FILTER_INPUT_TYPE_NO_INPUT"
          [initialValue]="[]"
          (valueChanges)="filterValuesChanged($event)"
        />
        <glxy-date-filter-input
          class="col field-col"
          *ngSwitchCase="FilterInputType.FILTER_INPUT_TYPE_DATE_SELECT"
          [initialValue]="_initialFilter?.values || []"
          [operator]="selectedOperatorControl.value"
          [disabled]="filterDefinition.lockedValue"
          (valueChanges)="filterValuesChanged($event)"
        />
        <glxy-float-filter-input
          class="col field-col"
          *ngSwitchCase="FilterInputType.FILTER_INPUT_TYPE_FLOAT"
          [initialValue]="_initialFilter?.values || []"
          [operator]="selectedOperatorControl.value"
          [disabled]="filterDefinition.lockedValue"
          (valueChanges)="filterValuesChanged($event)"
        />
        <glxy-integer-filter-input
          class="col field-col"
          *ngSwitchCase="FilterInputType.FILTER_INPUT_TYPE_INTEGER"
          [initialValue]="_initialFilter?.values || []"
          [operator]="selectedOperatorControl.value"
          [disabled]="filterDefinition.lockedValue"
          (valueChanges)="filterValuesChanged($event)"
        />
        <glxy-boolean-filter-input
          class="col field-col"
          *ngSwitchCase="FilterInputType.FILTER_INPUT_TYPE_BOOLEAN"
          [initialValue]="_initialFilter?.values || []"
          [disabled]="filterDefinition.lockedValue"
          (valueChanges)="filterValuesChanged($event)"
        />
        <glxy-custom-filter-input
          class="col field-col"
          *ngSwitchCase="FilterInputType.FILTER_INPUT_TYPE_CUSTOM_INPUT"
          [initialValue]="_initialFilter?.values || []"
          [filterOverride]="filterOverride()"
          [operator]="selectedOperator()"
          (valueChanges)="filterValuesChanged($event)"
        />
        <glxy-phone-filter-input
          class="row field-row"
          *ngSwitchCase="FilterInputType.FILTER_INPUT_TYPE_PHONE"
          [initialValue]="_initialFilter?.values || []"
          [fieldId]="filterDefinition.fieldId"
          (valueChanges)="filterValuesChanged($event)"
          [disabled]="filterDefinition.lockedValue"
        />
        <glxy-none-selected-input-filter-input
          class="col field-col"
          *ngSwitchCase="FilterInputType.FILTER_INPUT_TYPE_NO_OPERATOR_SELECTED"
        />
      </ng-container>
      <ng-container
        *ngIf="!filterDefinition?.supportedOperators?.length || filterDefinition?.supportedOperators?.length <= 1"
      >
        <ng-container *ngTemplateOutlet="applyButton" />
      </ng-container>
    </div>
    <ng-container *ngIf="filterDefinition?.supportedOperators?.length > 1">
      <ng-container *ngTemplateOutlet="applyButton" />
    </ng-container>
  </div>
</ng-container>

<ng-template #applyButton>
  <button mat-flat-button color="primary" class="row-button" (click)="applyFilter()" data-testid="filter-apply-button">
    {{ 'GALAXY.FILTER.CHIP.ACTIONS.APPLY' | translate }}
  </button>
</ng-template>
