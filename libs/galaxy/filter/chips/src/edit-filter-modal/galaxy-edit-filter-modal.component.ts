import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  EventEmitter,
  inject,
  Input,
  Output,
  signal,
  ViewChild,
  WritableSignal,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatSelect, MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyFilterOperator, GalaxyFilterType } from '../enums';
import { GalaxyCheckboxFilterInputComponent } from '../filter-inputs/checkbox-filter-input.component';
import { GalaxyCustomFilterInputComponent } from '../filter-inputs/custom-filter-input.component';
import { GalaxyDateFilterInputComponent } from '../filter-inputs/date-filter-input';
import { GalaxyNoInputFilterInputComponent } from '../filter-inputs/no-input.component';
import { GalaxyNoneSelectedFilterInputComponent } from '../filter-inputs/none-selected-input.component';
import { GalaxyRadioFilterInputComponent } from '../filter-inputs/radio-filter-input.component';
import { GalaxyStringFilterInputComponent } from '../filter-inputs/string-filter-input.component';
import { GalaxyTagsFilterInputComponent } from '../filter-inputs/tags-filter-input.component';
import { GalaxyBooleanFilterInputComponent } from '../filter-inputs/boolean-filter-input.component';
import { GalaxyPhoneFilterInputComponent } from '../filter-inputs/phone-filter-input.component';
import { filterOperatorToLabel } from '../filter-operator-labels';
import {
  GalaxyAbstractFilterInput,
  GalaxyFilterDefinitionInterface,
  GalaxyFilterInterface,
  GalaxyFilterValueInterface,
  InnerGalaxyFilterChipInjectionToken,
} from '../interface';
import { GalaxyFloatFilterInputComponent } from '../filter-inputs/float-filter-input';
import { GalaxyIntegerFilterInputComponent } from '../filter-inputs/integer-filter-input.component';

enum FilterInputType {
  FILTER_INPUT_TYPE_NO_OPERATOR_SELECTED = 'NoOperatorSelected',
  FILTER_INPUT_TYPE_NO_INPUT = 'NoInputFilter',
  FILTER_INPUT_TYPE_SINGLE_SELECT_STRING = 'SingleSelectStringInput',
  FILTER_INPUT_TYPE_SINGLE_SELECT_TAG = 'SingleSelectTagInput',
  FILTER_INPUT_TYPE_MULTI_SELECT_TAG = 'MultiSelectTagInput',
  FILTER_INPUT_TYPE_DATE_SELECT = 'DateSelectInput',
  FILTER_INPUT_TYPE_MULTI_SELECT_STRING = 'MultiSelectStringInput',
  FILTER_INPUT_TYPE_SINGLE_SELECT_RADIO = 'SingleSelectRadioInput',
  FILTER_INPUT_TYPE_MULTI_SELECT_CHECKBOX = 'MultiSelectCheckboxInput',
  FILTER_INPUT_TYPE_CUSTOM_INPUT = 'CustomInput',
  FILTER_INPUT_TYPE_FLOAT = 'FloatInput',
  FILTER_INPUT_TYPE_BOOLEAN = 'BooleanInput',
  FILTER_INPUT_TYPE_INTEGER = 'IntegerInput',
  FILTER_INPUT_TYPE_PHONE = 'PhoneInput',
}

@Component({
  selector: 'glxy-edit-filter-modal',
  templateUrl: './galaxy-edit-filter-modal.component.html',
  styleUrls: ['./galaxy-edit-filter-modal.component.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    MatSelectModule,
    MatButtonModule,
    MatInputModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    GalaxyStringFilterInputComponent,
    GalaxyNoInputFilterInputComponent,
    GalaxyNoneSelectedFilterInputComponent,
    GalaxyTagsFilterInputComponent,
    GalaxyDateFilterInputComponent,
    GalaxyNoInputFilterInputComponent,
    GalaxyNoneSelectedFilterInputComponent,
    GalaxyTagsFilterInputComponent,
    GalaxyCheckboxFilterInputComponent,
    GalaxyRadioFilterInputComponent,
    GalaxyCustomFilterInputComponent,
    GalaxyFloatFilterInputComponent,
    GalaxyBooleanFilterInputComponent,
    GalaxyIntegerFilterInputComponent,
    GalaxyPhoneFilterInputComponent,
  ],
})
export class GalaxyEditFilterModalComponent {
  @ViewChild(GalaxyAbstractFilterInput) filterInput!: GalaxyAbstractFilterInput;
  @ViewChild('operatorSelect') operatorSelect?: MatSelect;

  @Output() applyFilterChanges = new EventEmitter<GalaxyFilterInterface>();

  FilterInputType = FilterInputType;

  selectedOperatorControl: FormControl<GalaxyFilterOperator> = new FormControl<GalaxyFilterOperator>(
    GalaxyFilterOperator.FILTER_OPERATOR_IS,
    { nonNullable: true },
  );

  // this flows in one direction: from the filter inputs into this component
  // attempting to set it within this component will result in it being overwritten
  // if you need to clear the filter input, use the clearValue() method
  filterValues: WritableSignal<GalaxyFilterValueInterface[]> = signal([]);

  // we expect this to be set once and never change
  _initialFilter: WritableSignal<GalaxyFilterInterface | null> = signal(null);

  @Input() set initialFilter(initialFilter: GalaxyFilterInterface | null) {
    if (initialFilter) {
      this.selectedOperatorControl.setValue(initialFilter.operator);
    }
    if (this.filterDefinition?.lockedValue) {
      this.selectedOperatorControl.disable();
    }
    this._initialFilter.set(initialFilter);
  }

  @Input({ required: true }) filterDefinition!: GalaxyFilterDefinitionInterface;

  operatorOptions = computed(() => this.getOperatorOptions(this._initialFilter()));
  inputType = computed(() => {
    const foundOverride = this.filterInputOverrides().find(
      (override) => override.fieldId === this._initialFilter()?.fieldId,
    );
    const inputType = this.getInputType(this._initialFilter(), this.selectedOperator());
    if (foundOverride && inputType !== FilterInputType.FILTER_INPUT_TYPE_NO_INPUT) {
      return FilterInputType.FILTER_INPUT_TYPE_CUSTOM_INPUT;
    }
    return inputType;
  });

  operatorControl = toSignal(this.selectedOperatorControl.valueChanges, {
    initialValue: this.selectedOperatorControl.value,
  });

  selectedOperator = computed(() => this.operatorControl());

  // used to validate that the filter input has been set when applying it
  filterValuesSet = computed(() => {
    return this.filterValues().length > 0;
  });

  // float tolerance will be set to 0.001 if a float filter is present
  filterFloatTolerance = computed(() => {
    const filterValues = this.filterValues();
    if (filterValues?.findIndex((value) => value.float !== undefined) >= 0) {
      return 0.001;
    }
    return undefined;
  });

  private readonly filtersDependencies = inject(InnerGalaxyFilterChipInjectionToken);
  filterInputOverrides = toSignal(this.filtersDependencies.getFilterInputOverrides(), {
    initialValue: [],
  });
  filterOverride = computed(() => {
    return this.filterInputOverrides().find((override) => override.fieldId === this._initialFilter()?.fieldId);
  });

  applyFilter() {
    const selectedOperator = this.selectedOperator();
    const filterValues = this.filterValues();
    const _filterValuesSet = this.filterValuesSet();
    const filter = this._initialFilter();
    const floatTolerance = this.filterFloatTolerance();

    const operatorHasNoInput = [
      GalaxyFilterOperator.FILTER_OPERATOR_IS_NOT_EMPTY,
      GalaxyFilterOperator.FILTER_OPERATOR_IS_EMPTY,
      GalaxyFilterOperator.FILTER_OPERATOR_IS_NOT_VALID,
      GalaxyFilterOperator.FILTER_OPERATOR_IS_VALID,
    ].includes(selectedOperator);
    if (
      selectedOperator === GalaxyFilterOperator.FILTER_OPERATOR_INVALID ||
      (!_filterValuesSet && !operatorHasNoInput && !this.filterDefinition.preset) // presets are never removed, so they can have an empty value
    ) {
      return;
    }

    // take initial filter and apply delta
    if (filter) {
      filter.operator = selectedOperator;
      filter.values = filterValues;
      filter.floatTolerance = floatTolerance;

      this.applyFilterChanges.emit(filter);
    }
  }

  getOperatorOptions(filter: GalaxyFilterInterface | null): OperatorOption[] {
    if (!filter) {
      return [];
    }
    const supportedOperators = this.filterDefinition.supportedOperators;
    if (supportedOperators === undefined) {
      return [];
    }

    return supportedOperators.map((operator) => ({
      operator: operator,
      label: filterOperatorToLabel[operator],
    }));
  }

  filterValuesChanged(filterValues: GalaxyFilterValueInterface[]) {
    this.filterValues.set(filterValues);
  }

  private getInputType(filter: GalaxyFilterInterface | null, operator: GalaxyFilterOperator): FilterInputType {
    if (this.filterDefinition.type === GalaxyFilterType.FILTER_TYPE_CHECKBOX) {
      if (
        [
          GalaxyFilterOperator.FILTER_OPERATOR_IS,
          GalaxyFilterOperator.FILTER_OPERATOR_IS_NOT,
          GalaxyFilterOperator.FILTER_OPERATOR_IS_EQUAL_TO,
          GalaxyFilterOperator.FILTER_OPERATOR_IS_NOT_EQUAL_TO,
        ].includes(operator)
      ) {
        return FilterInputType.FILTER_INPUT_TYPE_SINGLE_SELECT_RADIO;
      }
      return FilterInputType.FILTER_INPUT_TYPE_MULTI_SELECT_CHECKBOX;
    }
    if (
      [GalaxyFilterOperator.FILTER_OPERATOR_IS_NOT_EMPTY, GalaxyFilterOperator.FILTER_OPERATOR_IS_EMPTY].includes(
        operator,
      )
    ) {
      return FilterInputType.FILTER_INPUT_TYPE_NO_INPUT;
    } else if (this.filterDefinition.type === GalaxyFilterType.FILTER_TYPE_DATE) {
      return FilterInputType.FILTER_INPUT_TYPE_DATE_SELECT;
    } else if (this.filterDefinition.type === GalaxyFilterType.FILTER_TYPE_FLOAT) {
      return FilterInputType.FILTER_INPUT_TYPE_FLOAT;
    } else if (this.filterDefinition.type === GalaxyFilterType.FILTER_TYPE_INTEGER) {
      return FilterInputType.FILTER_INPUT_TYPE_INTEGER;
    } else if (
      [
        GalaxyFilterOperator.FILTER_OPERATOR_IS,
        GalaxyFilterOperator.FILTER_OPERATOR_IS_NOT,
        GalaxyFilterOperator.FILTER_OPERATOR_CONTAINS,
        GalaxyFilterOperator.FILTER_OPERATOR_DOES_NOT_CONTAIN,
      ].includes(operator)
    ) {
      switch (this.filterDefinition.type) {
        case GalaxyFilterType.FILTER_TYPE_STRING:
          return FilterInputType.FILTER_INPUT_TYPE_SINGLE_SELECT_STRING;
        case GalaxyFilterType.FILTER_TYPE_TAG:
          return FilterInputType.FILTER_INPUT_TYPE_SINGLE_SELECT_TAG;
        case GalaxyFilterType.FILTER_TYPE_BOOLEAN:
          return FilterInputType.FILTER_INPUT_TYPE_BOOLEAN;
        case GalaxyFilterType.FILTER_TYPE_PHONE:
          return FilterInputType.FILTER_INPUT_TYPE_PHONE;
      }
    } else if (
      [
        GalaxyFilterOperator.FILTER_OPERATOR_IS_ANY,
        GalaxyFilterOperator.FILTER_OPERATOR_IS_NOT_ANY,
        GalaxyFilterOperator.FILTER_OPERATOR_IS_ALL,
        GalaxyFilterOperator.FILTER_OPERATOR_IS_NOT_ALL,
      ].includes(operator)
    ) {
      switch (this.filterDefinition.type) {
        case GalaxyFilterType.FILTER_TYPE_TAG:
          return FilterInputType.FILTER_INPUT_TYPE_MULTI_SELECT_TAG;
        case GalaxyFilterType.FILTER_TYPE_STRING:
          return FilterInputType.FILTER_INPUT_TYPE_MULTI_SELECT_STRING;
      }
    }
    if (
      [GalaxyFilterOperator.FILTER_OPERATOR_IS_VALID, GalaxyFilterOperator.FILTER_OPERATOR_IS_NOT_VALID].includes(
        operator,
      )
    ) {
      switch (this.filterDefinition.type) {
        case GalaxyFilterType.FILTER_TYPE_STRING:
          return FilterInputType.FILTER_INPUT_TYPE_SINGLE_SELECT_STRING;
        case GalaxyFilterType.FILTER_TYPE_PHONE:
          return FilterInputType.FILTER_INPUT_TYPE_NO_INPUT;
      }
    }
    return FilterInputType.FILTER_INPUT_TYPE_NO_OPERATOR_SELECTED;
  }
}

export interface OperatorOption {
  operator: GalaxyFilterOperator;
  label: string;
}
