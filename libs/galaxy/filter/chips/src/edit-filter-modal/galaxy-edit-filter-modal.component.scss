@use '../../../../styles/design-tokens' as dt;

:host {
  display: block;
  max-width: 100%;
  padding: dt.$spacing-3 dt.$spacing-3 dt.$spacing-2;
}

.filter-editor-container {
  display: flex;
  flex-wrap: wrap;
  column-gap: dt.$spacing-2;

  ::ng-deep glxy-form-field {
    margin-bottom: dt.$spacing-2;
    min-width: 180px;
  }
}

.field-row {
  column-gap: dt.$spacing-2;
}

button.row-button {
  margin-bottom: dt.$spacing-2;
}

.filter-row-label {
  @include dt.text-preset-4--bold;
  color: dt.$primary-text-color;
  margin-bottom: dt.$spacing-1;
}

.field-col {
  width: 100%;

  &:empty {
    display: none;
  }
}

glxy-date-filter-input.field-col {
  flex: 2;
  // min-width of date input
  min-width: 220px;
}

.operator-label {
  padding: dt.$spacing-1 dt.$spacing-2;
}
