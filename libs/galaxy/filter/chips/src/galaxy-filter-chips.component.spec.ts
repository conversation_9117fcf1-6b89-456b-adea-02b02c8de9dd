import { HttpClientTestingModule } from '@angular/common/http/testing';
import { LexiconModule } from '@galaxy/lexicon';
import { Spectator, byTestId, byText, createComponentFactory } from '@ngneat/spectator/jest';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyFilterChipsComponent } from './galaxy-filter-chips.component';

import { Observable, of } from 'rxjs';
import { GalaxyFilterOperator, GalaxyFilterType } from './enums';
import {
  GalaxyFilterChipDependencies,
  GalaxyFilterChipInjectionToken,
  GalaxyFilterDefinitionInterface,
  GalaxyFilterInterface,
} from './interface';

class MockDependencyService implements GalaxyFilterChipDependencies {
  mockFilters: GalaxyFilterInterface[] = [];
  mockDefinitions: GalaxyFilterDefinitionInterface[] = [];

  getFieldOptions(): Observable<string[]> {
    return of([]);
  }
  listObjectFilters(): Observable<GalaxyFilterDefinitionInterface[]> {
    return of(this.mockDefinitions) as Observable<GalaxyFilterDefinitionInterface[]>;
  }

  reset(): void {
    this.mockFilters = [];
  }
}

describe('GalaxyFilterChipsComponent', () => {
  const mockDependencyService = new MockDependencyService();

  function setup(): ReturnType<typeof createComponentFactory<GalaxyFilterChipsComponent>> {
    return createComponentFactory({
      component: GalaxyFilterChipsComponent,
      imports: [
        TranslateModule.forRoot(),
        LexiconModule.forRoot({
          disableOtw: true,
        }),
        HttpClientTestingModule,
      ],
      providers: [{ provide: GalaxyFilterChipInjectionToken, useValue: mockDependencyService }],
    });
  }

  describe('simple test', () => {
    beforeEach(() => {
      mockDependencyService.reset();
      mockDependencyService.mockFilters = [mockFilter];
      mockDependencyService.mockDefinitions = mockDefinitions;
    });

    let spectator: Spectator<GalaxyFilterChipsComponent>;
    const mockFilter = filterFixture();
    const mockDefinitions = [
      {
        fieldId: 'field_1',
        type: GalaxyFilterType.FILTER_TYPE_STRING,
        hasFilterOptions: true,
        supportedOperators: [GalaxyFilterOperator.FILTER_OPERATOR_IS],
        fieldName: 'field 1',
      },
    ];
    const createComponent = setup();

    it('should create component and render add filter button', async () => {
      spectator = createComponent();
      const addFilterButton = spectator.query(byTestId('add-filter-button'));
      expect(addFilterButton).toBeTruthy();
    });

    it('should open the filter when add filter button is clicked', () => {
      spectator = createComponent();
      expect(spectator.query(byTestId('search-input'), { root: true })).toBeFalsy();
      expect(spectator.query(byTestId(`filter-option-${mockFilter.fieldId}`), { root: true })).toBeFalsy();
      spectator.click(byTestId('add-filter-button'));
      expect(spectator.query(byTestId(`filter-option-${mockFilter.fieldId}`), { root: true })).toBeTruthy();
    });

    it('should move to edit once a field is clicked', () => {
      spectator = createComponent();
      expect(spectator.query(byTestId(`edit-filter-${mockFilter.fieldId}`), { root: true })).toBeFalsy();
      expect(spectator.query(byTestId('filter-apply-button'), { root: true })).toBeFalsy();
      spectator.click(byTestId('add-filter-button'));
      spectator.click(byTestId(`filter-option-${mockFilter.fieldId}`));
      expect(spectator.query(byTestId(`edit-filter-${mockFilter.fieldId}`), { root: true })).toBeTruthy();
      expect(spectator.query(byTestId('filter-apply-button'), { root: true })).toBeTruthy();
    });

    it('should filter down to the selected row for a string value given', async () => {
      spectator = createComponent();
      expect(spectator.query(byTestId(`chip-value-${mockFilter.fieldId}`))).toBeFalsy();
      expect(spectator.query(byText('field 1'))).toBeFalsy();
      spectator.click(byTestId('add-filter-button'));
      spectator.click(byTestId(`filter-option-${mockFilter.fieldId}`));
      spectator.typeInElement('This is my filter value', byTestId('filter-text-input'));
      spectator.typeInElement('This is my filter value', byTestId('filter-text-input'));
      spectator.click(byTestId('filter-apply-button'));
      expect(spectator.query(byTestId(`filter-option-${mockFilter.fieldId}`))).toBeFalsy();
      expect(spectator.query(byTestId(`chip-value-${mockFilter.fieldId}`))).toBeTruthy();
      expect(spectator.query(byText('This is my filter value'))).toBeTruthy();
    });
  });
});

function filterFixture(overrides?: {
  body?: Omit<Partial<GalaxyFilterInterface>, 'definition'>;
  definition?: Partial<GalaxyFilterDefinitionInterface>;
}): GalaxyFilterInterface {
  return {
    fieldId: 'field_1',
    filterId: 'filter_2',
    ...(overrides?.body || {}),
  } as GalaxyFilterInterface;
}
