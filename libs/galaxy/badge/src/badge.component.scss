@use 'design-tokens' as *;

:host {
  // 24px tall
  display: inline-block;
  vertical-align: middle;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  line-height: 1;
  white-space: nowrap;
  color: $dark-grey;
  background-color: $light-grey;
  border-radius: 12px;

  &.glxy-badge--small {
    // 16px tall
    padding: 0 $spacing-1;
    line-height: 16px;
    min-width: 16px;
    font-size: 11px;
    font-weight: 500;
    text-align: center;
  }

  &[class*='solid'] {
    color: $white;
  }

  &.glxy-badge--red {
    color: $dark-red;
    background-color: $light-red;
  }

  &.glxy-badge--red-solid {
    background-color: $red;
  }

  &.glxy-badge--yellow {
    // text color is material brown 600
    color: #6d4c41;
    background-color: $light-yellow;
  }

  &.glxy-badge--yellow-solid {
    background-color: $yellow;
  }

  &.glxy-badge--green {
    color: $dark-green;
    background-color: $light-green;
  }

  &.glxy-badge--green-solid {
    background-color: $green;
  }

  &.glxy-badge--blue {
    color: $dark-blue;
    background-color: $light-blue;
  }

  &.glxy-badge--blue-solid {
    background-color: $blue;
  }

  &.glxy-badge--light-grey {
    color: $grey;
    background-color: $lighter-grey;
  }

  &.glxy-badge--grey {
    color: $dark-grey;
    background-color: $light-grey;
  }

  &.glxy-badge--grey-solid {
    background-color: $grey;
  }

  &.glxy-badge--dark-grey-solid {
    background-color: $dark-grey;
  }

  &.glxy-badge--black-solid {
    background-color: $darker-grey;
  }

  &.glxy-badge--purple {
    color: #5e35b1;
    background-color: #f3e5f5;
  }
}
