@use 'design-tokens' as *;
@import '../../styles/snapshot-colors';

:host {
  // this fixes a vertical alignment issue, not sure why
  font-size: 0;
  vertical-align: text-bottom;
}

.grade-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

svg {
  // default styling
  fill: $border-color;
  color: $border-color;
}

.A,
.B {
  fill: $grade-positive-color;
  color: $grade-positive-color;
}

.C,
.D {
  fill: $grade-neutral-color;
  color: $grade-neutral-color;
}

.F {
  fill: $grade-negative-color;
  color: $grade-negative-color;
}

.large {
  width: 66px;
  height: 66px;
  font-size: 44px;
  font-weight: 700;
}

.medium {
  width: 50px;
  height: 50px;
  font-size: 25px;
  font-weight: 700;
}

.small {
  width: 36px;
  height: 36px;
  font-size: 20px;
  font-weight: 700;
}

.extra-small {
  width: 28px;
  height: 28px;
  font-size: 12px;
  font-weight: 700;
}
