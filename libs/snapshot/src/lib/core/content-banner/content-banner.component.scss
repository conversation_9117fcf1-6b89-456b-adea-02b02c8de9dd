@use 'design-tokens' as *;
@import '../../styles/snapshot-colors';

.banner-heading {
  background-color: $link-hover-color;
  padding: 0 $spacing-3;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: baseline;

  mat-icon {
    font-size: $font-preset-3-size;
    vertical-align: sub;
  }

  .mat-mdc-button {
    width: auto;
  }
}

.banner {
  background-color: $banner-background-color;
  padding: $spacing-4;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: $spacing-4;

  .message {
    line-height: 1.25em;
    color: $secondary-text-color;
    white-space: pre-line;
  }

  mat-form-field {
    width: 100%;
    min-height: 130px;
  }
}

.edit-action-button {
  color: $contrast-text-color;
}
