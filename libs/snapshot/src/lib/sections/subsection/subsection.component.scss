@use 'design-tokens' as *;
@import '../common/colors';

.subsection-container {
  flex-direction: column;
  box-sizing: border-box;
  display: flex;
}

.subsection-header {
  min-height: 51px;
  background-color: $subsection-header-background-color;
  padding: 12px $spacing-3;
  flex-direction: row;
  box-sizing: border-box;
  display: flex;
  place-content: center flex-start;
  align-items: center;

  .heading {
    font-weight: bold;
    font-size: $font-preset-4-size;
    padding-left: $spacing-2;
  }

  .tooltip {
    padding-top: $spacing-1;
  }

  .subheading {
    flex: auto;
    text-align: end;
    font-size: $font-preset-4-size;
  }

  .edit-subsection {
    padding-left: $spacing-2;
  }
}

.subsection-body {
  overflow: hidden;
}

.subsection {
  flex-direction: row;
  box-sizing: border-box;
  display: flex;
  place-content: center flex-start;
  align-items: center;
  margin-right: $spacing-3;
}
