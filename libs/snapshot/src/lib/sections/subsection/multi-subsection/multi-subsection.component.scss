@use 'design-tokens' as *;
@import '../../common/colors';
@import 'utilities';

.multi-subsection-container {
  flex-direction: column;
  box-sizing: border-box;
  display: flex;
}

.multi-subsection-header {
  min-height: 51px;
  background-color: $subsection-header-background-color;
  padding: 12px $spacing-3;
  flex-direction: column;
  box-sizing: border-box;
  display: flex;
  place-content: flex-start;
  align-items: flex-start;

  @include tablet-large {
    flex-direction: row;
    place-content: center flex-start;
    align-items: center;
  }

  .subsection {
    flex-direction: row;
    box-sizing: border-box;
    display: flex;
    place-content: center flex-start;
    align-items: center;
    margin-right: $spacing-3;
    padding-top: $spacing-1;
    padding-bottom: $spacing-1;

    @include tablet-large {
      padding-top: 0;
      padding-bottom: 0;
    }

    .heading {
      font-weight: bold;
      font-size: $font-preset-4-size;
      padding-left: $spacing-2;
    }

    .edit-subsection {
      padding-left: $spacing-2;
    }
  }

  .subheading {
    flex: auto;
    text-align: end;
  }
}

.multi-subsection-body {
  overflow: hidden;
}
