@use 'design-tokens' as *;
@import '../common/colors';

.heading {
  background-color: $link-hover-color;
  padding: 0 16px;

  mat-icon {
    font-size: 16px;
    vertical-align: sub;
  }

  .mat-mdc-button {
    width: auto;
  }
}

.full-width {
  width: 100%;
}

.section-footer {
  padding: $spacing-3;
  gap: 20px;

  a {
    cursor: pointer;
  }

  .salesperson-image {
    height: 40px;
    border-radius: 20px;
  }

  .section-footer-display {
    width: 100%;
    gap: 15px;
  }

  .section-footer-message,
  .section-footer-speech {
    line-height: 1.25em;
    font-size: 1em;
    color: $primary-font-color;
    white-space: pre-wrap;
    position: relative;
    padding: 10px;
    background-color: $banner-background-color;
    border-radius: 10px;
  }

  .section-footer-speech:before {
    content: '\0020';
    display: block;
    position: absolute;
    left: -10px;
    top: 10px;
    z-index: 2;
    width: 0;
    height: 0;
    overflow: hidden;
    border: solid 10px transparent;
    border-left: 0;
    border-right-color: $banner-background-color;
  }

  button {
    min-width: 120px;
    max-height: 36px;
  }
}

.cta {
  align-items: center;
}

.edit-cta {
  color: $section-secondary-color;
}

.contact-button {
  background-color: $section-secondary-color;
  color: $contrast-text-color;
}

.edit-action-button {
  color: $contrast-text-color;
}

.edit-actions-container {
  gap: $spacing-4;
}

.section-footer-inner-container {
  gap: 20px;
}
