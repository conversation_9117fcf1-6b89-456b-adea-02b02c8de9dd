@use 'design-tokens' as *;
@import '../common/colors';
@import 'utilities';

.header {
  font-size: $font-preset-3-size;
  color: $primary-text-color;
}

.nap {
  display: flex;
  flex-direction: column;
  align-content: flex-start;

  & > * {
    margin-bottom: $spacing-2;
  }

  .taxonomy {
    margin-bottom: $spacing-3;
    margin-top: $spacing-3;
  }

  .company-name,
  .address,
  .city-state,
  .zip,
  .phone,
  .website,
  .taxonomy {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
  }
  .website {
    word-break: break-all;
  }

  .label {
    @include text-preset-5;
    color: $tertiary-text-color;
  }

  .value {
    @include text-preset-4;
    width: 100%;

    .nap-field-missing {
      font-style: italic;
      color: $grade-negative-color;
    }
  }
}

.business-images {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  width: 100%;
  @include tablet-large {
    flex-direction: row;
  }

  > * {
    display: flex;
    justify-content: center;
  }

  img {
    width: 100%;
    max-width: 130px;
    object-fit: contain;
    margin: $spacing-2;
  }
}

.more-info-tooltip {
  font-size: $font-preset-5-size;
  color: $tertiary-font-color;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  align-items: center;

  > *:not(:last-child) {
    margin-right: $spacing-2;
  }

  mat-icon {
    font-size: 24px;
  }
}
