@import '../../common/colors';

:host {
  width: 100%;
}

.heading {
  height: 50px;
  font-size: $font-preset-4-size;
  font-weight: bold;
  color: $primary-font-color;
  background-color: $subsection-header-background-color;
  padding-left: 10px;
  gap: 10px;
}

.rule {
  height: 40px;
  background-color: $subsection-header-background-color;

  mat-icon {
    text-align: center;
    width: 70px;
  }

  div {
    height: 100%;
    width: 100%;
    color: $contrast-text-color;
    padding-left: 10px;
    font-weight: bold;
  }
}

a,
a:hover {
  color: $contrast-text-color;
}

.should-fix,
.below-average {
  mat-icon {
    color: $grade-negative-color;
  }
  div {
    background-color: $grade-negative-color;
  }
}

.consider-fixing {
  mat-icon {
    color: $grade-neutral-color;
  }
  div {
    background-color: $grade-neutral-color;
  }
}

.passed-rules,
.above-average {
  mat-icon {
    color: $grade-positive-color;
  }
  div {
    background-color: $grade-positive-color;
  }
}

.grade-table {
  gap: 5px;
}
