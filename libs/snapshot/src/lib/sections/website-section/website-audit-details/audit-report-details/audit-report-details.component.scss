@use 'design-tokens' as *;
@import '../../../common/colors';

mat-panel-title {
  max-width: 100%;
}

mat-icon {
  padding-right: $spacing-2;
}

.description {
  span {
    @include text-preset-4;
  }
}

.details-container {
  overflow: hidden;

  .details {
    padding: $spacing-2;
    background-color: $primary-background-color;
    word-break: break-all;
    @include text-preset-5;
    span {
      @include text-preset-4--bold;
    }
  }
}

.check_circle {
  color: $grade-positive-color;
}

.error {
  color: $grade-neutral-color;
}

.cancel {
  color: $grade-negative-color;
}

.detail-button {
  margin-top: $spacing-2;
}

.title {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

@supports (display: -webkit-box) {
  // change the title to truncate after 2 lines if supported
  .title {
    display: -webkit-box;
    white-space: break-spaces;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}
