@import '../../common/colors';

a {
  cursor: pointer;
}

h4 {
  margin: 0 0 $spacing-2;
}

.rule {
  padding: $spacing-2 0;
  gap: $spacing-3;

  .details-container {
    overflow: hidden;

    .details {
      padding: $spacing-2;
      background-color: $primary-background-color;
      word-break: break-all;
      gap: $spacing-2;
    }
  }

  .name {
    font-weight: bold;
    margin-bottom: $spacing-2;
  }
}

.error {
  color: $grade-negative-color;
}

.warning {
  color: $grade-neutral-color;
}

.check_circle {
  color: $grade-positive-color;
}

.rule-icon {
  flex-shrink: 0;
}
