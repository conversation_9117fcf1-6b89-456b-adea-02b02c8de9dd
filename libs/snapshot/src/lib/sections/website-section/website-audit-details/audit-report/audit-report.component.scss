@use 'design-tokens' as *;
@import 'utilities';

.flat {
  box-shadow: none;
  border: 1px solid $border-color;

  @media print {
    height: 40px;
  }
}

mat-expansion-panel {
  mat-expansion-panel-header {
    height: auto;
    min-height: 48px;
    mat-panel-title {
      align-items: center;
      color: $primary-text-color;
    }
    mat-panel-description {
      justify-content: flex-end;
      align-items: center;
    }
  }
}

.status-summary-container {
  display: flex;
  justify-content: flex-end;
}

span {
  @include text-preset-4;
  padding-left: $spacing-1;
}

.rule-separator {
  border-top: 1px solid $primary-background-color;
}

.details-container {
  padding: $spacing-1 0;
}
