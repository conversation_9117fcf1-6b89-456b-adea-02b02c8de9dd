@use 'design-tokens' as *;
@import '../common/colors';
@import 'utilities';

a {
  cursor: pointer;
}

hr {
  color: $border-color;
  border: none;
  border-top: 1px solid;
}

.audit-details {
  margin-top: $spacing-4;
}

.subsection {
  padding: $spacing-3;

  .vitals-title {
    display: flex;
    justify-content: space-between;
    padding: $spacing-2 0;
    @include text-preset-3--bold;
    .help {
      cursor: pointer;
      color: $tertiary-text-color;

      display: block;
      @include tablet-large {
        display: none;
      }
    }
  }
  .vitals-description {
    padding-bottom: $spacing-4;
    @include text-preset-4;
  }
}

snap-toggled-item {
  width: 100%;
}

.preview-control-group {
  min-height: $spacing-4;
  align-items: center;
  .preview-control {
    align-items: center;
    display: flex;
    padding: 0 $spacing-3;
  }
}

.mobile-preview,
.desktop-preview {
  margin-top: 30px;
  position: relative;
  width: 100%;
  flex-direction: column;
  box-sizing: border-box;
  display: flex;
  place-content: center;
  align-items: center;
}

.preview-col {
  height: 300px;
}

@include tablet-large {
  .preview-col {
    height: 400px;
  }
}

.center-flex-content {
  display: flex;
  align-items: center;
  justify-content: center;
  snap-performance-card {
    width: 100%;
  }
}

.mobile-preview {
  background: url('../../../assets/images/mobile-frame.svg') no-repeat center/auto 365px;
  height: 365px;

  img {
    margin-top: -10px;
    margin-left: 1px; // center image in background frame
    max-width: 160px;
  }
}

.desktop-preview {
  height: 300px;
  background: url('../../../assets/images/desktop-frame.svg') no-repeat center/auto 275px;

  img {
    margin-top: -40px; // center image in background frame
    margin-left: 1px; // center image in background frame
    max-width: 290px;
  }
}

.toggle {
  position: absolute;
  top: -($spacing-4);
  right: $spacing-4;
}

.rules {
  margin-top: $spacing-3;

  .rule-separator {
    border-top: 1px dotted;
  }

  & > div {
    overflow: hidden;
  }
}

.check_circle {
  color: $grade-positive-color;
}

.error {
  color: $grade-negative-color;
}

.table-scroll-wrapper {
  overflow-x: auto;
}

.not-found {
  padding: $spacing-4;

  & > * {
    margin-bottom: $spacing-3;
  }

  p {
    white-space: pre-wrap;
  }
}

@media only screen and (max-width: 840px) {
  .mat-mdc-table .mat-mdc-row {
    padding: $spacing-1 12px;
  }
}

.form-field {
  display: flex;
  align-items: baseline;
  justify-content: center;
}

mat-error {
  text-align: left;
}

.website-url {
  padding-bottom: $spacing-3;
}

.submit-button {
  padding-bottom: $spacing-3;

  mat-icon {
    width: 20px;
    height: 20px;
    font-size: $font-preset-2-size;
  }

  a {
    margin-left: $spacing-2;
  }
}

.mat-mdc-outlined-button {
  min-width: 32px;
  padding: 0 $spacing-2;
  line-height: 30px;
}

.mobile-friendly {
  gap: 5px;
}

.subsection-inner-container {
  gap: 5px;
}

.summary-container {
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 100%;
  gap: 20px;
  max-width: 40%;
}

@media (max-width: $media--tablet-minimum) {
  .subsection-inner-container {
    flex-direction: column;
    place-content: stretch center;
    align-items: stretch;
  }

  .summary-container {
    max-width: 100%;
  }
}
