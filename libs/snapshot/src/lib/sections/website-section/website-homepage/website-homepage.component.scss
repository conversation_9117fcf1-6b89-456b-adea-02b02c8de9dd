@import '../../common/colors';
@import 'utilities';

.check_circle {
  color: $grade-positive-color;
}

.cancel {
  color: $grade-negative-color;
}

.error {
  color: $grade-negative-color;
}

.table-scroll-wrapper {
  overflow-x: auto;
}

.homepage-content {
  h3 {
    margin-left: $spacing-3;
    margin-bottom: 0;
  }

  .business-info {
    display: flex;
    flex-direction: column;
    padding: $spacing-3 0;

    @include tablet-large {
      flex-direction: row;
      width: 100%;
      padding: $spacing-5 $spacing-3;
    }
  }

  .business-info-card {
    display: flex;
    align-items: center;
    margin: $spacing-1 $spacing-3;
    padding: $spacing-2;
    background-color: $primary-background-color;
    box-shadow: none;
    font-size: $font-preset-4-size;
    border: none;

    @include tablet-large {
      width: 100%;
      margin: 0 $spacing-2;
      padding: $spacing-2;
      justify-content: center;
    }
    .icon {
      margin-right: $spacing-2;
    }
    .value {
      padding-left: $spacing-2;
    }
  }

  .last {
    border-bottom: none;
  }

  .title {
    display: flex;
    align-items: center;
    @include text-preset-4--bold;
    .icon {
      margin-right: 12px;
    }
  }

  .mat-mdc-header-cell {
    text-align: center;
    color: $primary-font-color;
    @include text-preset-5--bold;
  }

  .business,
  .industry {
    font-size: $font-preset-3-size;
    text-align: center;
  }
}

@media only screen and (max-width: 840px) {
  .mat-mdc-table .mat-mdc-row {
    padding: $spacing-1 12px;
  }
}
