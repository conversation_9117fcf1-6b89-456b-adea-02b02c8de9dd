@use 'utilities' as ut;
@use 'design-tokens' as *;
@import '../../common/colors';

.mobile-friendly {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  mat-icon {
    margin-right: $spacing-1;
  }
  .check_circle {
    color: $grade-positive-color;
  }
  .error {
    color: $grade-negative-color;
  }
}

.mobile-preview,
.laptop-preview {
  flex-direction: column;
  box-sizing: border-box;
  display: flex;
  place-content: center;
  align-items: center;
}

.mobile-preview {
  background: url('../../../../assets/images/mobile-frame.svg') no-repeat center/auto 250px;
  height: 250px;
  min-width: 125px;
  img {
    margin-top: -8px;
    max-width: 108px;
  }
}

.laptop-preview {
  height: 200px;
  background: url('../../../../assets/images/laptop-frame.png') no-repeat center/auto 200px;
  width: 315px;
  img {
    margin-top: -4.5%;
    margin-left: 0.63%;
    max-width: 62%;
  }
}

.mobile-overlap {
  position: absolute;
  top: 40%;
  right: -2%;
  .mobile-preview {
    background: url('../../../../assets/images/mobile-frame.svg') no-repeat center/auto 100%;
    height: 160px;
    min-width: 91px;
    width: 91px;
    img {
      margin-top: -3.5%;
      max-width: 72%;
    }
  }
}

@include ut.tablet-large {
  .mobile-preview {
    background: url('../../../../assets/images/mobile-frame.svg') no-repeat center/auto 300px;
    height: 300px;
    min-width: 150px;
    img {
      margin-top: -10px;
      max-width: 130px;
    }
  }

  .laptop-preview {
    height: 250px;
    background: url('../../../../assets/images/laptop-frame.png') no-repeat center/auto 250px;
    width: 394px;
    img {
      margin-top: -4.5%;
      margin-left: 0.63%;
      max-width: 62%;
    }
  }

  .mobile-overlap {
    position: absolute;
    top: 38%;
    right: -6%;
    .mobile-preview {
      background: url('../../../../assets/images/mobile-frame.svg') no-repeat center/auto 100%;
      height: 190px;
      min-width: 108px;
      width: 108px;
      img {
        margin-top: -3.5%;
        max-width: 72%;
      }
    }
  }
}

@include ut.desktop {
  .mobile-preview {
    background: url('../../../../assets/images/mobile-frame.svg') no-repeat center/auto 300px;
    height: 300px;
    min-width: 150px;
    img {
      margin-top: -10px;
      max-width: 130px;
    }
  }

  .laptop-preview {
    height: 317px;
    background: url('../../../../assets/images/laptop-frame.png') no-repeat center/auto 100%;
    width: 500px;
    img {
      margin-top: -4.5%;
      margin-left: 0.63%;
      max-width: 62%;
    }
  }

  .mobile-overlap {
    position: absolute;
    top: 33%;
    right: 3%;
    .mobile-preview {
      background: url('../../../../assets/images/mobile-frame.svg') no-repeat center/auto 100%;
      height: 240px;
      min-width: 137px;
      width: 137px;
      img {
        margin-top: -3.5%;
        max-width: 72%;
      }
    }
  }
}
