@use 'design-tokens' as *;
@import '../common/colors';

.chart-summary {
  div {
    color: $grade-positive-color;
    font-size: $font-preset-2-size;
  }

  h2 {
    margin: $spacing-1;
    color: $primary-font-color;
    font-size: $font-preset-4-size;
  }

  span {
    color: $tertiary-text-color;
    font-size: $font-preset-5-size;
  }
}

.chart {
  height: 245px;
  overflow-y: hidden;

  &.small {
    height: auto;
  }
}

.adwords-wrapper {
  color: $secondary-font-color;
  padding: 20px;
}

.adwords-footer {
  color: $tertiary-font-color;
  font-size: $font-preset-5-size;
}

.adwords-chart {
  flex-basis: 70%;
}

@media (max-width: $media--tablet-minimum) {
  .blurb {
    padding-bottom: 10px;
  }

  .adwords-impressions {
    flex-direction: column !important;
  }

  .adwords-clicks {
    flex-direction: column !important;
  }

  .adwords-chart {
    flex-basis: 100%;
    height: auto;
  }
}
