@use 'design-tokens' as *;
@import '../styles/snapshot-colors';
@import './flex-layout';

/* these styles are in the global sheet because they need to apply to the innerHtml binding of the call-to-action */

/* IMPORTANT: make sure to scope all styles in this file under snapshot-specific components/class-names to avoid conflicting with other styles */

snapshot-subsection-overall-score,
snapshot-subsection-business-details,
snapshot-subsection-organic-keywords-performance,
snapshot-subsection-organic-keywords-ranking,
snapshot-subsection-local-seo,
snapshot-subsection-listing-accuracy,
snapshot-subsection-listing-details,
snapshot-subsection-listing-presence,
snapshot-subsection-listing-provider,
snapshot-subsection-website-homepage,
snapshot-subsection-website-performance,
snapshot-subsection-ecommerce,
snapshot-subsection-facebook,
snapshot-subsection-instagram,
snapshot-subsection-twitter,
snapshot-subsection-advertising-adwords,
snapshot-subsection-advertising-retargeting,
snapshot-subsection-campaign-performance,
snapshot-subsection-review,
snapshot-section-header-social,
snapshot-section-header-website,
snapshot-section-header-seo,
snapshot-section-header-review,
snapshot-section-header-listing,
snapshot-section-header-ecommerce,
snapshot-section-header-advertising,
.snapshot-mat-table-overrides {
  .mat-table {
    display: block;

    .mat-row,
    .mat-header-row {
      display: flex;
      border-bottom: 1px $contrast-text-color;
      align-items: center;
      min-height: 48px;
      padding: 0 24px;

      &.mat-header-row {
        border-bottom-style: solid;
      }

      &.mat-row {
        border-bottom-style: solid;

        &.last {
          border-bottom: none;
        }
      }
    }

    .mat-cell,
    .mat-header-cell {
      overflow: visible;
      flex: 1;
      word-wrap: break-word;
    }

    .mat-header-cell,
    .heading {
      @include text-preset-4--bold;
    }
  }
}

@media screen and (max-width: 840px) {
  .hide-mobile {
    display: none;
  }
}
