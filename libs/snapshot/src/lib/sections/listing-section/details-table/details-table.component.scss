@use 'design-tokens' as *;
@import '../../common/colors';

.mat-mdc-cell,
.mat-mdc-header-cell {
  color: $primary-font-color;
}

.mat-mdc-header-cell {
  @include text-preset-5--bold;
}

.mat-column-status {
  max-width: 100px;
}

.source {
  display: flex;
  flex-wrap: wrap;
  padding-top: $spacing-2;
  padding-bottom: $spacing-2;
}

.source-name {
  font-weight: bold;
}
.source-icon {
  margin-right: $spacing-2;
}

.status {
  justify-content: flex-end;
}

.simplified-listing {
  padding: $spacing-1 0;
  span {
    max-width: 144px;
  }
}

.not-found {
  color: $grade-negative-color;
}

.accurate {
  color: $grade-positive-color;
}

.inaccurate {
  color: $grade-neutral-color;
}

.working {
  color: $tertiary-text-color;
  font-style: italic;
}

.ignored {
  color: $tertiary-text-color;
}

.view-more {
  display: flex;
  justify-content: center;
  padding: $spacing-2;
  margin-top: $spacing-2;
  margin-bottom: $spacing-2;
}

.mat-mdc-table .mat-mdc-row.mat-row:last-child,
.mat-table .mat-mdc-header-row.mat-row:last-child {
  border-bottom-style: none;
}

.truncate {
  max-width: 150px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.view-more-label {
  display: flex;
  align-items: center;
  gap: $spacing-1;
}

// Class name is auto-generated, based on cell name
// https://material.angular.io/components/table/overview#table-column-styling
.mat-column-edit {
  max-width: 64px;
}
