@import '../../common/colors';
@import 'hide-sizes';

@media only screen and (max-width: 840px) {
  .mat-mdc-row {
    padding: $spacing-1 12px;
  }
  .name {
    flex-direction: column;
    gap: 0;
  }
  .example-data {
    flex-direction: column;
    gap: 0;
    place-content: center flex-start;
    align-items: center;
  }
}

.mat-mdc-header-cell {
  color: $primary-font-color;
  @include text-preset-5--bold;
}

.name {
  min-width: 110px;
  max-width: 240px;
  font-weight: bold;
  font-size: $font-preset-4-size;
  gap: 5px;

  i {
    color: $link-color;
  }
}

.business {
  min-width: 135px;
}

.toggle {
  width: 40px;
  max-width: 40px;
}

.foundCount,
.industryAverageFound {
  font-size: $font-preset-3-size;
}

.industryAverageFound {
  text-align: right;
  padding-right: $spacing-1;
}

.industryAverageHeader {
  text-align: right;
}

.example-data {
  font-weight: normal;
  font-size: $font-preset-4-size;
  gap: 50px;

  .example-data-value {
    color: $grade-negative-color;
    width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.example-data {
  font-size: $font-preset-4-size;
}

.negative-grade {
  color: $grade-negative-color;
}

.neutral-grade {
  color: $grade-neutral-color;
}

.positive-grade {
  color: $grade-positive-color;
}
