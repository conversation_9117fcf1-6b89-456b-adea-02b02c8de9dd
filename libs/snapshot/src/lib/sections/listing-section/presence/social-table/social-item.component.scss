@import '../../../common/colors';

:host {
  display: block;
}

.image {
  margin: auto;
  width: 50px;
  height: 50px;
  position: relative;
  overflow: hidden;
  border-radius: 50%;
}

.found-online {
  color: $tertiary-text-color;
  @include text-preset-5;
}

.center {
  text-align: center;

  .bold {
    @include text-preset-4--bold;
    padding: $spacing-1 0;
  }
}

.found {
  @include text-preset-4;
  color: $grade-positive-color;
}
.not-found {
  @include text-preset-4;
  color: $grade-negative-color;
}
.inconclusive {
  @include text-preset-4;
  color: $grade-neutral-color;
  flex-direction: row;
  display: flex;
  place-content: center;
  align-items: center;

  span {
    margin-right: $spacing-1;
  }
  mat-icon {
    font-size: $font-preset-3-size;
    align-items: center;
    display: flex;
  }
}

.listing {
  flex-grow: 1;
}

.listing-box {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
