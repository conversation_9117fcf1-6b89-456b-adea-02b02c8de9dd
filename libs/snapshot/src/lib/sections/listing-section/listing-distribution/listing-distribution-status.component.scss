@import '../../common/colors';
@import 'utilities';

.status {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  @include tablet-large {
    flex-direction: column;
  }

  .provider {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 $spacing-1;
    width: 100%;
    background-color: $contrast-text-color;
    @include tablet-large {
      justify-content: center;
    }

    img {
      height: 40px;
    }
  }

  .status-text {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: $spacing-2;
    @include tablet-large {
      justify-content: center;
    }
  }
  .neustar {
    width: 160px;
    object-fit: contain;
  }
  .foursquare {
    width: 160px;
    object-fit: contain;
  }
}

.accurate {
  color: $grade-positive-color;
  mat-icon {
    margin-right: $spacing-1;
    color: $grade-positive-color;
  }
}

.contains_errors {
  color: $grade-neutral-color;
  mat-icon {
    margin-right: $spacing-1;
    color: $grade-neutral-color;
  }
}

.not_found {
  color: $grade-negative-color;
  mat-icon {
    margin-right: $spacing-1;
    color: $grade-negative-color;
  }
}
