@import '../common/colors';
@import 'utilities';

.mat-mdc-table {
  @media only screen and (max-width: 800px) {
    .mat-mdc-row {
      padding: $spacing-1 12px;
    }
  }

  .category {
    @include text-preset-4--bold;
    width: 30%;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-left: $spacing-2;

    @media only screen and (max-width: 800px) {
      font-size: $font-preset-4-size;
    }

    .icon {
      margin-right: $spacing-2;
    }
  }

  .found-header {
    width: 20%;
    color: $primary-font-color;
    @include text-preset-5--bold;
  }

  .found {
    width: 20%;
    font-size: $font-preset-4-size;
  }

  .not-found {
    width: 20%;
    color: $secondary-font-color;
    font-size: $font-preset-4-size;
  }

  .more {
    font-size: $font-preset-5-size;
    color: $secondary-font-color;
  }

  .attribute {
    min-width: 50px;
    max-width: 240px;
  }

  .recommendation-header {
    @include text-preset-5--bold;
    color: $primary-font-color;
    width: 45%;
  }

  .recommendation {
    @include text-preset-4;
    color: $secondary-font-color;
    width: 45%;
    .recommendation-value {
      &.IncorrectWebsite {
        word-break: break-all;
      }
    }
  }

  .toggle {
    width: 5%;
  }

  .last {
    border-bottom: none;
  }
}

.green-icon {
  color: $grade-positive-color;
}

.red-icon {
  color: $grade-negative-color;
}

.no-website {
  text-align: center;
  padding: $spacing-4;

  & > * {
    margin-bottom: $spacing-3;
  }

  p {
    white-space: pre-wrap;
  }
}

.data-not-found {
  padding: $spacing-4;

  & > * {
    margin-bottom: $spacing-3;
  }

  p {
    white-space: pre-wrap;
  }
}

.business-header {
  width: 30%;
  @include text-preset-5--bold;
  color: $primary-font-color;
}

.competitors-header {
  @include text-preset-5--bold;
  color: $primary-font-color;
  width: 15%;
  text-align: center;
}

.competitor {
  @include text-preset-4;
  color: $secondary-font-color;
  width: 15%;
  text-align: center;
}

.business-found {
  width: 30%;
  font-size: $font-preset-4-size;
}

.business-not-found {
  width: 30%;
  color: $secondary-font-color;
  font-size: $font-preset-4-size;
}

.business {
  display: flex;
  align-items: center;
  .icon {
    padding-right: $spacing-2;
  }
  .solution {
    display: flex;
    flex-direction: column;
  }
}

.mobile-data-container {
  display: flex;
  flex-direction: column;
  padding: $spacing-3;
}

.title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: $spacing-2;
}

.text-and-grades {
  display: flex;
  align-items: center;
  gap: $spacing-2;
}

.title-text {
  font-weight: bold;
  align-items: center;
  color: $primary-font-color;
}
