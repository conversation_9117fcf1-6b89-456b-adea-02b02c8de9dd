@use 'design-tokens' as *;
@import '../../common/colors';
@import url('https://www.cdnstyles.com/static/css/thermometer.css');

.positive {
  background-color: $grade-positive-color;
}
.neutral {
  background-color: $grade-neutral-color;
}
.negative {
  background-color: $grade-negative-color;
}
.default {
  background-color: $field-border-color;
}

.thermometer {
  margin: $spacing-4 auto $spacing-3 auto !important;

  .marker {
    background: transparent;
    min-width: fit-content;
  }
  .marker:before {
    position: absolute;
    content: '';
    width: 1px;
    height: 10px;
    bottom: -5px;
    right: 0;
    background-color: $tertiary-text-color;
  }

  .marker-top {
    top: -15px;
  }
  .marker-top:before {
    top: 35px;
  }
  .marker-top:after {
    display: none;
  }

  .marker-bottom {
    bottom: 11px;
  }
  .marker-bottom:before {
    top: -15px;
  }
  .marker-bottom:after {
    display: none;
  }

  .marker-label {
    @include text-preset-5;
    display: block;
    white-space: normal;
  }
  .marker-value {
    @include text-preset-4;
  }

  .section {
    margin-top: 30px;
    margin-bottom: 30px;
    position: absolute;
    line-height: 24px;
    border-left: 1px solid;
    text-align: center;
    color: $contrast-text-color;
  }
  .section-label {
    font-size: $font-preset-6-size;
    font-weight: 500;
    .label {
      max-width: 40px;
      max-height: 24px;
      display: inline-flex;
      overflow: hidden;
    }
  }
  .section:first-child {
    border-radius: $spacing-4 0 0 $spacing-4;
  }
  .section-last {
    border-radius: 0 $spacing-4 $spacing-4 0;
  }
  .section-first {
    width: 33.3%;
    left: 0;
  }
  .section-second {
    width: 33.3%;
    left: 33.3%;
  }
  .section-third {
    width: 33.3%;
    left: 66.6%;
  }
}
