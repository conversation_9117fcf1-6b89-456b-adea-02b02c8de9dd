@use 'design-tokens' as *;
@import 'utilities';
@import '../../common/colors';

mat-icon {
  font-size: $font-preset-4-size;
  height: $font-preset-4-size;
  width: $font-preset-4-size;
}

.business-review-rating,
.business-review-count,
.claim-status,
.business-address,
.business-phone {
  @include text-preset-5;
  display: inline-flex;
}

.claim-icon {
  font-size: $font-preset-5-size;
}
.positive {
  color: $grade-positive-color;
}
.negative {
  color: $grade-negative-color;
}
.rating {
  color: $grade-neutral-color;
}

.local-seo-container {
  padding: $spacing-3;
}

.ranking-container {
  padding: $spacing-3;
  display: flex;
  flex-direction: column;
}

.description {
  margin-bottom: $spacing-2;
}

.business {
  display: flex;
  align-items: center;
}

.tab-container {
  padding: $spacing-3;
  min-height: 450px;
}

mat-divider {
  margin-left: $spacing-3;
}

.mat-divider-horizontal {
  width: 90%;
}

.business-item {
  padding: $spacing-3;
}

.local-seo-map {
  height: 350px;
}

.last-business {
  border-top-style: dashed;
}

.business-chip {
  background: $error-background-color;
  color: $grade-negative-color;
  padding: $spacing-1 $spacing-2;
  border-radius: 40%;
  font-size: $font-preset-5-size;
  font-weight: 500;
  max-width: min-content;
}

.rank-chip {
  background: $error-background-color;
  color: $grade-negative-color;
  padding: $spacing-1 $spacing-2;
  border-radius: 50%;
  font-size: $font-preset-5-size;
  font-weight: 500;
  max-width: min-content;
  margin-left: $spacing-1;
}

.top-performer {
  background: $success-background-color;
  color: $grade-positive-color;
}

.average-performer {
  background: $warn-background-color;
  color: $grade-neutral-color;
}

.business-not-found {
  font-size: $font-preset-5-size;
  color: $grade-negative-color;
  padding: $spacing-3;
}

.empty-state-container {
  padding: $spacing-3;
}

.map-info-text {
  padding: $spacing-3;
  background-color: $banner-background-color;
  @include text-preset-4;
  color: $secondary-font-color;
  display: flex;
  align-items: center;

  mat-icon {
    margin-right: $spacing-1;
    font-size: $font-preset-2-size;
    height: 20px;
    width: 20px;
  }
}

.table-row {
  align-items: center;
}

.divider {
  color: $primary-text-color;
  padding: 0 $spacing-1;
}

.disabled-link {
  pointer-events: none;
  color: $primary-text-color;
}

.no-results-container {
  display: flex;
  align-items: center;
  flex-grow: 1;
}
