@use 'design-tokens' as *;
@import 'hide-sizes';

.not-found {
  padding: $spacing-4;

  & > * {
    margin-bottom: $spacing-3;
  }
}

.mat-mdc-table {
  &.mat-mdc-row {
    &.first {
      background-color: $field-background-disabled-color;
    }
  }

  .mat-mdc-header-cell {
    color: $primary-font-color;
    @include text-preset-5--bold;

    .mat-icon {
      font-size: $font-preset-4-size;
      height: 15px;
      width: 15px;
      color: $tertiary-text-color;
    }
  }

  .mat-mdc-cell {
    @media only screen and (min-width: 840px) {
      font-size: $font-preset-3-size;
    }
  }

  .progress {
    @media print {
      position: unset !important;
    }
  }

  .keyword {
    font-size: $font-preset-4-size;
  }

  .domain-name a {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

:host ::ng-deep .glxy-empty-state--small .glxy-empty-state__content {
  max-width: 560px;
}

.title {
  gap: 2px;
}

.width-15 {
  max-width: 15%;
}

.width-20 {
  max-width: 20%;
}

.width-30 {
  max-width: 30%;
}
