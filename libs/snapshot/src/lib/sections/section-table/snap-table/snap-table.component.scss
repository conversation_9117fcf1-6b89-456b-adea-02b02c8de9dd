@use 'design-tokens' as *;
@import '../../common/colors';

.red-icon {
  color: $grade-negative-color;
}

.green-icon {
  color: $grade-positive-color;
}

.not-found {
  color: $grade-negative-color;
  font-size: $font-preset-4-size;
}

.examples {
  font-size: $font-preset-5-size;
  color: $tertiary-font-color;
}

.A,
.B {
  color: $grade-positive-color;
}

.C,
.D {
  color: $grade-neutral-color;
}

.F {
  color: $grade-negative-color;
}

.mat-mdc-table {
  .mat-mdc-cell {
    @include text-preset-4;
    text-align: center;
  }

  .mat-mdc-header-cell {
    color: $primary-font-color;
    @include text-preset-4--bold;
    text-align: center;
    background-color: $card-background-color;
  }

  .mat-column-rowLabel {
    text-align: left;
    @include text-preset-4--bold;
  }
}

.row-labels {
  display: inline-flex;
  text-align: left;
  flex-wrap: wrap;
}
