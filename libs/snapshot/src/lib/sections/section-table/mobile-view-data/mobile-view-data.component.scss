@use 'design-tokens' as *;
@import '../../common/colors';

.row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: $spacing-2;
}

.text-and-grades {
  display: flex;
  align-items: center;
  gap: $spacing-2;
}

.title-text {
  font-weight: bold;
  align-items: center;
  color: $primary-font-color;
}

.your-business-chip {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: $spacing-1;
  justify-content: flex-start;
}

.data {
  text-align: right;
  font-weight: bold;
  padding-right: $spacing-1;
}

.data-padding {
  padding: $spacing-1;
}

.data-container {
  padding: $spacing-3;
}

span {
  color: $secondary-font-color;
  text-align: left;
}

.business-chip {
  background: $success-background-color;
  color: $grade-positive-color;
  padding: $spacing-1 $spacing-2;
  border-radius: 40%;
  font-size: $font-preset-5-size;
  font-weight: 500;
}

.not-found {
  color: $grade-negative-color;
  text-align: right;
  padding-right: $spacing-2;
}

.data {
  text-align: right;
  font-weight: bold;
}

.A,
.B,
.green-icon {
  color: $grade-positive-color;
}

.C,
.D {
  color: $grade-neutral-color;
}

.F,
.red-icon {
  color: $grade-negative-color;
}
