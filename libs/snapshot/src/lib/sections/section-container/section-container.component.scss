@use 'design-tokens' as *;
@import 'utilities';
@import '../common/colors';

mat-card {
  padding-bottom: 0;

  mat-card-title {
    min-height: 56px;
    color: $contrast-text-color;
    background-color: $section-primary-color;
    margin-bottom: 0;
    padding: 0 $spacing-3;
    flex-direction: row;
    box-sizing: border-box;
    display: flex;
    place-content: center space-between;
    align-items: center;

    .more-info {
      font-size: $font-preset-5-size;
      cursor: pointer;
      display: flex;
      flex-direction: row;
      box-sizing: border-box;
      align-items: center;

      > *:not(:last-child) {
        margin-right: $spacing-2;
      }

      mat-icon {
        font-size: 24px;
      }
    }

    .toggle-section {
      height: auto;
      margin-left: $spacing-2;
    }
  }

  mat-card-content {
    overflow: hidden;
    padding: 0 !important;
  }
}

.text-container {
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  align-items: center;
}

.small-header {
  padding: 10px 0;
  @include tablet-large {
    padding-right: 0;
  }
}
