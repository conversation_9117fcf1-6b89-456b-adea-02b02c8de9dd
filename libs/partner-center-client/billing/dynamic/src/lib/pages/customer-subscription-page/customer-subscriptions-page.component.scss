@use 'design-tokens' as *;
@import 'utilities';

.retail-header {
  margin-top: $spacing-4;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.subscription-title {
  @include text-preset-2;
  margin-bottom: $spacing-2;
}

.section-break {
  margin-top: $spacing-5;
}

.subscription-subtitle {
  @include text-preset-4;
  color: $secondary-font-color;
}

.divider {
  margin-top: $spacing-4;
}

.subscription-list {
  padding-top: $spacing-3;
}

.create-subscription-button {
  margin-top: $spacing-3;
}

.loading-container {
  width: 100%;
  display: flex;
  justify-content: center;
}

.panel-container {
  display: flex;
  height: 100%;

  @include phone {
    width: 100%;
  }

  @include phone-large {
    width: 400px;
  }

  .full-width {
    width: 100%;
  }
}
