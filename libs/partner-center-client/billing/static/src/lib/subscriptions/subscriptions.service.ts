import { inject, Inject, Injectable, OnDestroy } from '@angular/core';
import { BehaviorSubject, combineLatest, lastValueFrom, Observable, of, ReplaySubject, Subscription } from 'rxjs';

import { DatePipe } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';
import {
  BillableItem,
  BillingService,
  CHANGE_INVOICE_DATE_ON_SAME_DAY_ERROR,
  Discount,
  DiscountConsumer,
  DiscountService,
  DiscountType,
  Frequency,
  ListSubscriptionsFilter,
  PagedResponse,
  RenewalState,
  renewalStateToApi,
  SubscriptionsProjectionFilter,
  FieldMask,
} from '@galaxy/billing';
import { TranslateService } from '@ngx-translate/core';
import { Consumer, CreateSubscriptionRequest, Frequency as ApiFrequency } from '@vendasta/billing';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import assignWith from 'lodash/assignWith';
import { catchError, distinctUntilChanged, filter, map, shareReplay, take, tap } from 'rxjs/operators';
import {
  SubscriptionCancelDialogComponent,
  SubscriptionCancelDialogData,
} from './subscription-details/subscription-cancel-dialog/subscription-cancel-dialog.component';
import {
  SubscriptionRenewalDialogComponent,
  SubscriptionRenewalDialogData,
} from './subscription-details/subscription-renewal-dialog/subscription-renewal-dialog.component';
import {
  SubscriptionServicePeriodDialogComponent,
  SubscriptionServicePeriodDialogData,
} from './subscription-details/subscription-service-period-dialog/subscription-service-period-dialog.component';
import { PARTNER_CENTER_BILLING_CONFIG_TOKEN } from '../core/';
import { PartnerCenterBillingConfig } from '../core/config';
import { SUBSCRIPTION_EVENTS } from './subscription-events';
import { isSameDay } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';

export function toLocalDate(date: Date): Date {
  return new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 12);
}

export interface SubscriptionState {
  partnerId: string;
  marketId: string;
  wholesaleSubscriptions: {
    [customerId: string]: BillableItem[];
  };
  retailSubscriptions: {
    [customerId: string]: BillableItem[];
  };
}

export enum ConsumerType {
  RETAIL = 'retail',
  WHOLESALE = 'wholesale',
}

export interface CreateSubscription {
  merchantId: string;
  sku: string;
  customerId: string;
  billingStart: Date;
  renewalStart: Date;
  frequency: ApiFrequency;
  consumer: Consumer;
  renewalState: RenewalState;
  expiry: Date;
  billingCycleAnchor?: Date;
}

@Injectable()
export class SubscriptionsService implements OnDestroy {
  partnerId$: Observable<string>;
  state$$ = new BehaviorSubject<SubscriptionState>({
    partnerId: '',
    marketId: '',
    wholesaleSubscriptions: {},
    retailSubscriptions: {},
  });

  rxjsSubscriptions: Subscription[] = [];

  private earliestRetailSubscriptionRenewalDate$$ = new ReplaySubject<Date>(1);
  public readonly earliestRetailSubscriptionRenewalDate$ = this.earliestRetailSubscriptionRenewalDate$$.asObservable();
  public readonly allVisibleRenewalDates$ = combineLatest([
    this.state$$.pipe(
      map((s) => s.retailSubscriptions),
      distinctUntilChanged(),
    ),
    this.earliestRetailSubscriptionRenewalDate$,
  ]).pipe(
    map(([retailSubscriptions, earliestRetailSubscriptionRenewalDate]) => {
      const today = new Date();
      today.setHours(23, 59, 59);
      const subscriptionsWithUpcomingRenewalOrBillingStart = [
        earliestRetailSubscriptionRenewalDate,
        ...Object.values(retailSubscriptions || {})
          .map((s) => {
            return s.map((bi) => {
              if (bi.billingStart && bi.billingStart > today) {
                return bi.billingStart;
              }
              return bi.nextInvoiceDate;
            });
          })
          .flat()
          .filter(Boolean),
      ];
      return subscriptionsWithUpcomingRenewalOrBillingStart;
    }),
  );
  private _currentUpdatedSubscription$$ = new ReplaySubject<BillableItem>(1);
  public readonly currentUpdatedSubscription$ = this._currentUpdatedSubscription$$.asObservable();

  public set currentUpdatedSubscription(subscription: BillableItem | null) {
    if (!subscription) {
      return;
    }
    this._currentUpdatedSubscription$$.next(subscription);
  }

  private _reloadSubscriptions$$ = new BehaviorSubject<boolean>(true);
  public readonly reloadSubscriptions$ = this._reloadSubscriptions$$.asObservable();

  private readonly subscriptionEvents$$ = inject(SUBSCRIPTION_EVENTS);
  public readonly subscriptionsEvents$ = this.subscriptionEvents$$.asObservable();

  public set reloadSubscriptions(reload: boolean) {
    this._reloadSubscriptions$$.next(reload);
  }

  constructor(
    @Inject(PARTNER_CENTER_BILLING_CONFIG_TOKEN) readonly config: PartnerCenterBillingConfig,
    private readonly billingService: BillingService,
    private readonly discountService: DiscountService,
    private readonly dialog: MatDialog,
    private snackbarService: SnackbarService,
    private translateService: TranslateService,
    private glxyConfirmationModal: OpenConfirmationModalService,
    private datePipe: DatePipe,
  ) {
    this.partnerId$ = this.config.merchantId$;
    this.rxjsSubscriptions.push(
      this.state$$
        .pipe(
          map((s) => {
            let customerIdKey = '';
            if (s.retailSubscriptions && Object.keys(s.retailSubscriptions).length > 0) {
              customerIdKey = Object.keys(s.retailSubscriptions)[0];
            } else {
              return null;
            }
            const retailSubscriptionsWithRenewalDates = s.retailSubscriptions[customerIdKey].filter((sub) => {
              const today = new Date();
              today.setHours(23, 59, 59);
              if (sub.frequency === Frequency.OneTime) {
                // only consider one time subscriptions if in the future (will be invoiced)
                if (sub.billingStart && sub.billingStart < today) {
                  return false;
                }
              }
              return !(sub.expiry && sub.expiry < today);
            });
            return this.findEarliestInvoiceDate(retailSubscriptionsWithRenewalDates);
          }),
          filter(Boolean),
        )
        .subscribe((date) => {
          this.earliestRetailSubscriptionRenewalDate$$.next(toLocalDate(date));
        }),
    );
  }

  private findEarliestInvoiceDate(subscriptions?: BillableItem[]): Date | undefined {
    if (!subscriptions) {
      return undefined;
    }
    const today = new Date();
    today.setHours(23, 59, 59);
    const soonest = subscriptions.reduce((prev: null | BillableItem, current) => {
      if (!current?.billingStart && !current?.nextInvoiceDate) {
        // If the current item's renewal and billing start dates are zero or not set, return the previous item.
        return prev;
      }

      if (!prev) {
        return current;
      }
      const previousBINextInvoiceDate =
        prev?.billingStart && prev.billingStart > today ? prev.billingStart : prev.nextInvoiceDate;
      if (!previousBINextInvoiceDate) {
        return current;
      }

      const currentBINextInvoiceDate =
        current?.billingStart && current.billingStart > today ? current.billingStart : current.nextInvoiceDate;
      if (
        currentBINextInvoiceDate &&
        previousBINextInvoiceDate &&
        previousBINextInvoiceDate > currentBINextInvoiceDate
      ) {
        return current;
      }
      return prev;
    }, null);
    return soonest?.billingStart && soonest.billingStart > today ? soonest.billingStart : soonest?.nextInvoiceDate;
  }

  loadSubscriptions(
    filters: ListSubscriptionsFilter,
    cursor: string,
    pageSize: number,
    consumer?: ConsumerType,
  ): Observable<PagedResponse<BillableItem>> {
    let sub$: Observable<PagedResponse<BillableItem>>;
    if (consumer === ConsumerType.RETAIL) {
      sub$ = this.billingService.listRetailSubscriptions({ ...filters }, cursor, pageSize, { price: true });
    } else {
      sub$ = this.billingService.listSubscriptions({ ...filters }, cursor, pageSize, { price: true });
    }
    return sub$.pipe(
      tap((results) => {
        if (consumer === ConsumerType.RETAIL) {
          this.patchState({ retailSubscriptions: { [filters.customerId ?? '']: results.results } });
        } else {
          this.patchState({ wholesaleSubscriptions: { [filters.customerId ?? '']: results.results } });
        }
        this.subscriptionEvents$$.next({ type: 'subscriptions-loaded' });
      }),
      shareReplay(1),
    );
  }

  async cancelSubscription(subscription: BillableItem): Promise<BillableItem | undefined> {
    const data: SubscriptionCancelDialogData = {
      productName: subscription.productName,
      nextInvoiceDate: subscription.nextInvoiceDate,
    };
    const expiry: Date = await lastValueFrom(
      this.dialog
        .open(SubscriptionCancelDialogComponent, {
          data,
          width: '440px',
        })
        .afterClosed(),
    );

    if (!expiry) {
      return;
    }

    try {
      const res = await lastValueFrom(
        this.billingService.expireSubscription(
          subscription.merchantId,
          subscription.sku,
          subscription.customerId,
          subscription.orderId,
          expiry,
        ),
      );

      if (!res) {
        return;
      }

      const expiryDate = new Date(expiry);
      const today = new Date();
      if (expiryDate.toDateString() === today.toDateString()) {
        this.snackbarService.openSuccessSnack(
          this.translateService.instant('BILLING_SUBSCRIPTIONS.IMMEDIATELY_CANCEL_SUBSCRIPTION_SUCCESS'),
        );
      } else {
        this.snackbarService.openSuccessSnack(
          this.translateService.instant('BILLING_SUBSCRIPTIONS.CANCEL_SUBSCRIPTION_SUCCESS'),
        );
      }
      const latestSubscription = await lastValueFrom(
        this.billingService.getBillableItem(
          subscription.merchantId,
          subscription.sku,
          subscription.customerId,
          subscription.orderId,
          { price: true },
        ),
      );
      const bi = this.updateExistingSubscription(latestSubscription);
      this.subscriptionEvents$$.next({ type: 'subscription-canceled' });
      return bi;
    } catch (err: any) {
      this.snackbarService.openErrorSnack(
        this.translateService.instant('BILLING_SUBSCRIPTIONS.CANCEL_SUBSCRIPTION_ERROR', {
          err: err.error?.message || err.message || err,
        }),
      );
      return subscription;
    }
  }

  async uncancelSubscription(subscription: BillableItem): Promise<BillableItem> {
    await lastValueFrom(
      this.billingService
        .unexpireSubscription(subscription.merchantId, subscription.sku, subscription.customerId, subscription.orderId)
        .pipe(
          map((): BillableItem => {
            this.snackbarService.openSuccessSnack(
              this.translateService.instant('BILLING_SUBSCRIPTIONS.UNCANCEL_SUBSCRIPTION_SUCCESS'),
            );
            return subscription;
          }),
          tap(() => this.subscriptionEvents$$.next({ type: 'subscription-uncanceled' })),
          catchError((err): Observable<BillableItem> => {
            this.snackbarService.openErrorSnack(
              this.translateService.instant('BILLING_SUBSCRIPTIONS.UNCANCEL_SUBSCRIPTION_ERROR', {
                err: err.error?.message || err.message || err,
              }),
            );
            return of(subscription);
          }),
          take(1),
        ),
    );
    const latestSubscription = await lastValueFrom(
      this.billingService.getBillableItem(
        subscription.merchantId,
        subscription.sku,
        subscription.customerId,
        subscription.orderId,
        { price: true },
      ),
    );

    return this.updateExistingSubscription(latestSubscription);
  }

  async updateRenewalDate(
    subscription: BillableItem,
    canAccessBillingServicePeriods = false,
  ): Promise<BillableItem | undefined> {
    const today = new Date();
    const renewal = subscription.nextInvoiceDate;

    // cannot edit renewal if current renewal is today
    if (renewal && (isSameUTCDay(renewal, today) || renewal < today)) {
      const nextInvoiceDate = this.datePipe.transform(subscription.nextInvoiceDate, 'longDate');
      const title = this.translateService.instant(
        'BILLING_SUBSCRIPTIONS.CHANGE_INVOICE_DATE_DIALOG.NOT_ALLOWED_TITLE',
        { productName: subscription.productName },
      );
      await lastValueFrom(
        this.glxyConfirmationModal.openModal({
          title: title,
          message: this.translateService.instant(
            'BILLING_SUBSCRIPTIONS.CHANGE_INVOICE_DATE_DIALOG.NOT_ALLOWED_DESCRIPTION',
            { nextInvoiceDate: nextInvoiceDate },
          ),
          confirmButtonText: this.translateService.instant('COMMON.ACTION_LABELS.OK'),
          hideCancel: true,
        }),
      );

      return;
    }

    const data: SubscriptionRenewalDialogData = {
      merchantId: subscription.merchantId,
      customerId: subscription.customerId,
      productName: subscription.productName,
      nextRenewal: subscription.nextInvoiceDate,
    };
    const newRenewalDate: Date = await lastValueFrom(
      this.dialog
        .open(SubscriptionRenewalDialogComponent, {
          data,
          width: '425px',
        })
        .afterClosed(),
    );

    if (!newRenewalDate) {
      return;
    }

    try {
      let res;
      if (canAccessBillingServicePeriods) {
        const fieldMask = new FieldMask({ paths: ['billing_cycle_anchor'] });
        subscription.billingCycleAnchor = newRenewalDate;
        res = await lastValueFrom(this.billingService.updateSubscription(subscription, fieldMask));
      } else {
        res = await lastValueFrom(
          this.billingService.changeBillableItemNextRenewalDate(
            subscription.merchantId,
            subscription.sku,
            subscription.customerId,
            subscription.orderId,
            newRenewalDate,
          ),
        );
      }
      if (!res) {
        this.snackbarService.openErrorSnack(
          this.translateService.instant('BILLING_SUBSCRIPTIONS.CHANGE_INVOICE_DATE_DIALOG.ERROR'),
        );
        return;
      }

      this.snackbarService.openSuccessSnack(
        this.translateService.instant('BILLING_SUBSCRIPTIONS.CHANGE_INVOICE_DATE_DIALOG.SUCCESS'),
      );

      const projectionFilter: SubscriptionsProjectionFilter = {
        price: true,
      };
      // re-fetch the subscription to recalculate the next invoice date
      const latestSubscription = await lastValueFrom(
        this.billingService.getBillableItem(
          subscription.merchantId,
          subscription.sku,
          subscription.customerId,
          subscription.orderId,
          projectionFilter,
        ),
      );

      const bi = this.updateExistingSubscription(latestSubscription);
      this.subscriptionEvents$$.next({ type: 'subscription-renewal-updated' });
      return bi;
    } catch (err) {
      if (err?.error?.message === CHANGE_INVOICE_DATE_ON_SAME_DAY_ERROR) {
        this.snackbarService.openErrorSnack(
          this.translateService.instant('BILLING_SUBSCRIPTIONS.CHANGE_INVOICE_DATE_DIALOG.ERROR_INVOICE_DAY_TODAY'),
        );
      } else {
        this.snackbarService.openErrorSnack(
          this.translateService.instant('BILLING_SUBSCRIPTIONS.CHANGE_INVOICE_DATE_DIALOG.ERROR'),
        );
      }
      return;
    }
  }

  async updateServicePeriod(subscription: BillableItem): Promise<BillableItem | undefined> {
    const data: SubscriptionServicePeriodDialogData = {
      merchantId: subscription.merchantId,
      customerId: subscription.customerId,
      productName: subscription.productName,
      servicePeriodStart: subscription.nextServicePeriodStart,
    };
    const newServicePeriodStart: Date = await lastValueFrom(
      this.dialog
        .open(SubscriptionServicePeriodDialogComponent, {
          data,
          width: '425px',
        })
        .afterClosed(),
    );

    if (!newServicePeriodStart) {
      return;
    }

    try {
      const res = await lastValueFrom(
        this.billingService.changeBillableItemNextRenewalDate(
          subscription.merchantId,
          subscription.sku,
          subscription.customerId,
          subscription.orderId,
          newServicePeriodStart,
        ),
      );
      if (!res) {
        this.snackbarService.openErrorSnack(
          this.translateService.instant('BILLING_SUBSCRIPTIONS.CHANGE_SERVICE_PERIOD_DIALOG.ERROR'),
        );
        return;
      }

      this.snackbarService.openSuccessSnack(
        this.translateService.instant('BILLING_SUBSCRIPTIONS.CHANGE_SERVICE_PERIOD_DIALOG.SUCCESS'),
      );

      const projectionFilter: SubscriptionsProjectionFilter = {
        price: true,
      };
      // re-fetch the subscription to recalculate the service period dates
      const latestSubscription = await lastValueFrom(
        this.billingService.getBillableItem(
          subscription.merchantId,
          subscription.sku,
          subscription.customerId,
          subscription.orderId,
          projectionFilter,
        ),
      );

      const bi = this.updateExistingSubscription(latestSubscription);
      this.subscriptionEvents$$.next({ type: 'subscription-service-period-updated' });
      return bi;
    } catch (err) {
      this.snackbarService.openErrorSnack(
        this.translateService.instant('BILLING_SUBSCRIPTIONS.CHANGE_SERVICE_PERIOD_DIALOG.ERROR'),
      );
      return;
    }
  }

  createDisount(
    merchantId: string,
    sku: string,
    discountType: DiscountType,
    amount: number,
    startDateTime: string,
    endDateTime?: string,
    resetEachPeriodFlag?: boolean,
    description?: string,
    customerId?: string,
    discountConsumer = DiscountConsumer.WHOLESALE,
    subscriptionId?: string,
  ): Observable<string> {
    return this.discountService
      .create(
        merchantId,
        sku,
        discountType,
        amount,
        startDateTime,
        endDateTime,
        resetEachPeriodFlag,
        description,
        customerId,
        discountConsumer,
        subscriptionId,
      )
      .pipe(tap(() => this.subscriptionEvents$$.next({ type: 'discount-created' })));
  }

  updateDiscount(
    merchantId: string,
    id: string,
    discountType: DiscountType,
    amount: number,
    startDateTime: string,
    endDateTime?: string,
    resetEachPeriodFlag?: boolean,
    description?: string,
    customerId?: string,
    subscriptionId?: string,
  ): Observable<unknown> {
    return this.discountService
      .update(
        merchantId,
        id,
        discountType,
        amount,
        startDateTime,
        endDateTime,
        resetEachPeriodFlag,
        description,
        customerId,
        subscriptionId,
      )
      .pipe(tap(() => this.subscriptionEvents$$.next({ type: 'discount-updated' })));
  }

  private updateExistingSubscription(updatedSubscription: BillableItem): BillableItem {
    let existingRetailSubscriptions =
      this.state$$.getValue()?.retailSubscriptions[updatedSubscription?.customerId] ??
      this.state$$.getValue()?.retailSubscriptions[''];
    existingRetailSubscriptions = (existingRetailSubscriptions || []).map((s) => {
      if (s.subscriptionId === updatedSubscription.subscriptionId) {
        return updatedSubscription;
      }
      return s;
    });
    this.patchState({ retailSubscriptions: { [updatedSubscription?.customerId]: existingRetailSubscriptions } });

    return updatedSubscription;
  }

  private patchState(patch: Partial<SubscriptionState>): void {
    // https://lodash.com/docs/4.17.15#assignWith
    this.state$$.next(
      assignWith({}, this.state$$.getValue(), patch, (objValue: any, srcValue: any) => {
        if (Array.isArray(objValue) || Array.isArray(srcValue)) {
          return srcValue;
        }
        if (typeof objValue === 'string' && typeof srcValue === 'string') {
          return srcValue || objValue; // prefer non-empty strings
        }
      }),
    );
  }

  async createSubscription(cs: CreateSubscription): Promise<string> {
    const req = new CreateSubscriptionRequest({
      merchantId: cs.merchantId,
      sku: cs.sku,
      customerId: cs.customerId,
      billingStart: cs.billingStart,
      renewalStart: cs.renewalStart,
      frequency: cs.frequency,
      consumer: cs.consumer,
      renewalState: renewalStateToApi(cs.renewalState),
      expiry: cs.expiry,
      billingCycleAnchor: cs.billingCycleAnchor,
    });

    const response = await lastValueFrom(this.billingService.createSubscription(req));
    this.subscriptionEvents$$.next({ type: 'subscription-created' });
    return response?.subscriptionId;
  }

  async expireDiscount(discount: Discount) {
    await lastValueFrom(
      this.discountService.expire(discount.merchantId, discount.id).pipe(
        catchError((err) => {
          this.snackbarService.openErrorSnack(
            this.translateService.instant(
              'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.DISCOUNTS.EXPIRE_DISCOUNT_ERROR',
              {
                err: err.error?.message || err.message || err,
              },
            ),
          );
          return of(null);
        }),
        take(1),
      ),
    );
    this.subscriptionEvents$$.next({ type: 'discount-expired' });
  }

  ngOnDestroy(): void {
    this.rxjsSubscriptions.forEach((sub) => sub.unsubscribe());
  }
}

function isSameUTCDay(date1?: Date, date2?: Date) {
  if (date1 === date2) {
    return true;
  }
  if (!date1 || !date2) {
    return false;
  }
  const timeZone = 'UTC';
  const zonedDate1 = utcToZonedTime(date1, timeZone);
  const zonedDate2 = utcToZonedTime(date2, timeZone);
  return isSameDay(zonedDate1, zonedDate2);
}
