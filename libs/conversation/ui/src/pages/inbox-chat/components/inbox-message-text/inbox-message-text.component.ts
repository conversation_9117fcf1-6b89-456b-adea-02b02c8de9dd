import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  EventEmitter,
  inject,
  Inject,
  input,
  Input,
  OnInit,
  Output,
  Signal,
  signal,
  ViewChild,
  ViewContainerRef,
  WritableSignal,
} from '@angular/core';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';
import { MatMenu } from '@angular/material/menu';
import { PaymentService } from '@galaxy/billing';
import { FeatureFlagService } from '@galaxy/partner';
import { Conversation, ConversationChannel, MediaInterface, ParticipantType } from '@vendasta/conversation';
import {
  ChatComposerComponent,
  ChatSource,
  ChatSourceId,
  SentMessage,
} from '@vendasta/galaxy/chat-composer/src/chat-composer.component';
import { PopoverComponent, PopoverPositions } from '@vendasta/galaxy/popover';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { FileInfo, FileUploadStatus, GALAXY_UPLOADER_SERVICE_TOKEN } from '@vendasta/galaxy/uploader';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { TranslateService } from '@ngx-translate/core';
import { Channel, ReviewRequestApiService } from '@vendasta/reputation';
import {
  BehaviorSubject,
  combineLatest,
  filter,
  first,
  firstValueFrom,
  forkJoin,
  map,
  Observable,
  of,
  ReplaySubject,
  shareReplay,
  switchMap,
  take,
  tap,
  withLatestFrom,
} from 'rxjs';
import { ConversationService } from '../../../../../../core/src/lib/state/conversation.service';
import { isMobile } from '../../../../../../core/src/lib/inbox-utils';
import { InboxService } from '../../../../../../core/src/lib/inbox.service';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import {
  ConversationAvailableChannels,
  ConversationDetail,
  MessageInfo,
} from '../../../../../../core/src/lib/interface/conversation.interface';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  CONVERSATION_HOST_APP_INTERFACE_TOKEN,
  PARTNER_ID_TOKEN,
} from '../../../../../../core/src/lib/tokens';
import { AlertOptions } from '../../../../../../core/src/lib/channels/conversation-channel.abstract';
import { InboxInsertPaymentLinkComponent } from '../inbox-insert-payment-link/inbox-insert-payment-link.component';
import { InboxInsertReviewLinkComponent } from '../inbox-insert-review-link/inbox-insert-review-link.component';
import { InboxInsertTemplateComponent } from '../inbox-insert-template/inbox-insert-template.component';
import { InboxUploadFileComponent } from '../inbox-upload-file/inbox-upload-file.component';
import { InboxUploadService } from '../inbox-upload-file/inbox-upload.service';
import { filterNullAndUndefined } from '@vendasta/rx-utils';
import { TimerService } from './timer-service';
import { Capacitor } from '@capacitor/core';
import { openAppNotificationSettings, pushNotificationsEnabled } from '@vendasta/shared';
import { mapConversationChannelToChatSource } from '../../../../conversation-utils';
import { WhatsappTemplatesService } from '../../../../../../core/src/lib/channels/whatsapp-templates.service';
import { isAccountGroupAndCustomerConversation } from '../../../../../../core/src/lib/conversation-utils';
import { AiResponder } from '../../../../../../core/src/lib/interface/assistant.interface';
import { AiAssistantService, DEFAULT_CHAT_RECEPTIONIST_AVATAR_SVG_ICON } from '@galaxy/ai-assistant';
import { Router } from '@angular/router';
const hideNotificationAlertKey = 'hide-notification-alert';

export interface ChatSourceInfo {
  channel: ConversationChannel;
  phoneNumber?: string;
}

interface PopoverConfig {
  title: string;
  contentFirst: string;
  contentSecond: string;
}

type FileTypes = 'image' | 'other';

interface ComposerAction {
  id: string;
  action: string;
  icon: string;
  onClick?: () => void;
  matMenu?: MatMenu | null;
  visible: Observable<boolean>;
}

@Component({
  selector: 'inbox-message-text',
  templateUrl: './inbox-message-text.component.html',
  styleUrls: ['./inbox-message-text.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [{ provide: GALAXY_UPLOADER_SERVICE_TOKEN, useExisting: InboxUploadService }, { provide: TimerService }],
  standalone: false,
})
export class InboxMessageTextComponent implements OnInit, AfterViewInit {
  math = Math;
  private readonly hostAppInterface = inject(CONVERSATION_HOST_APP_INTERFACE_TOKEN);
  private readonly snackbarService = inject(SnackbarService);
  private readonly confirmationModalService = inject(OpenConfirmationModalService);
  private readonly translateService = inject(TranslateService);
  private readonly reviewRequestApiService = inject(ReviewRequestApiService);
  protected readonly whatsappTemplatesService = inject(WhatsappTemplatesService);
  protected readonly aiAssistantService = inject(AiAssistantService, { optional: true });
  private readonly router = inject(Router);
  private readonly viewContainerRef = inject(ViewContainerRef);

  @ViewChild('chatComposer') chatComposer?: ChatComposerComponent;
  @ViewChild('inboxUploadFile') inboxUploadFile?: InboxUploadFileComponent;
  @ViewChild('inboxInsertReviewLink') inboxInsertReview?: InboxInsertReviewLinkComponent;
  @ViewChild('inboxInsertPaymentLink') inboxInsertPaymentLink?: InboxInsertPaymentLinkComponent;

  readonly isImpersonating = input(false);
  readonly clearMessageText = input(false);
  readonly prefilledMessage = input<string | null>(null);
  readonly missingContactInformation = input(false);

  readonly selectedChatSourceId = signal<ChatSourceId | undefined>(undefined);

  protected readonly currentUrl = this.router.url;
  protected readonly whatsAppTemplateLanguage = signal<string>('');
  protected readonly whatsAppTemplates = toSignal(this.whatsappTemplatesService.approvedTemplates$);
  protected readonly selectedWhatsAppTemplate = toSignal(
    this.whatsappTemplatesService.whatsappTemplate(toObservable(this.whatsAppTemplateLanguage)),
  );
  protected readonly whatsAppTemplatesOnly = toSignal(this.whatsappTemplatesService.whatsappTemplatesOnly$);
  // Whether a WhatsApp template is being shown in the message text area
  protected readonly whatsAppTemplateSelected = computed(() => {
    return this.selectedChatSourceId() === 'whatsapp' && this.whatsAppTemplatesOnly();
  });
  protected readonly whatsAppTemplateError = computed(() => {
    if (!this.whatsAppTemplateSelected()) {
      return undefined;
    }
    return this.selectedWhatsAppTemplate()?.bodyParameters.find((param) => !!param.error)?.error;
  });
  protected readonly hydratedWhatsAppTemplate = toSignal(
    this.whatsappTemplatesService.hydratedWhatsappTemplate(toObservable(this.whatsAppTemplateLanguage)),
  );

  readonly whatsAppTemplatesAlert = computed(() => {
    return !this.disabledAlert() && this.whatsAppTemplateSelected();
  });
  whatsAppTemplatesPendingApproval = toSignal(this.whatsappTemplatesService.templatesPendingApproval$);

  protected readonly $conversationAIResponder: WritableSignal<AiResponder | null> = signal(null);

  @Input() set aiResponder(value: AiResponder | null) {
    this.$conversationAIResponder.set(value);
  }

  private readonly availableChannels$$ = new ReplaySubject<ConversationAvailableChannels>(1);
  readonly availableChannels$ = this.availableChannels$$.asObservable();

  readonly clearMessageTextOnSend: Signal<boolean> = computed(() => {
    return this.clearMessageText() && !this.whatsAppTemplateSelected();
  });

  @Input({ required: true }) set availableChannels(value: ConversationAvailableChannels | null) {
    if (value) {
      this.availableChannels$$.next(value);
      if (ConversationChannel.CONVERSATION_CHANNEL_UNDEFINED !== value.preferredChannel && value.preferredChannel) {
        this.selectedChatSourceId.set(mapConversationChannelToChatSource(value.preferredChannel).id);
      }
    }
  }

  readonly conversationDetail = input<ConversationDetail | null>(null);
  readonly conversationDetail$ = toObservable(this.conversationDetail);

  @Output() sendMessage = new EventEmitter<MessageInfo>();
  @Output() fileChange = new EventEmitter<FileInfo[]>();

  readonly files = signal<FileInfo[]>([]);

  fileTypes = computed<FileTypes>(() => {
    for (const file of this.files()) {
      if (file.file?.type.includes('image')) {
        return 'image';
      }
      return 'other';
    }
    return 'other';
  });

  readonly disabledAlert: Signal<AlertOptions | null>;

  disableButtonForSendMsg$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  disableButtonForUploadFile$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  disableButton$: Observable<boolean> = combineLatest([
    this.disableButtonForSendMsg$$.asObservable(),
    this.disableButtonForUploadFile$$.asObservable(),
  ]).pipe(map(([sendMsg, uploadFile]) => sendMsg || uploadFile));

  protected readonly isComposerDisabled = computed(() => {
    return (
      this.isImpersonating() ||
      this.missingContactInformation() ||
      this.selectedChatSourceId() === undefined ||
      !!this.whatsAppTemplateError() ||
      (this.whatsAppTemplateSelected() && this.whatsAppTemplatesPendingApproval())
    );
  });
  protected readonly isComposerReadonly = computed(() => {
    return this.whatsAppTemplateSelected();
  });

  showChannelInfoPopover = false;
  showDynamicFieldsHydrationAlert = false;

  readonly showNotificationsDisabledBanner = signal(false);

  protected readonly messageText = signal<string>('');

  protected readonly DEFAULT_CHAT_RECEPTIONIST_AVATAR_SVG_ICON = DEFAULT_CHAT_RECEPTIONIST_AVATAR_SVG_ICON;

  ngOnInit(): void {
    if (Capacitor.isNativePlatform()) {
      pushNotificationsEnabled().then((enabled) => {
        const hideNotificationAlert = localStorage?.getItem(hideNotificationAlertKey) === 'true';
        this.showNotificationsDisabledBanner.set(!enabled && !hideNotificationAlert);
      });
    }
    let languageCode = navigator.language || 'en';
    if (languageCode.includes('-')) {
      languageCode = languageCode.split('-')[0];
    }
    this.whatsAppTemplateLanguage.set(languageCode);
  }

  private readonly currentConversationChannel$ = this.availableChannels$.pipe(
    map((availableChannels) => {
      return availableChannels?.preferredChannel;
    }),
  );

  readonly isOpenAIChannel$ = this.currentConversationChannel$.pipe(
    map(
      (channel) =>
        channel === ConversationChannel.CONVERSATION_CHANNEL_OPENAI ||
        channel === ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
    ),
  );

  readonly canAccessVoiceRecorder$ = this.currentConversationChannel$.pipe(
    map((channel) => {
      return channel === ConversationChannel.CONVERSATION_CHANNEL_INTERNAL;
    }),
  );

  readonly chatSourcesInfo$: Observable<ChatSourceInfo[]> = this.availableChannels$.pipe(
    switchMap((value) => {
      const channels = value.availableChannels ?? [];
      if (channels.length === 0) {
        return of([]);
      }
      const chatSourcesInfo = channels.map((channel) => {
        return this.getChatSourceInfo(channel).pipe(first());
      });
      return forkJoin(chatSourcesInfo);
    }),
  );

  readonly availableChatSources$: Observable<ChatSource[]> = this.chatSourcesInfo$.pipe(
    map((chatSourceInfo): ChatSource[] => {
      return chatSourceInfo.map((info) => {
        return mapConversationChannelToChatSource(info.channel, info.phoneNumber);
      });
    }),
    map((v) => v ?? new Array<ChatSource>()),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  readonly popoverConfig$: Observable<PopoverConfig> = this.chatSourcesInfo$.pipe(
    map((results) => {
      if (results.length !== 1) {
        return {
          title: '',
          contentFirst: '',
          contentSecond: '',
        };
      }
      const result = results[0];
      switch (result.channel) {
        case ConversationChannel.CONVERSATION_CHANNEL_SMS:
          return this.setSMSChannelPopoverInfo(result.phoneNumber);
        case ConversationChannel.CONVERSATION_CHANNEL_INTERNAL:
          return this.setInPlatformChannelPopoverInfo();
        case ConversationChannel.CONVERSATION_CHANNEL_GOOGLE_BUSINESS_COMMUNICATIONS:
          return this.setGoogleBusinessCommunicationsPopoverInfo();
        default:
          return {
            title: '',
            contentFirst: '',
            contentSecond: '',
          };
      }
    }),
  );

  readonly showPopover$: Observable<boolean> = combineLatest([this.isOpenAIChannel$, this.popoverConfig$]).pipe(
    map(([isOpenAIChannel, popoverConfig]) => {
      return !isOpenAIChannel && !!popoverConfig.title;
    }),
  );

  readonly messages$ = this.conversationService.messages$;

  canAccessPaymentLink$: Observable<boolean>;

  TOOLTIP_BOTTOM_RIGHT = [{ ...PopoverPositions.TopRight }];
  showImpersonatedPopover = false;

  composerCollapsedActions: ComposerAction[] = [];

  readonly isAIWaitingToReply = toSignal(
    combineLatest([this.conversationDetail$, this.messages$]).pipe(
      filter(([detail, messages]) => !!detail && !!messages),
      map(([detail, messages]): number | null => {
        // checks if AI is active for the conversation
        if (!this.$isAIResponderActiveForConversation()) {
          return null;
        }

        const latestMessage = messages?.[0];
        if (!latestMessage) {
          return null;
        }

        if (detail?.conversation.aiConfiguration?.willRespondAt?.getTime() !== 0) {
          return this.timerService.calculateWaitDurationInSeconds(detail?.conversation.aiConfiguration?.willRespondAt);
        }

        return null;
      }),
      tap((waitTime) => (waitTime === 0 ? this.timerService.stop() : this.timerService.start(waitTime || 0))),
    ),
  );

  aiWaitTime = toSignal(
    this.timerService.remainingSeconds$.pipe(map((seconds) => this.timerService.getRemainingTime(seconds))),
  );

  isOpenAIChannel = toSignal(this.isOpenAIChannel$);
  protected readonly showActionButtons = computed(() => {
    return (
      !this.isImpersonating() &&
      !this.isOpenAIChannel() &&
      !(this.selectedChatSourceId() === 'whatsapp' && this.whatsAppTemplatesOnly())
    );
  });

  readonly $isAIResponderActiveForConversation = computed(() => {
    return this.$conversationAIResponder()?.isEnabled && this.$conversationAIResponder()?.isConversationEnabled;
  });

  readonly $aiAssistantConfigUrl = toSignal(
    toObservable(this.$conversationAIResponder).pipe(
      switchMap((responder) => {
        const id = responder?.assistant?.id;
        return id && this.aiAssistantService ? this.aiAssistantService.buildAssistantConfigurationUrl(id) : of('');
      }),
    ),
    { initialValue: '' },
  );

  constructor(
    private conversationService: ConversationService,
    private featureFlagService: FeatureFlagService,
    private paymentService: PaymentService,
    @Inject(PARTNER_ID_TOKEN) readonly partnerId$: Observable<string>,
    @Inject(ACCOUNT_GROUP_ID_TOKEN) readonly accountGroupId$: Observable<string>,
    private inboxService: InboxService,
    private inboxUploadService: InboxUploadService,
    public modal: MatDialog,
    private analyticsService: ProductAnalyticsService,
    private timerService: TimerService,
  ) {
    this.canAccessPaymentLink$ = this.partnerId$.pipe(
      switchMap((partnerId) => {
        return this.featureFlagService.batchGetStatus(partnerId, '', ['inbox_payment_link']).pipe(
          take(1),
          shareReplay(1),
          map((res) => res['inbox_payment_link']),
        );
      }),
      withLatestFrom(this.accountGroupId$),
      switchMap(([canAccess, accountGroupId]) => {
        if (canAccess && this.inboxService.isBusinessApp) {
          // check if we have setup the retail merchant's stripe account
          return this.paymentService.getRetailProvider(accountGroupId).pipe(
            take(1),
            map((retailProvider) => !!retailProvider?.stripeConnectId),
          );
        }
        if (canAccess && this.inboxService.isPartnerCenter) {
          // check if user has access to Partner Admin views. this will hide the menu for salesperson-only role
          return this.inboxService.userIsPartnerAdmin$;
        }
        return of(false);
      }),
      take(1),
    );

    this.conversationDetail$
      .pipe(takeUntilDestroyed(), withLatestFrom(this.canAccessPaymentLink$))
      .subscribe(([conversationDetail, canAccessPaymentLink]) => {
        if (conversationDetail?.conversation?.conversationId) {
          this.inboxUploadService.conversationId$$.next(conversationDetail.conversation.conversationId);
        }

        if (canAccessPaymentLink) {
          this.addCreateInvoiceAction(conversationDetail?.conversation);
        }
      });

    const alertOptions$ = this.conversationDetail$.pipe(
      filterNullAndUndefined(),
      switchMap((convoDetails) => {
        //TODO: MEGA-1077 - Update this to only use the preferred channel and the selected channel by the user as input and to not block the channel selection
        return this.conversationService.displayAlertIfExists(convoDetails, convoDetails.conversation.channel);
      }),
    );

    const disabledAlert = toSignal(alertOptions$, {
      initialValue: null,
    });

    this.disabledAlert = computed(() => {
      if (disabledAlert()) {
        return disabledAlert() ?? null;
      }
      if (this.whatsAppTemplateError()) {
        return this.whatsAppTemplateError() ?? null;
      }
      return null;
    });

    effect(() => {
      if (this.selectedChatSourceId() === 'whatsapp' && this.whatsAppTemplatesOnly()) {
        this.messageText.set(this.hydratedWhatsAppTemplate() ?? '');
      } else {
        this.messageText.set(this.prefilledMessage() ?? '');
      }
    });
  }

  // adds the create invoice action button if the conversation is with a customer
  private addCreateInvoiceAction(conversation: Conversation | undefined) {
    if (!conversation) {
      return;
    }
    const isCustomerConversation = isAccountGroupAndCustomerConversation(conversation);
    const createInvoiceActionIndex = this.composerCollapsedActions.findIndex(
      (action) => action.id === 'insertpaymentlink',
    );
    if (!isCustomerConversation && createInvoiceActionIndex >= 0) {
      this.composerCollapsedActions.splice(createInvoiceActionIndex, 1);
    }
    if (isCustomerConversation && createInvoiceActionIndex == -1) {
      this.composerCollapsedActions.push({
        id: 'insertpaymentlink',
        action: 'INBOX.REQUEST_PAYMENT.TITLE',
        icon: 'attach_money',
        onClick: () => this.inboxInsertPaymentLink?.openInsertLinkModal(),
        visible: of(true),
      });
    }
  }

  async canAccessCustomerVoice(): Promise<boolean> {
    return await firstValueFrom(this.inboxInsertReview?.customerVoiceAppIsActive$ || of(false));
  }

  async ngAfterViewInit(): Promise<void> {
    if (this.inboxService.isBusinessApp) {
      const canAccessCV = await this.canAccessCustomerVoice();
      const isRMPremiumActive = await firstValueFrom(this.inboxService.isRMPremiumActive());
      if (isRMPremiumActive) {
        this.composerCollapsedActions.push({
          id: 'reputation',
          action: 'INBOX.CHAT.INSERT_REVIEW_LINK',
          icon: 'star_outline',
          visible: of(true),
          onClick: () => this.openRMReviewRequestModal(),
        });
      } else if (canAccessCV) {
        this.composerCollapsedActions.push({
          id: 'customervoice',
          action: 'INBOX.CHAT.INSERT_REVIEW_LINK',
          icon: 'star_outline',
          matMenu: this.inboxInsertReview?.customerVoiceMenu || null,
          visible: of(true),
        });
      }
    }

    this.composerCollapsedActions.push({
      id: 'inserttemplate',
      action: 'INBOX.MESSAGE_TEMPLATES.TITLE',
      icon: 'article',
      onClick: async () => {
        const trackingProps = await this.inboxService.buildTemplateTrackProperties();
        this.analyticsService.trackEvent('inbox', 'template', 'insert-template-button-click', 0, trackingProps);
        this.openInsertTemplateModal();
      },
      visible: of(true),
    });

    // add the 'send invoice' action button when starting a brand new conversation
    const conversationDetail = await firstValueFrom(this.conversationDetail$);
    this.addCreateInvoiceAction(conversationDetail?.conversation);
  }

  private getChatSourceInfo(ch: ConversationChannel): Observable<ChatSourceInfo> {
    const result: ChatSourceInfo = {
      channel: ch,
    };

    if (ch === ConversationChannel.CONVERSATION_CHANNEL_SMS) {
      // If it's multilocation Inbox, we need to get the current conversation's SMS number because conversations in multilocation have different phone numbers
      if (this.hostAppInterface.getAppOptions().is_multilocation) {
        return this.conversationService.currentConvoSMSNumber$.pipe(
          filter((resp) => !!resp),
          map((resp) => {
            result.phoneNumber = resp.phoneNumber ?? undefined;
            return result;
          }),
        );
        // If it's regular Inbox, we need to get the phone number of account group so that users still can send SMS in new message component
      } else {
        return this.inboxService.SMSNumber$.pipe(
          filter((phoneNumber) => phoneNumber !== null),
          map((phoneNumber) => {
            result.phoneNumber = phoneNumber ?? undefined;
            return result;
          }),
        );
      }
    }
    return of(result);
  }

  onSubmit(sentMessage: SentMessage): void {
    if (this.selectedChatSourceId() === 'whatsapp' && this.whatsAppTemplatesOnly()) {
      const whatsappTemplate = this.selectedWhatsAppTemplate();
      if (whatsappTemplate?.id && whatsappTemplate?.body) {
        this.sendMessage.emit({
          text: sentMessage?.messageText,
          template: {
            id: whatsappTemplate.id,
            bodyParameters: whatsappTemplate.bodyParameters,
          },
          channel: ConversationChannel.CONVERSATION_CHANNEL_WHATSAPP,
          attachments: [],
        });
      }
    } else {
      const text = sentMessage?.messageText;
      const attachments = this.getMessageMediaInfo(this.files());
      const channel = mapChatSourceIdToConversationChannel(sentMessage.sourceId!);

      const messageInfo: MessageInfo = {
        text: text,
        attachments: attachments,
        channel,
      };

      if ((text && text.trim() !== '') || messageInfo.attachments.length > 0) {
        this.sendMessage.emit(messageInfo);
        if (this.files().length > 0) {
          this.removeFile();
        }
      }
    }
  }

  getMessageMediaInfo(files: FileInfo[]): MediaInterface[] {
    if (!files || files.length <= 0) {
      return [];
    }

    const messageMedia: MediaInterface[] = files.map((file): MediaInterface => {
      const { media_content_type, media_location_path, media_file_name, media_file_size } = file.resp.data;
      return {
        mediaContentType: media_content_type,
        mediaLocationPath: media_location_path,
        mediaFileName: media_file_name,
        fileSize: media_file_size,
      };
    });

    return messageMedia;
  }

  toggleChannelInfoPopover(): void {
    this.showChannelInfoPopover = !this.showChannelInfoPopover;
  }

  closeChannelInfoPopover(): void {
    this.showChannelInfoPopover = false;
  }

  showImpersonatedPopovers(): void {
    this.showImpersonatedPopover = true;
  }

  hideImpersonatedPopovers(): void {
    this.showImpersonatedPopover = false;
  }

  insertLink(reviewLink: string): void {
    this.chatComposer?.insertDynamicContent(reviewLink, 'curser');
    this.chatComposer?.composerTextArea?.nativeElement?.focus();
  }

  setFile(file: FileInfo): void {
    this.files.set([file]);
    switch (file.status) {
      case FileUploadStatus.Queued:
      case FileUploadStatus.InProgress:
        this.disableButtonForUploadFile$$.next(true);
        break;
      case FileUploadStatus.Success:
      case FileUploadStatus.Fail:
      default:
        this.disableButtonForUploadFile$$.next(false);
        break;
    }
    this.fileChange.emit(this.files());
  }

  removeFile(): void {
    this.files.set(new Array<FileInfo>());
    //Clear file and queue and error
    this.inboxUploadService.clear();
    this.inboxUploadService.fileErrored$$.next(null);
    this.disableButtonForUploadFile$$.next(false);
    this.fileChange.emit(this.files());
  }

  toggleDynamicFieldsHydrationAlert(state: boolean): void {
    this.showDynamicFieldsHydrationAlert = state;
  }

  setInPlatformChannelPopoverInfo(): PopoverConfig {
    return {
      title: 'INBOX.INFO.PARTNER_PLATFORM_LABEL',
      contentFirst: 'INBOX.POPOVER.IN_PLATFORM_CHANNEL_MESSAGE_INFO',
      contentSecond: '',
    };
  }

  setGoogleBusinessCommunicationsPopoverInfo(): PopoverConfig {
    return {
      title: 'INBOX.INFO.GOOGLE_BUSINESS_COMMUNICATIONS_LABEL',
      contentFirst: 'INBOX.POPOVER.GOOGLE_BUSINESS_COMMUNICATIONS_CHANNEL_MESSAGE_INFO',
      contentSecond: '',
    };
  }

  setSMSChannelPopoverInfo(phoneNumber?: string): PopoverConfig {
    const popoverConfig: PopoverConfig = {
      title: '',
      contentFirst: '',
      contentSecond: '',
    };
    if (!phoneNumber) {
      popoverConfig.title = 'INBOX.POPOVER.BUSINESS_INITIATED_CONVERSATIONS';
      popoverConfig.contentFirst = 'INBOX.POPOVER.NO_PHONE_NUMBER_PROVISIONED';
      popoverConfig.contentSecond = '';
    }
    return popoverConfig;
  }

  clearText(): void {
    this.chatComposer?.clearMessageText();
  }

  async openInsertTemplateModal(): Promise<void> {
    const modalRef$ = this.modal.open(InboxInsertTemplateComponent, {
      width: '620px',
      autoFocus: false,
      viewContainerRef: this.viewContainerRef,
    });

    try {
      const hydratedTemplate = await firstValueFrom(modalRef$.afterClosed());
      if (hydratedTemplate.template) {
        this.chatComposer?.insertDynamicContent(hydratedTemplate.template.content, 'curser');
        this.chatComposer?.composerTextArea?.nativeElement?.focus();
        if (!hydratedTemplate?.hydrationInformation?.allFieldsHydrated) {
          this.toggleDynamicFieldsHydrationAlert(true);
        }
        const trackingProps = await this.inboxService.buildTemplateTrackProperties();
        this.analyticsService.trackEvent('inbox', 'template', 'insert-template-success', 0, {
          trackingProps,
          templateId: hydratedTemplate.template.templateId,
        });
      }
    } catch (error) {
      console.error('error inserting template', error);
      this.snackbarService.openErrorSnack('INBOX.MESSAGE_TEMPLATES.MODAL.ERROR');
      return;
    }
  }

  async openRMReviewRequestModal(): Promise<void> {
    const conversationDetail = await firstValueFrom(this.conversationDetail$);
    if (!conversationDetail) {
      this.snackbarService.openErrorSnack('INBOX.ERROR.CONVERSATION_DETAILS');
      return;
    }
    const title = this.hostAppInterface.getConversationTitleInfo(conversationDetail).title;
    this.confirmationModalService
      .openModal({
        title: 'INBOX.REQUEST_REVIEW.TITLE',
        message: this.translateService.instant('INBOX.REQUEST_REVIEW.CONTENT', { customerName: title }),
        confirmButtonText: 'INBOX.REQUEST_REVIEW.SEND_REQUEST',
      })
      .subscribe(async (confirmed) => {
        const accountGroupId = await firstValueFrom(this.accountGroupId$);
        if (confirmed) {
          const recipients = conversationDetail?.participants
            ?.filter((p) => p.isSubjectParticipant && p.internalParticipantId)
            .filter((participant) => participant.participantType === ParticipantType.PARTICIPANT_TYPE_CUSTOMER);
          try {
            await firstValueFrom(
              this.reviewRequestApiService.scheduleReviewRequest({
                accountGroupId: accountGroupId,
                contactIds: recipients.map((recipient) => recipient.internalParticipantId),
                channel: Channel.SMS_FALLBACK_TO_EMAIL,
                sendNow: true,
              }),
            );
          } catch (error) {
            this.snackbarService.openErrorSnack('INBOX.ERROR.SEND_REVIEW_REQUEST');
          }
        }
      });
  }

  get isMobile(): boolean {
    return isMobile();
  }

  closeNotificationsDisabledBanner(): void {
    this.showNotificationsDisabledBanner.set(false);
    localStorage?.setItem(hideNotificationAlertKey, 'true');
  }

  async openAppSettings(): Promise<void> {
    await openAppNotificationSettings();
  }

  async abortAIResponse(conversationId: string, aiResponderPopover: PopoverComponent): Promise<void> {
    try {
      await this.conversationService.abortAIResponder(conversationId);
      this.snackbarService.openSuccessSnack('INBOX.CHAT.AI_RESPONSE_CANCELLED');
      aiResponderPopover.close();
    } catch (e) {
      console.error('error aborting ai response in the conversation', e);
      this.snackbarService.openErrorSnack(this.translateService.instant('INBOX.SOMETHING_WENT_WRONG'));
    }
  }
}

function mapChatSourceIdToConversationChannel(sourceId: ChatSourceId): ConversationChannel {
  switch (sourceId) {
    case 'sms':
      return ConversationChannel.CONVERSATION_CHANNEL_SMS;
    case 'facebook':
      return ConversationChannel.CONVERSATION_CHANNEL_FACEBOOK;
    case 'google':
      return ConversationChannel.CONVERSATION_CHANNEL_GOOGLE_BUSINESS_COMMUNICATIONS;
    case 'webchat':
      return ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT;
    case 'openai':
      return ConversationChannel.CONVERSATION_CHANNEL_OPENAI;
    case 'platform':
      return ConversationChannel.CONVERSATION_CHANNEL_INTERNAL;
    case 'email':
      return ConversationChannel.CONVERSATION_CHANNEL_EMAIL;
    case 'instagram':
      return ConversationChannel.CONVERSATION_CHANNEL_INSTAGRAM;
    case 'whatsapp':
      return ConversationChannel.CONVERSATION_CHANNEL_WHATSAPP;
    case 'ai-assistant':
      return ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT;
    default:
      throw new Error(`Unknown chat source id: ${sourceId}`);
  }
}
