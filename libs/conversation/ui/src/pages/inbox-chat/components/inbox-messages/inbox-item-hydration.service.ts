import { Injectable } from '@angular/core';
import { mapConversationChannelToChatSource } from '../../../../conversation-utils';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
import { FormIconSvg } from './inbox-event/icon-svg-string';
import { ConversationChannel, Event, EventType } from '@vendasta/conversation';
import {
  ConversationEvent,
  ConversationMessage,
  EventInfo,
  InboxItem,
} from '../../../../../../core/src/lib/interface/conversation.interface';
import { chatMessageFrom } from '../../../../../../pipes/src/message.pipes';

export const metadataCampaignUrlKey = 'event_campaign_url';
export const metadataEmailIdKey = 'event_email_id';
export const metadataMissedCallKey = 'event_missed_call';
export const metadataCallFromNumberKey = 'event_call_from_number';
export const metadataCallToNumberKey = 'event_call_to_number';
export const metadataCallDurationKey = 'event_call_duration';
export const metadataCallRecordingUrlKey = 'event_call_recording_url';
export const metadataCallTranscriptKey = 'event_call_transcript';
export const metadataCallDirectionKey = 'event_call_direction';
export const metadataCallSourceDrill1Key = 'event_call_source_drill_1';
export const metadataCallRecordId = 'event_call_record_id';
export const aiVoiceReceptionist = 'AI Voice Receptionist';

@Injectable()
export class InboxItemHydrationService {
  constructor(
    private matIconRegistry: MatIconRegistry,
    private sanitizer: DomSanitizer,
  ) {
    this.matIconRegistry.addSvgIconLiteral('form-icon-svg', this.sanitizer.bypassSecurityTrustHtml(FormIconSvg));
  }

  hydrateEventInfo(item: Event): EventInfo {
    const info = {} as EventInfo;
    info.EventType = item.type || EventType.EVENT_TYPE_UNDEFINED;

    if (!item.type) {
      if (item.message === 'SMS review request') {
        info.EventType = EventType.EVENT_TYPE_REVIEW_REQUEST;
      } else if (item.labelKey === 'INBOX.EVENTS.LEAD_CAPTURED') {
        info.EventType = EventType.EVENT_TYPE_FORM_SUBMISSION;
      }
    }

    info.title = item.labelKey;
    info.content = item.message;
    info.channel = item.channel ? mapConversationChannelToChatSource(item.channel).sourceName : '';

    const metaData = item.metadata ? new Map(item.metadata.map((obj) => [obj.key, obj.value])) : new Map();

    switch (info.EventType) {
      case EventType.EVENT_TYPE_REVIEW_REQUEST:
        info.icon = 'star';
        info.sentBy = 'INBOX.EVENTS.SENT_BY_YOU';
        info.sentVia = this.chatItemVia(item);
        info.IsOutBound = true;
        info.emailId = metaData.get(metadataEmailIdKey) || '';
        break;
      case EventType.EVENT_TYPE_CAMPAIGN:
        info.icon = 'campaign';
        info.sentBy = 'INBOX.EVENTS.SENT_BY_YOU';
        info.sentVia = this.chatItemVia(item);
        info.IsOutBound = true;
        info.emailId = metaData.get(metadataEmailIdKey) || '';
        info.originEventUrl = metaData.get(metadataCampaignUrlKey)
          ? window.location.origin + metaData.get(metadataCampaignUrlKey)
          : undefined;
        break;
      case EventType.EVENT_TYPE_FORM_SUBMISSION:
        info.svgIcon = 'form-icon-svg';
        info.sentBy = 'INBOX.CHAT.NEW_FORM_SUBMISSION';
        info.sentVia = 'INBOX.CHAT.VIA_WEB_FORM';
        break;
      case EventType.EVENT_TYPE_PHONE_CALL:
        info.icon = 'call';
        info.sentVia = 'INBOX.CHAT.VIA_PHONE';
        if (metaData.get(metadataMissedCallKey) == 'true') {
          info.sentBy = 'INBOX.EVENTS.PHONE.MISSED_CALL';
        }
        info.isAIVoiceCall = metaData.get(metadataCallSourceDrill1Key) == aiVoiceReceptionist;
        info.transcriptJSON = metaData.get(metadataCallTranscriptKey) || '';
        info.callRecordId = metaData.get(metadataCallRecordId) || '';
        if (info.isAIVoiceCall) {
          info.IsOutBound = metaData.get(metadataCallDirectionKey) == 'Outbound';
          info.callFromNumber = metaData.get(metadataCallFromNumberKey) || '';
          info.callToNumber = metaData.get(metadataCallToNumberKey) || '';
          if (info.IsOutBound) {
            info.sentBy = aiVoiceReceptionist;
            info.callFromNumber = `${aiVoiceReceptionist} (${info.callFromNumber})`;
          } else {
            info.sentBy = info.callFromNumber || '';
            info.callToNumber = `${aiVoiceReceptionist} (${info.callToNumber})`;
          }
          info.subtitle = 'INBOX.EVENTS.PHONE.CARD_SUBTITLE';
          info.callDuration = Number(metaData.get(metadataCallDurationKey));
          info.callRecordingUrl = metaData.get(metadataCallRecordingUrlKey) || '';
        }
        break;
      case EventType.EVENT_TYPE_UNDEFINED:
        info.sentVia = this.chatItemVia(item);
        break;
      case EventType.EVENT_TYPE_SMS_SUBSCRIBED:
        info.icon = 'textsms';
        info.sentBy = 'INBOX.LABELS.CONTACT';
        info.sentVia = 'INBOX.CHAT.VIA_SMS';
        info.subtitle = 'INBOX.EVENTS.SMS.SUBSCRIBED_SUBTITLE';
        break;
      case EventType.EVENT_TYPE_SMS_UNSUBSCRIBED:
        info.icon = 'textsms';
        info.sentBy = 'INBOX.LABELS.CONTACT';
        info.sentVia = 'INBOX.CHAT.VIA_SMS';
        info.subtitle = 'INBOX.EVENTS.SMS.UNSUBSCRIBED_SUBTITLE';
        break;
    }
    return info;
  }

  hydrateEventWrapper(event: Event): ConversationEvent {
    return {
      created: event.created,
      channel: event.channel,
      type: event.type,
      event: event,
      eventInfo: this.hydrateEventInfo(event),
      isFirstItem: false,
    };
  }

  hydrateInboxItemWrapper(item: ConversationEvent | ConversationMessage): InboxItem {
    const info = {} as InboxItem;
    info.channel = item.channel;
    info.type = item.type;
    info.item = item;

    if ('eventInfo' in item) {
      info.id = item.event.eventId;
      info.sender = undefined;
      info.created = item.event.happenedAt;
      info.sentVia = item.eventInfo.sentVia;
      info.itemFrom = item.eventInfo.sentBy;
    } else {
      info.id = item.id ?? '';
      info.sender = item.sender;
      info.created = item.created;
      info.sentVia = this.chatItemVia(item);
      info.itemFrom = chatMessageFrom(item) ?? '';
    }
    return info;
  }

  chatItemVia(item: ConversationMessage | Event): string {
    switch (item.channel) {
      case ConversationChannel.CONVERSATION_CHANNEL_GOOGLE_BUSINESS_COMMUNICATIONS:
        return 'INBOX.CHAT.VIA_GOOGLE';
      case ConversationChannel.CONVERSATION_CHANNEL_FACEBOOK:
        return 'INBOX.CHAT.VIA_FACEBOOK';
      case ConversationChannel.CONVERSATION_CHANNEL_SMS:
        return 'INBOX.CHAT.VIA_SMS';
      case ConversationChannel.CONVERSATION_CHANNEL_EMAIL:
        return 'INBOX.CHAT.VIA_EMAIL';
      case ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT:
        return 'INBOX.CHAT.VIA_WEBCHAT';
      case ConversationChannel.CONVERSATION_CHANNEL_INSTAGRAM:
        return 'INBOX.CHAT.VIA_INSTAGRAM';
      case ConversationChannel.CONVERSATION_CHANNEL_WHATSAPP:
        return 'INBOX.CHAT.VIA_WHATSAPP';
      default:
        return '';
    }
  }
}
