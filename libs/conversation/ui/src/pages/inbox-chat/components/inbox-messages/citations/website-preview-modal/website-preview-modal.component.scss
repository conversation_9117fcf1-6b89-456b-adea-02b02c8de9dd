@import 'design-tokens';

// Remove padding and sizing restrictions similar to email preview
::ng-deep .mat-dialog-no-padding {
  max-width: 99vw !important;
}

::ng-deep .mat-dialog-no-padding .mat-mdc-dialog-container {
  padding: 0px;
}

::ng-deep .mat-dialog-no-padding .mat-mdc-dialog-content {
  max-height: 99vh;
  margin: 0;
  padding: 0;
}

// Header styling to match function-call modal
h2[mat-dialog-title] {
  @include text-preset-2; // 20px, 500 weight, 1.2 line-height
  margin: 0;
  padding: 16px 24px;
  background-color: $card-background-color;
  border-bottom: 1px solid $border-color;
  color: $primary-text-color;

  span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

// Main Dialog
.preview-container {
  display: flex;
  flex-direction: column;
  height: 90vh;
  width: 90vw;
  min-width: 960px;
  background-color: $secondary-background-color;
}

.alert-container {
  padding: 16px 24px 0;
}

.iframe-container {
  flex: 1;
  padding: 16px;
  display: flex;
}

.html-preview {
  flex: 1;
  width: 100%;
  border: none;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: $card-background-color;
}

.error-message {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: $secondary-text-color;
}

.error-message mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  color: $error-icon-color;
}

.error-message p {
  @include text-preset-3; // 16px, 400 weight, 1.4 line-height
  margin: 0;
}
