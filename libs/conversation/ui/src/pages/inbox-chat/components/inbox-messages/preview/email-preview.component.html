<mat-dialog-content>
  <div class="wrapper">
    @if (dialogData.htmlContent) {
      <glxy-email-viewer
        [html]="dialogData.htmlContent"
        [showDevicePreviewSizeButtons]="false"
        [openLinksOnNewTabs]="true"
        class="email-preview"
      ></glxy-email-viewer>
    } @else {
      <div
        class="email-preview email-preview-text"
        [innerHTML]="textContent | linky: { email: false } | messageCodeBacktick | glxyMarkdown"
      ></div>
    }
  </div>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-stroked-button mat-dialog-close>
    {{ 'INBOX.CLOSE' | translate }}
  </button>
</mat-dialog-actions>
