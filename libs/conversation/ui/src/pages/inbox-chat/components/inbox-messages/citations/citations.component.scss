@use 'design-tokens' as *;

.expansion-toggle {
  display: flex;
  flex-direction: row;
  align-items: center;
  color: $secondary-font-color;
  font-style: italic;
  justify-content: end;
}

.expansion-toggle:hover {
  cursor: pointer;
}

.expansion-panel {
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
  background-color: $white;
  border-radius: $default-border-radius;
  margin-top: $spacing-2;
  margin-bottom: $spacing-2;
  padding: $spacing-3;
}

.explanation {
  color: $primary-font-color;
  line-height: 1.5;
  font-size: $font-preset-4-size;

  // Only show padding and border when citations exist
  &:has(+ .citation) {
    padding-bottom: $spacing-3;
    border-bottom: 1px solid $border-color;
  }
}

.citation {
  @include text-preset-4;
  display: flex;
  flex-direction: column;
  line-height: 1.2;
  color: $secondary-font-color;
  padding-left: $spacing-2;

  a {
    text-decoration: none;
    display: flex;
    flex-direction: row;
    align-items: center;
    max-width: 100%;
    color: $link-color;

    &:hover {
      color: $link-hover-color;
    }

    div {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      min-width: 0;
    }

    .mat-icon {
      flex-shrink: 0;
      font-size: $font-preset-3-size;
      display: flex;
      align-items: center;
      justify-content: center;
      height: auto;
      margin-left: $spacing-1;
    }
  }

  .content {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    min-width: 0;
    max-width: 100%;
    padding: 0;
  }

  // Simplified file citation styling - just add a file icon before the title
  &.file-citation {
    .file-icon {
      margin-right: $spacing-1;
      margin-left: 0;
    }
  }
}

.internal-only {
  color: $tertiary-font-color;
  display: flex;
  margin-top: $spacing-2;
  font-size: $font-preset-4-size;

  mat-icon {
    font-size: $font-preset-3-size;
    margin-right: $spacing-1;
    display: flex;
    align-items: center;
    justify-content: center;
    height: auto;
  }
}
