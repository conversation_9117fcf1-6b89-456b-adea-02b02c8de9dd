@let eventInfo = event().eventInfo;
@let wrappedEvent = event().event;
@if (eventInfo?.EventType !== EventType.EVENT_TYPE_UNDEFINED) {
  @if (eventInfo?.IsOutBound) {
    <div class="event-container">
      <div class="event-title">{{ eventInfo.title | translate: { channel: eventInfo.channel } }}</div>
      <ng-container *ngTemplateOutlet="eventSubtitle"></ng-container>
      <div class="chat-event-message">{{ eventInfo.content | translate }}</div>

      @if (
        event().channel === ConversationChannel.CONVERSATION_CHANNEL_EMAIL &&
        event().type === EventType.EVENT_TYPE_REVIEW_REQUEST
      ) {
        <a class="event-link" (click)="showOriginalEmail(eventInfo)">
          {{ 'INBOX.EVENTS.VIEW_ORIGINAL_EMAIL' | translate }}
        </a>
      }
      @if (event().type === EventType.EVENT_TYPE_CAMPAIGN) {
        <a class="event-link" href="{{ eventInfo.originEventUrl }}">
          {{ 'INBOX.EVENTS.VIEW_CAMPAIGN' | translate }}
        </a>
      }
      <ng-container *ngTemplateOutlet="call"></ng-container>
    </div>
    <div class="event-footer"></div>
  } @else {
    <div class="inbound-event-container" (click)="onClick($event)">
      <div class="inbound-event-title">{{ eventInfo.title | translate: { channel: eventInfo.channel } }}</div>
      @if (eventInfo.subtitle) {
        <ng-container *ngTemplateOutlet="eventSubtitle"></ng-container>
      }
      @if (eventInfo.content) {
        <div class="inbound-event-message">{{ eventInfo.content }}</div>
      }
      <ng-container *ngTemplateOutlet="call"></ng-container>
    </div>
    <div class="event-footer">
      @if (showEvaluationButtonsComponent()) {
        <inbox-evaluation-buttons class="thumbs" [class.show]="showEvaluationButtons" [message]="wrappedEvent">
        </inbox-evaluation-buttons>
      }
    </div>
  }
}

<ng-template #eventSubtitle>
  <div class="event-subtitle">
    {{ eventInfo.subtitle | translate: { from: eventInfo.callFromNumber, to: eventInfo.callToNumber } }}
  </div>
</ng-template>

<ng-template #call>
  @if (callRecordingUrl()) {
    <audio class="call-recording" preload="metadata" controls src="{{ callRecordingUrl() }}">
      {{ 'INBOX.EVENTS.PHONE.AUDIO_PLAYBACK_ERROR' | translate }}
    </audio>
  }
  @if (eventInfo.transcriptJSON) {
    <a data-action="”view-transcript-clicked”" class="event-link" (click)="openTranscriptModal()">
      {{ 'INBOX.EVENTS.PHONE.VIEW_TRANSCRIPT' | translate }}
    </a>
  }
</ng-template>
