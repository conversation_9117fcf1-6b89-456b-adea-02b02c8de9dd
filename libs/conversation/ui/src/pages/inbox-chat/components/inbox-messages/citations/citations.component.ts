import { Component, computed, inject, input, signal } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { Citation, CitationType, METADATA_KEY_CITATIONS, METADATA_KEY_REASONING } from './citations';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { DomSanitizer } from '@angular/platform-browser';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  CONVERSATION_HOST_APP_INTERFACE_TOKEN,
  CONVERSATION_ROUTES_TOKEN,
  ConversationService,
  getAccountGroupIDFromConversation,
  KeyValuePair,
} from '../../../../../../../core/src';
import { AiKnowledgeModule, BUSINESS_PROFILE_URL_TOKEN, MANAGE_KNOWLEDGE_URL_TOKEN } from '@galaxy/ai-knowledge';
import { MatDialog } from '@angular/material/dialog';
import { FunctionCallModalComponent } from './function-call/function-call-modal.component';
import {
  WebsitePreviewModalComponent,
  WebsitePreviewModalData,
} from './website-preview-modal/website-preview-modal.component';
import { Router } from '@angular/router';

@Component({
  selector: 'inbox-citations-component',
  templateUrl: './citations.component.html',
  styleUrls: ['./citations.component.scss'],
  imports: [MatIconModule, CommonModule, TranslateModule, AiKnowledgeModule],
})
export class CitationsComponent {
  CitationType = CitationType;
  messageMetadata = input<KeyValuePair[]>([]);

  private readonly dialog = inject(MatDialog);
  private readonly sanitizer = inject(DomSanitizer);
  private readonly router = inject(Router);
  private readonly routes = toSignal(inject(CONVERSATION_ROUTES_TOKEN));

  private readonly conversationService = inject(ConversationService);
  private readonly hostAppInterface = inject(CONVERSATION_HOST_APP_INTERFACE_TOKEN);
  private readonly isMultiLocation = this.hostAppInterface.getAppOptions().is_multilocation;

  isExpanded = signal(false);

  toggleCitations() {
    this.isExpanded.update((prev) => !prev);
  }

  // The tokens here assume that knowledge is defined in one place for the App, but this isn't the case for
  // multi-location in Business App, so we need to link to the conversation's account group routes specifically in those case
  private accountGroupIdToken = toSignal(inject(ACCOUNT_GROUP_ID_TOKEN));
  private conversationDetails = toSignal(this.conversationService.currentConversationDetail$);
  private accountGroupId = computed(() => {
    if (this.isMultiLocation) {
      return getAccountGroupIDFromConversation(this.conversationDetails()?.conversation);
    }
    return this.accountGroupIdToken() || '';
  });

  private knowledgeURLToken = toSignal(inject(MANAGE_KNOWLEDGE_URL_TOKEN));
  private knowledgeURL = computed(() => {
    if (this.isMultiLocation) {
      return `/account/location/${this.accountGroupId()}/settings/ai-knowledge`;
    }
    return this.knowledgeURLToken() || '';
  });

  private businessProfileURLToken = toSignal(inject(BUSINESS_PROFILE_URL_TOKEN));
  private businessProfileURL = computed(() => {
    if (this.isMultiLocation) {
      return `/account/location/${this.accountGroupId()}/settings/profile`;
    }
    return this.businessProfileURLToken() || '';
  });

  reasoning = computed(() => {
    if (!this.messageMetadata()) return '';
    for (const m of this.messageMetadata()) {
      if (m.key !== METADATA_KEY_REASONING) continue;
      const parsed = JSON.parse(m.value || '""');
      return this.sanitizer.bypassSecurityTrustHtml(parsed);
    }
    return '';
  });

  citations = computed(() => {
    if (!this.messageMetadata()) return [];

    for (const m of this.messageMetadata()) {
      if (m.key !== METADATA_KEY_CITATIONS) continue;

      if (m.value && m.value.length > 0) {
        const rawCitations = JSON.parse(m.value) as Citation[];
        // Dedupe citations by title since we're not using the content yet
        const dedupedCitations: Citation[] = [];
        for (const citation of rawCitations) {
          if (!dedupedCitations.find((c) => c.title === citation.title)) {
            citation.link = this.buildCitationLink(citation.type ?? 0, citation.link);
            dedupedCitations.push(citation);
          }
        }
        return dedupedCitations;
      }

      break;
    }

    return [];
  });

  private buildCitationLink(type: CitationType, id: string): string {
    switch (type) {
      case CitationType.TYPE_INVALID:
        return id;
      case CitationType.TYPE_WEBSITE:
      case CitationType.TYPE_WEBSITE_V2:
        return id;
      case CitationType.TYPE_BUSINESS_PROFILE:
      case CitationType.TYPE_BUSINESS_PROFILE_V2: {
        return this.businessProfileURL();
      }
      case CitationType.TYPE_USER_INPUT:
      case CitationType.TYPE_USER_INPUT_V2: {
        return `${this.knowledgeURL()}?editSource=${id}`;
      }
      default:
        return id;
    }
  }

  openFunctionCallCitationDetails(c: Citation): void {
    this.dialog.open(FunctionCallModalComponent, {
      width: '950px',
      maxWidth: '100vw',
      maxHeight: '100vh',
      data: c,
      autoFocus: false,
    });
  }

  supportsFilePreview(citation: Citation): boolean {
    return citation.type === CitationType.TYPE_WEBSITE || citation.type === CitationType.TYPE_WEBSITE_V2;
  }

  openWebsitePreview(citation: Citation): void {
    if (!citation.fileUrl || !this.supportsFilePreview(citation)) return;

    this.dialog.open(WebsitePreviewModalComponent, {
      data: {
        fileUrl: citation.fileUrl,
        fileName: citation.title,
        liveUrl: citation.link,
        citationType: citation.type,
      } as WebsitePreviewModalData,
      panelClass: 'mat-dialog-no-padding',
      autoFocus: false,
    });
  }
}
