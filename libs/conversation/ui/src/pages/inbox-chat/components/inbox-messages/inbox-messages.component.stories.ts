import { StoryObj, applicationConfig, moduleMetadata } from '@storybook/angular';
import { Conversation, ConversationChannel, MessageType, ParticipantType, Participant } from '@vendasta/conversation';
import { CONVERSATION_HOST_APP_INTERFACE_TOKEN, ConversationMessage } from '../../../../../../core/src/index';
import { ConversationUIModule } from '../../../../conversation-ui.module';
import { InboxMessagesComponent } from './inbox-messages.component';

export default {
  component: InboxMessagesComponent,
  decorators: [
    applicationConfig({
      providers: [
        {
          provide: CONVERSATION_HOST_APP_INTERFACE_TOKEN,
          useValue: {
            isSenderFromOrganization: (message: ConversationMessage) => {
              return message.sender.participantType === ParticipantType.PARTICIPANT_TYPE_IAM_USER;
            },
          },
        },
      ],
    }),
    moduleMetadata({
      imports: [ConversationUIModule],
    }),
  ],
  args: {
    conversation: {
      conversationId: 'C-123',
      channel: ConversationChannel.CONVERSATION_CHANNEL_INTERNAL,
    },
    messages: [
      {
        messageId: 'M-123',
        conversationId: 'C-123',
        channel: ConversationChannel.CONVERSATION_CHANNEL_INTERNAL,
        body: 'Hello World',
        created: new Date(2023, 8, 15, 18),
        type: MessageType.MESSAGE_TYPE_MESSAGE,
        sender: new Participant({
          name: 'John Doe',
        }),
      },
    ] as ConversationMessage[],
  },
};

type Story = StoryObj<InboxMessagesComponent>;

export const Preview: Story = {};

export const OpenAI: Story = {
  args: {
    conversation: {
      conversationId: 'C-123',
      channel: ConversationChannel.CONVERSATION_CHANNEL_OPENAI,
    } as Conversation,
    messages: [
      {
        messageId: 'M-2',
        conversationId: 'C-123',
        body: 'I would like to know more about your products',
        created: new Date(2023, 8, 15, 18, 0, 10),
        type: MessageType.MESSAGE_TYPE_MESSAGE,
        sender: new Participant({
          participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER,
          participantId: 'ABC',
          channel: ConversationChannel.CONVERSATION_CHANNEL_OPENAI,
        }),
      },
      {
        messageId: 'M-1',
        conversationId: 'C-123',
        body: 'Hello, how may I help you today?',
        created: new Date(2023, 8, 15, 18),
        type: MessageType.MESSAGE_TYPE_MESSAGE,
        sender: new Participant({
          participantType: ParticipantType.PARTICIPANT_TYPE_OPENAI_BOT,
          participantId: '',
          channel: ConversationChannel.CONVERSATION_CHANNEL_OPENAI,
        }),
      },
    ] as ConversationMessage[],
  },
};
