import { ChangeDetectionStrategy, Component, computed, signal, ViewChild, inject, effect } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ConversationChannel, MessageType, PlatformLocation, Participant } from '@vendasta/conversation';
import { filterNullAndUndefined } from '@vendasta/rx-utils';
import { firstValueFrom, Observable, of, catchError } from 'rxjs';
import { ConversationService } from '../../../../core/src/lib/state/conversation.service';
import { ConversationStatelessService } from '../../../../core/src/lib/conversation-stateless.service';
import { InboxService } from '../../../../core/src/lib/inbox.service';
import { ConversationAvailableChannels, MessageInfo } from '../../../../core/src/lib/interface/conversation.interface';
import { InboxMessageTextComponent } from '../inbox-chat/components/inbox-message-text/inbox-message-text.component';
import { buildAIContextMetadata, toFirestoreId } from '../../../../core/src/lib/conversation-utils';
import { ParticipantService } from '../../../../core/src/lib/participant.service';
import { AURORA_AVATAR_SVG_ICON, AiAssistantService } from '@galaxy/ai-assistant';
import { TranslateService } from '@ngx-translate/core';
import { SuggestionChip } from '../inbox-chat/components/inbox-chat-empty-state/inbox-chat-empty-state.component';
import { DEFAULT_OPENAI_BOT_NAME } from '../../../../core/src/lib/inbox.constants';

@Component({
  selector: 'inbox-ai-chat',
  templateUrl: './inbox-ai-chat.component.html',
  styleUrls: ['./inbox-ai-chat.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class InboxAiChatComponent {
  messages$ = this.conversationService.messages$;
  readonly messages = toSignal(this.conversationService.messages$, { initialValue: [] });
  readonly loadingMessages = toSignal(this.conversationService.loadingMessages$);
  readonly currentConversationDetail$ = this.conversationService.currentConversationDetail$;
  readonly currentConversationDetail = toSignal(this.conversationService.currentConversationDetail$);
  readonly loadingConversation = signal(true);
  readonly loading = computed(() => this.loadingConversation() || this.loadingMessages());

  @ViewChild('inboxTextComponent') inboxTextComponent?: InboxMessageTextComponent;

  readonly isImpersonating$: Observable<boolean> = this.conversationService.isUserImpersonated$;

  private readonly participantService = inject(ParticipantService);
  private readonly translateService = inject(TranslateService);
  private readonly aiAssistantService = inject(AiAssistantService);

  protected readonly DEFAULT_OPENAI_BOT_NAME = DEFAULT_OPENAI_BOT_NAME;

  readonly currentAssistant = toSignal(
    this.aiAssistantService.getSystemAssistant().pipe(
      catchError((err) => {
        console.error('Error getting system AI assistant information', err);
        return of(null);
      }),
    ),
    { initialValue: null },
  );

  senderParticipant$ = this.participantService.buildIAMUserParticipant(true);

  readonly assistantAvatarUrl = computed(() => {
    const assistant = this.currentAssistant();
    return assistant?.avatarUrl;
  });

  readonly assistantIconName = computed(() => {
    return AURORA_AVATAR_SVG_ICON;
  });

  readonly hasValidAssistant = computed(() => {
    const assistant = this.currentAssistant();
    return !!assistant;
  });

  readonly aiSuggestionChips = computed(() => {
    const suggestionKeys = [
      'INBOX.AI_CHAT.SUGGESTIONS.HELP_GETTING_STARTED',
      'INBOX.AI_CHAT.SUGGESTIONS.WALK_THROUGH_FEATURE',
      'INBOX.AI_CHAT.SUGGESTIONS.HELP_FIXING_SOMETHING',
    ];

    return suggestionKeys.map(
      (key): SuggestionChip => ({
        label: this.translateService.instant(key),
        message: this.translateService.instant(key),
      }),
    );
  });

  readonly aiTitle = computed(() => {
    const assistantName = this.currentAssistant()?.name;
    return this.translateService.instant('INBOX.AI_CHAT.NO_MESSAGES.TITLE', { assistantName });
  });

  readonly aiDescription = computed(() => {
    const assistantName = this.currentAssistant()?.name;
    return this.translateService.instant('INBOX.AI_CHAT.NO_MESSAGES.DESCRIPTION', { assistantName });
  });

  availableChannels: ConversationAvailableChannels = {
    availableChannels: [ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT],
    preferredChannel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
    channelAvailabilities: [
      {
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        isAvailable: true,
      },
    ],
  };

  readonly isAssistantReady = computed(() => {
    const assistant = this.currentAssistant();
    return !!assistant?.id;
  });

  constructor(
    private readonly conversationService: ConversationService,
    private readonly inboxService: InboxService,
    private readonly conversationStatelessService: ConversationStatelessService,
  ) {
    effect(() => {
      const isReady = this.isAssistantReady();
      const isLoading = this.loadingConversation();

      if (isReady && isLoading) {
        this.startConversation();
      }
    });
  }

  async startConversation(): Promise<void> {
    const assistant = this.currentAssistant();
    if (!assistant?.id) {
      console.error('Assistant not available');
      this.loadingConversation.set(false);
      return;
    }

    try {
      const aiParticipant = await firstValueFrom(
        this.participantService.buildAIAssistantParticipant(assistant.id).pipe(
          catchError((err) => {
            console.error('Error building AI participant', err);
            return of(null);
          }),
        ),
      );

      if (!aiParticipant) {
        console.error('AI Participant could not be built');
        this.loadingConversation.set(false);
        return;
      }

      const participants = [await firstValueFrom(this.senderParticipant$), aiParticipant] as Participant[];

      const resp = await firstValueFrom(
        this.conversationStatelessService.createConversation(
          participants,
          ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
          PlatformLocation.PLATFORM_LOCATION_PARTNER_CENTER,
        ),
      );

      this.conversationService.setCurrentFirestoreConversationId(
        toFirestoreId(resp?.conversation?.conversationId ?? ''),
      );
    } catch (error) {
      console.warn('Error to get or create the AI conversation: ', error);
    } finally {
      this.loadingConversation.set(false);
    }
  }

  async sendMessage(messageInfo: MessageInfo): Promise<void> {
    const currentConversationDetail = await firstValueFrom(
      this.currentConversationDetail$.pipe(filterNullAndUndefined()),
    );
    if (currentConversationDetail.conversation.channel !== ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT) {
      // Just in case the first non-null conversation we load isn't an AI conversation
      // we don't want messages intended for AI appearing in other conversations
      return;
    }

    // Add current page URL to metadata for AI context
    const metadata = buildAIContextMetadata();

    await this.conversationService.sendAndStageMessage(
      currentConversationDetail.conversation.conversationId,
      MessageType.MESSAGE_TYPE_MESSAGE,
      messageInfo.text!,
      messageInfo.channel,
      this.inboxService.platformLocation,
      messageInfo.attachments,
      undefined,
      undefined,
      metadata,
    );
    this.inboxTextComponent?.clearText?.();
  }

  async sendSuggestionMessage(message: string): Promise<void> {
    const messageInfo: MessageInfo = {
      text: message,
      channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
      attachments: [],
    };

    await this.sendMessage(messageInfo);
  }
}
