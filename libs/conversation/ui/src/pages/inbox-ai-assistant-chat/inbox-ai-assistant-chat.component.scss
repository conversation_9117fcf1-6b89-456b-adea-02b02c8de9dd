@use 'design-tokens' as *;
@use '@angular/material' as mat;
@use 'sass:map';

$page-border: 1px;

:host {
  display: flex;
  flex-direction: column;

  overflow: hidden;
  container-type: inline-size;

  --viewport-height: 100svh;

  --height-of-other-elements: calc(
    #{$atlas-bar-height} + env(safe-area-inset-bottom) + env(safe-area-inset-top) + #{$page-border}
  );

  .glxy-page {
    min-height: calc(var(--viewport-height, 100vh) - var(--height-of-other-elements));
    max-height: calc(var(--viewport-height, 100vh) - var(--height-of-other-elements));
  }

  ::ng-deep {
    .glxy-page-main-content {
      overflow: hidden;
      max-height: 100%;
      min-height: 100%;
      display: flex;
      flex-grow: 1;
    }
  }
}

@container (inline-size <= #{$media--tablet-minimum}) {
  :host {
    ::ng-deep {
      .glxy-page-action-container {
        padding-right: $spacing-3;
      }
    }
  }

  .configure-button {
    min-width: min-content;
    margin-right: 0;

    mat-icon {
      margin-inline: 0;
    }

    .label {
      display: none;
    }
  }
}

.inbox-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.inbox-panes {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  overflow: hidden;
}

.inbox-left-pane {
  width: 282px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  border-right: 1px solid $border-color;
  overflow: auto;
}

.info-icon {
  display: block;
}

.assistant-select-field {
  span {
    font-size: $font-preset-4-size;
  }
}
