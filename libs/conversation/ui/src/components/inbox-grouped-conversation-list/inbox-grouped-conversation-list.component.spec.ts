import { TestBed } from '@angular/core/testing';
import { InboxGroupedConversationListComponent } from './inbox-grouped-conversation-list.component';
import { ConversationService } from '../../../../core/src/lib/state/conversation.service';
import { InboxService } from '../../../../core/src/lib/inbox.service';
import { ConversationListService } from '../../../../core/src/lib/state/conversation-list.service';
import { AiAssistantConversationService } from '../../../../core/src/lib/state/ai-assistant-conversation.service';
import { ParticipantService } from '../../../../core/src/lib/participant.service';
import { FirestoreService } from '../../../../core/src/lib/firestore.service';
import { ViewService } from '../../../../core/src/lib/view.service';
import { ActivatedRoute } from '@angular/router';
import { DestroyRef } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  CONVERSATION_PLATFORM_LOCATION_TOKEN,
  CONVERSATION_ROUTES_TOKEN,
  GROUP_ID_TOKEN,
  PARTNER_ID_TOKEN,
  USER_ID_TOKEN,
} from '../../../../core/src/lib/tokens';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { DEFAULT_LANGUAGE, TranslateStore, USE_DEFAULT_LANG, USE_EXTEND, USE_STORE } from '@ngx-translate/core';
import { LEXICON_DISABLE_OTW, LEXICON_FILE_FORMAT } from '@galaxy/lexicon';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import {
  GetMultiConversationDetailsResponseDetailedConversation as ConversationDetail,
  PlatformLocation,
} from '@vendasta/conversation';
import { groupConversations, GroupedConversations, TimeRangeGroupLabels } from './util';
import dayjs from 'dayjs';
import { ConversationStatelessService } from '../../../../core/src/lib/conversation-stateless.service';

// IntersectionObserver isn't available in test environment
const mockIntersectionObserver = jest.fn();
mockIntersectionObserver.mockReturnValue({
  observe: (): null => null,
  unobserve: (): null => null,
  disconnect: (): null => null,
});
window.IntersectionObserver = mockIntersectionObserver;

interface TestSetupConfig {
  loadingConversations?: boolean;
  emptyConversations?: boolean;
  conversations?: any[];
  showInboxViews?: boolean;
  routes?: { getAiAssistantChatPath: (id: string) => string };
}

interface ParamMap {
  get(name: string): string | null;
}

async function setupTest(config: TestSetupConfig = {}) {
  const {
    loadingConversations = false,
    emptyConversations = false,
    conversations = [],
    showInboxViews = true,
    routes = { getAiAssistantChatPath: (id: string) => `/test-path/${id}` },
  } = config;

  const conversationService = {
    conversationSelected: jest.fn(),
    setCurrentFirestoreConversationId: jest.fn(),
  };

  const conversationStatelessService = {
    getMultiConversationDetails: jest.fn().mockReturnValue(of([])),
    getConversationDetail: jest.fn().mockReturnValue(of(null)),
  };

  const conversationListService = {
    setAssistantId: jest.fn(),
    updateFilters: jest.fn(),
    loadingConversations$: new BehaviorSubject(loadingConversations),
    emptyConversations$: new BehaviorSubject(emptyConversations),
    loadMoreConversations: jest.fn(),
    conversations$: new BehaviorSubject(conversations),
  } as unknown as ConversationListService;

  const inboxService = {
    showInboxViews$: new BehaviorSubject<boolean>(showInboxViews),
  };

  const participantService = {
    getParticipant: jest.fn(),
    getParticipants: jest.fn().mockReturnValue(of([])),
    participantsByConversationId$: of(new Map()),
    currentParticipant$: new BehaviorSubject(null),
    organizationId$: new BehaviorSubject('test-org'),
  };

  const firestoreService = {
    collection: jest.fn().mockReturnValue({
      valueChanges: jest.fn().mockReturnValue(of([])),
      stateChanges: jest.fn().mockReturnValue(of([])),
      get: jest.fn().mockReturnValue(of([])),
    }),
    setupBaseConversationsQuery: jest.fn().mockReturnValue({}),
  };

  const viewService = {
    getView: jest.fn().mockReturnValue(of(null)),
    updateView: jest.fn(),
  };

  const destroyRef = {
    onDestroy: jest.fn(),
  };

  const assistantId$$ = new BehaviorSubject('ASSISTANT-123');

  const aiConversationService = {
    aiAssistantId$: assistantId$$,
    startConversation: jest.fn(),
  };

  const activatedRoute = {
    paramMap: new BehaviorSubject<ParamMap>({ get: () => null }),
    queryParamMap: new BehaviorSubject<ParamMap>({ get: () => null }),
  };

  await TestBed.configureTestingModule({
    imports: [
      InboxGroupedConversationListComponent,
      HttpClientTestingModule,
      TranslateTestingModule.withTranslations({}),
    ],
    providers: [
      { provide: ConversationService, useValue: conversationService },
      { provide: ConversationStatelessService, useValue: conversationStatelessService },
      { provide: InboxService, useValue: inboxService },
      { provide: ConversationListService, useValue: conversationListService },
      { provide: AiAssistantConversationService, useValue: aiConversationService },
      { provide: ParticipantService, useValue: participantService },
      { provide: FirestoreService, useValue: firestoreService },
      { provide: ViewService, useValue: viewService },
      { provide: DestroyRef, useValue: destroyRef },
      { provide: ActivatedRoute, useValue: activatedRoute },
      { provide: CONVERSATION_PLATFORM_LOCATION_TOKEN, useValue: PlatformLocation.PLATFORM_LOCATION_PARTNER_CENTER },
      { provide: ACCOUNT_GROUP_ID_TOKEN, useValue: new BehaviorSubject('test-account') },
      { provide: PARTNER_ID_TOKEN, useValue: new BehaviorSubject('test-partner') },
      { provide: USER_ID_TOKEN, useValue: new BehaviorSubject('test-user') },
      { provide: CONVERSATION_ROUTES_TOKEN, useValue: new BehaviorSubject(routes) },
      { provide: GROUP_ID_TOKEN, useValue: new BehaviorSubject('') },
      { provide: TranslateStore, useValue: TranslateStore },
      { provide: LEXICON_DISABLE_OTW, useValue: {} },
      { provide: LEXICON_FILE_FORMAT, useValue: {} },
      { provide: USE_DEFAULT_LANG, useValue: true },
      { provide: USE_STORE, useValue: true },
      { provide: USE_EXTEND, useValue: true },
      { provide: DEFAULT_LANGUAGE, useValue: '' },
    ],
  }).compileComponents();

  const fixture = TestBed.createComponent(InboxGroupedConversationListComponent);
  const component = fixture.componentInstance;
  fixture.detectChanges();

  return {
    fixture,
    component,
    conversationService,
    //@ts-expect-error private/protected field
    conversationListService: component.conversationListService,
    inboxService,
    activatedRoute,
    aiConversationService,
    assistantId$$,
  };
}

describe('InboxGroupedConversationListComponent', () => {
  it('should create', async () => {
    const { component } = await setupTest();
    expect(component).toBeTruthy();
  });

  it('should use view=closed query parameter to set isOpen to false', async () => {
    const { activatedRoute, conversationListService, assistantId$$ } = await setupTest();
    const updateFiltersSpy = jest.spyOn(conversationListService, 'updateFilters');

    // Then simulate query parameter change for anonymous view
    activatedRoute.queryParamMap.next({
      get: (name: string) => (name === 'view' ? 'closed' : null),
    });
    // First ensure no AI assistant ID is set
    assistantId$$.next('ASSISTANT-isOpen-false');

    expect(updateFiltersSpy).toHaveBeenCalledWith({
      isPrivate: true,
      isOpen: false,
      isAnonymous: false,
    });
  });

  it('should compute rootConversationPath correctly for AI assistant', async () => {
    const { component, assistantId$$, fixture } = await setupTest();

    // Set AI assistant ID
    assistantId$$.next('test-assistant-id');

    fixture.detectChanges();

    const componentInstance = component as unknown as { rootConversationPath: () => string | undefined };
    expect(componentInstance.rootConversationPath()).toBe('/test-path/test-assistant-id');
  });

  it('should show inbox zero when conversations are empty and not loading', async () => {
    const { component } = await setupTest({
      loadingConversations: false,
      emptyConversations: true,
    });

    const componentInstance = component as unknown as { showInboxZero$: Observable<boolean> };
    componentInstance.showInboxZero$.subscribe((showZero) => {
      expect(showZero).toBe(true);
    });
  });

  it('should not show inbox zero when loading', async () => {
    const { component } = await setupTest({
      loadingConversations: true,
      emptyConversations: true,
    });

    const componentInstance = component as unknown as { showInboxZero$: Observable<boolean> };
    componentInstance.showInboxZero$.subscribe((showZero) => {
      expect(showZero).toBe(false);
    });
  });

  it('should attempt to create conversation when AI assistant ID is present', async () => {
    const { component, aiConversationService, assistantId$$, fixture } = await setupTest();

    // Set AI assistant ID
    assistantId$$.next('test-assistant-id');
    fixture.detectChanges();

    await component.createConversation();
    expect(aiConversationService.startConversation).toHaveBeenCalled();
  });
});

describe('groupConversations', () => {
  it('should group conversations into today, yesterday, and older', () => {
    const today = dayjs('2024-02-20T10:00:00Z');
    const yesterday = dayjs('2024-02-19T15:00:00Z');
    const sixDaysAgo = dayjs('2024-02-14T09:00:00Z');
    const lastWeek = dayjs('2024-02-13T09:00:00Z');
    const twoWeeksAgo = dayjs('2024-02-06T14:00:00Z');
    const twentyEightDaysAgo = dayjs('2024-01-22T14:00:00Z');
    const thirtyOneDaysAgo = dayjs('2024-01-19T14:00:00Z');

    const conversations = [
      { conversation: { conversationId: '1', created: today.toDate() } },
      { conversation: { conversationId: '2', created: today.toDate() } },
      { conversation: { conversationId: '3', created: yesterday.toDate() } },
      { conversation: { conversationId: '4', created: sixDaysAgo.toDate() } },
      { conversation: { conversationId: '5', created: lastWeek.toDate() } },
      { conversation: { conversationId: '6', created: twoWeeksAgo.toDate() } },
      { conversation: { conversationId: '7', created: twoWeeksAgo.toDate() } },
      { conversation: { conversationId: '8', created: twentyEightDaysAgo.toDate() } },
      { conversation: { conversationId: '9', created: thirtyOneDaysAgo.toDate() } },
    ] as ConversationDetail[];

    const groups = groupConversations(conversations, today);

    // Should have three groups: Today, Yesterday, and Older
    expect(groups.length).toBe(5);

    // Check Today group
    const todayGroup = groups.find((g: GroupedConversations) => g.label === TimeRangeGroupLabels.Today);
    expect(todayGroup).toBeDefined();
    expect(todayGroup?.cs.length).toBe(2);
    expect(todayGroup?.cs.map((c) => c.conversation.conversationId).sort()).toEqual(['1', '2']);

    // Check Yesterday group
    const yesterdayGroup = groups.find((g: GroupedConversations) => g.label === TimeRangeGroupLabels.Yesterday);
    expect(yesterdayGroup).toBeDefined();
    expect(yesterdayGroup?.cs.length).toBe(1);
    expect(yesterdayGroup?.cs[0].conversation.conversationId).toBe('3');

    // Check Yesterday group
    const last7DaysGroup = groups.find((g: GroupedConversations) => g.label === TimeRangeGroupLabels.Last7Days);
    expect(last7DaysGroup).toBeDefined();
    expect(last7DaysGroup?.cs.length).toBe(1);
    expect(last7DaysGroup?.cs[0].conversation.conversationId).toBe('4');

    // Check last-30-days group
    const last30DaysGroup = groups.find((g: GroupedConversations) => g.label === TimeRangeGroupLabels.Last30Days);
    expect(last30DaysGroup).toBeDefined();
    expect(last30DaysGroup?.cs.length).toBe(4);
    expect(last30DaysGroup?.cs.map((c) => c.conversation.conversationId).sort()).toEqual(['5', '6', '7', '8']);

    // Check Older group
    const olderGroup = groups.find((g: GroupedConversations) => g.label === TimeRangeGroupLabels.Older);
    expect(olderGroup).toBeDefined();
    expect(olderGroup?.cs.length).toBe(1);
    expect(olderGroup?.cs.map((c) => c.conversation.conversationId).sort()).toEqual(['9']);
  });

  it('should handle empty conversations array', () => {
    const now = dayjs();
    const groups = groupConversations([], now);
    expect(groups).toEqual([]);
  });

  it('should handle all conversations from same day', () => {
    const now = dayjs('2024-03-20T12:00:00Z');
    const today1 = dayjs('2024-03-20T10:00:00Z');
    const today2 = dayjs('2024-03-20T11:00:00Z');

    const conversations = [
      { conversation: { conversationId: '1', created: today1.toDate() } },
      { conversation: { conversationId: '2', created: today2.toDate() } },
    ] as ConversationDetail[];

    const groups = groupConversations(conversations, now);

    expect(groups.length).toBe(1);
    expect(groups[0].label).toBe(TimeRangeGroupLabels.Today);
    expect(groups[0].cs.length).toBe(2);
  });
});
