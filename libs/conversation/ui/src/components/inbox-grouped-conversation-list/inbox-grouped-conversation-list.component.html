<div class="conversation-list">
  <div class="conversation-list-title">
    <div>{{ 'INBOX.CHAT_HISTORY' | translate }}</div>
    <div>
      <button mat-icon-button class="title-button" (click)="createConversation()">
        <mat-icon class="title-button-icon">add-comment</mat-icon>
      </button>
    </div>
  </div>
  @for (group of groupedConversations$ | async; track group.label) {
    <div class="group-title">{{ group.label | translate }}</div>
    @for (conversationDetail of group.cs; track trackConversationDetailsById(conversationDetail)) {
      <div class="conversation">
        @if (conversationDetail) {
          <a
            [routerLink]="getConversationPath(conversationDetail?.conversation?.conversationId)"
            [queryParams]="{ inboxPrefilledMessage: null }"
            queryParamsHandling="merge"
            routerLinkActive="active-conversation"
            (click)="conversationSelected(true)"
            data-action="clicked-conversation"
          >
            <div
              [ngClass]="{ 'unread-font-weight': (conversationDetail.conversation | conversationUnseen | async) }"
              class="conversation-detail-container"
            >
              <div class="conversation-indicator-selected"></div>
              <inbox-conversation-preview [conversationDetail]="conversationDetail"></inbox-conversation-preview>
            </div>
          </a>
        }
      </div>
    }
  }
  <glxy-infinite-scroll-trigger (isVisible)="loadMoreConversations()" />
  @if ((loadingConversations$ | async) === true) {
    <glxy-loading-spinner />
  }
</div>

<inbox-views-empty *ngIf="showInboxZero$ | async"></inbox-views-empty>
