import { ConversationDetail } from '../../../../core/src/lib/interface/conversation.interface';
import dayjs from 'dayjs';

export const TimeRangeGroupLabels = {
  Today: 'INBOX.CONVERSATION_DATE_GROUPS.TODAY',
  Yesterday: 'INBOX.CONVERSATION_DATE_GROUPS.YESTERDAY',
  Last7Days: 'INBOX.CONVERSATION_DATE_GROUPS.LAST_7_DAYS',
  Last30Days: 'INBOX.CONVERSATION_DATE_GROUPS.LAST_30_DAYS',
  Older: 'INBOX.CONVERSATION_DATE_GROUPS.OLDER',
} as const;

export type TimeRangeGroupLabel = (typeof TimeRangeGroupLabels)[keyof typeof TimeRangeGroupLabels];

export type GroupedConversations = {
  label: TimeRangeGroupLabel;
  cs: ConversationDetail[];
};

export function groupConversations(conversations: ConversationDetail[], now = dayjs()): GroupedConversations[] {
  if (!conversations || conversations.length === 0) {
    return [];
  }

  const today = now;
  const yesterday = now.subtract(1, 'day');
  const sevenDaysAgo = now.subtract(7, 'day');
  const thirtyDaysAgo = now.subtract(30, 'day');

  // Initialize empty groups
  const todayGroup: ConversationDetail[] = [];
  const yesterdayGroup: ConversationDetail[] = [];
  const last7DaysGroup: ConversationDetail[] = [];
  const last30DaysGroup: ConversationDetail[] = [];
  const olderGroup: ConversationDetail[] = [];

  for (const c of conversations) {
    const time = c.message?.created ?? c.event?.happenedAt ?? c.conversation.created;
    if (!time) continue;

    const messageDate = dayjs(time);
    if (messageDate.isSame(today, 'day')) {
      todayGroup.push(c);
    } else if (messageDate.isSame(yesterday, 'day')) {
      yesterdayGroup.push(c);
    } else if (messageDate.isAfter(sevenDaysAgo)) {
      last7DaysGroup.push(c);
    } else if (messageDate.isAfter(thirtyDaysAgo)) {
      last30DaysGroup.push(c);
    } else {
      olderGroup.push(c);
    }
  }

  const groups: GroupedConversations[] = [];
  if (todayGroup.length > 0) {
    groups.push({ label: TimeRangeGroupLabels.Today, cs: todayGroup });
  }
  if (yesterdayGroup.length > 0) {
    groups.push({ label: TimeRangeGroupLabels.Yesterday, cs: yesterdayGroup });
  }
  if (last7DaysGroup.length > 0) {
    groups.push({ label: TimeRangeGroupLabels.Last7Days, cs: last7DaysGroup });
  }
  if (last30DaysGroup.length > 0) {
    groups.push({ label: TimeRangeGroupLabels.Last30Days, cs: last30DaysGroup });
  }
  if (olderGroup.length > 0) {
    groups.push({ label: TimeRangeGroupLabels.Older, cs: olderGroup });
  }

  return groups;
}
