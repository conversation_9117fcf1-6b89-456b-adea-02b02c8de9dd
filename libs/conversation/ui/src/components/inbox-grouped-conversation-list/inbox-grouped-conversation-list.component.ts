import { Component, computed, DestroyRef, inject, input, Signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, RouterLink, RouterLinkActive } from '@angular/router';
import { combineLatest, filter, Observable, pairwise } from 'rxjs';
import { map, shareReplay } from 'rxjs/operators';
import { ConversationListService } from '../../../../core/src/lib/state/conversation-list.service';
import { ConversationService } from '../../../../core/src/lib/state/conversation.service';
import { InboxService } from '../../../../core/src/lib/inbox.service';
import { ConversationDetail } from '../../../../core/src/lib/interface/conversation.interface';
import { toFirestoreId } from '../../../../core/src/lib/conversation-utils';
import { AiAssistantConversationService } from '../../../../core/src/lib/state/ai-assistant-conversation.service';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  CONVERSATION_ROUTES_TOKEN,
  PARTNER_ID_TOKEN,
  USER_ID_TOKEN,
} from '../../../../core/src/lib/tokens';
import { CommonModule } from '@angular/common';
import { ConversationPipesModule } from '../../../../pipes';
import { ConversationUIModule } from '../../conversation-ui.module';
import { GalaxyInfiniteScrollTriggerModule } from '@vendasta/galaxy/infinite-scroll-trigger';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { TranslateModule } from '@ngx-translate/core';
import { MatIcon } from '@angular/material/icon';
import { MatIconButton } from '@angular/material/button';
import { InboxViewsEmptyComponent } from '../inbox-views-empty/inbox-views-empty.component';
import { groupConversations, GroupedConversations } from './util';
import { filterNullAndUndefined } from '@vendasta/rx-utils';

@Component({
  selector: 'inbox-grouped-conversation-list',
  imports: [
    CommonModule,
    ConversationPipesModule,
    ConversationUIModule,
    GalaxyInfiniteScrollTriggerModule,
    GalaxyLoadingSpinnerModule,
    ReactiveFormsModule,
    RouterLinkActive,
    TranslateModule,
    RouterLink,
    MatIcon,
    MatIconButton,
    InboxViewsEmptyComponent,
  ],
  templateUrl: './inbox-grouped-conversation-list.component.html',
  styleUrl: './inbox-grouped-conversation-list.component.scss',
})
export class InboxGroupedConversationListComponent {
  conversationPath = input<string | undefined>(undefined);

  private readonly routes = toSignal(inject(CONVERSATION_ROUTES_TOKEN));
  private readonly destroyRef = inject(DestroyRef);
  private readonly activeRoute = inject(ActivatedRoute);
  private readonly inboxService = inject(InboxService);
  private readonly conversationService = inject(ConversationService);
  private readonly conversationListService = inject(ConversationListService);
  private readonly aiConversationService = inject(AiAssistantConversationService);

  protected readonly userId$ = inject(USER_ID_TOKEN);
  protected readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  protected readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN);
  protected readonly showInboxViews$ = this.inboxService.showInboxViews$;

  readonly loadingConversations$ = this.conversationListService.loadingConversations$;
  readonly emptyConversations$ = this.conversationListService.emptyConversations$;
  readonly showInboxZero$ = combineLatest([this.emptyConversations$, this.loadingConversations$]).pipe(
    map(([empty, loading]) => empty && !loading),
  );

  readonly rootConversationPath: Signal<string | undefined>;
  readonly groupedConversations$: Observable<GroupedConversations[]>;

  private readonly aiAssistantId$ = this.aiConversationService.aiAssistantId$;
  readonly aiAssistantId = toSignal(this.aiAssistantId$);

  constructor() {
    this.rootConversationPath = computed(() => {
      const assistantId = this.aiAssistantId();
      const routes = this.routes();
      if (assistantId && routes?.getAiAssistantChatPath) {
        return `${routes.getAiAssistantChatPath(assistantId)}`;
      }
      return this.conversationPath();
    });

    // Select the most recent conversation when conversations first load
    combineLatest([
      this.conversationListService.conversations$.pipe(filterNullAndUndefined()),
      this.conversationService.currentFirestoreConversationId$,
      this.conversationListService.loadingConversations$.pipe(pairwise()),
    ])
      .pipe(
        takeUntilDestroyed(),
        filter(([conversations, id, [prevLoading, loading]]): boolean => {
          if (!prevLoading || loading) {
            return false;
          }
          return Boolean(id === '' && conversations && conversations.length > 0);
        }),
        map(([conversations]) => {
          return toFirestoreId(conversations[0].conversation.conversationId);
        }),
      )
      .subscribe((id) => {
        this.conversationService.setCurrentFirestoreConversationId(id);
      });

    this.groupedConversations$ = this.conversationListService.conversations$.pipe(
      map((conversations: ConversationDetail[] | null): GroupedConversations[] => {
        return groupConversations(conversations || []);
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.activeRoute.queryParamMap.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (queryParams) => {
        this.conversationListService.updateFilters({
          isPrivate: true,
          isOpen: queryParams.get('view') !== 'closed',
          isAnonymous: false,
        });
      },
    });
  }

  getConversationPath(id?: string) {
    if (!id) {
      return this.rootConversationPath();
    }
    return this.rootConversationPath() + `/${toFirestoreId(id)}`;
  }

  conversationSelected(selected: boolean): void {
    this.conversationService.conversationSelected(selected);
  }

  trackConversationDetailsById(conversationDetail: ConversationDetail): string | undefined {
    return conversationDetail?.conversation?.conversationId;
  }

  loadMoreConversations(): void {
    this.conversationListService.loadMoreConversations();
  }

  createConversation() {
    return this.aiConversationService.startConversation();
  }
}
