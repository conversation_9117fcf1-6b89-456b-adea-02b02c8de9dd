@use 'sass:math';
@use 'design-tokens' as *;
@use '../../styles/breaks.scss' as *;

:host {
  flex-grow: 1;
  overflow: hidden;
  display: flex;
  flex-flow: column;
  background: $card-background-color;
  position: relative;

  a:hover {
    text-decoration: none;
    background: $row-hover-bg-color;
  }
}

.conversation-list {
  flex-grow: 1;
  padding-bottom: $spacing-3;
  overflow-y: auto;

  .conversation-list-title {
    padding: $spacing-3;
    @include text-preset-3;

    display: flex;
    justify-content: space-between;
    align-items: center;

    .title-button {
      display: flex;
      justify-content: center;
      align-items: center;
      .title-button-icon {
        font-size: $font-preset-2-size;
        width: $font-preset-2-size;
        height: $font-preset-2-size;
        color: $primary-color;
      }
    }
  }

  .conversation {
    display: flex;
    flex-direction: column;
    flex-flow: column;
    padding-left: $spacing-2;

    a {
      display: flex;
      padding: $spacing-2 $spacing-3 $spacing-2;
      color: $primary-text-color;
    }

    inbox-conversation-preview {
      font-size: $font-preset-4-size;
      color: $secondary-text-color;
      min-height: 24px;
      max-height: 42px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      word-break: break-all;
    }
  }
}

.unread-font-weight {
  font-weight: 550;
}

.group-title {
  color: $secondary-text-color;
  padding: $spacing-2 0 $spacing-1 $spacing-3;
  font-weight: 700;
}

.conversation-indicator-selected {
  width: $spacing-1;
  background-color: #64b5f6;
  border-radius: 2px;
  opacity: 0;
  padding-right: $spacing-1;
  margin-right: $spacing-2;
}

.active-conversation .conversation-indicator-selected {
  opacity: 1;
}

.conversation-detail-container {
  display: flex;
}
