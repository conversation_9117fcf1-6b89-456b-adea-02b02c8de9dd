@use 'design-tokens' as dt;
@use '@angular/material' as mat;
@use 'utilities' as ut;

// global styles for our shadow DOM
@include mat.elevation-classes();
@include mat.app-background();
@import 'base-themes/glxy-light-theme';

.button-container {
  position: relative;
}

.status-container {
  position: absolute;
  pointer-events: none;
  top: 0px;
  right: 0px;
  z-index: 10;
  visibility: hidden;
  opacity: 0;
  transition:
    opacity 0.35s,
    visibility 0.35s;
}

.visible {
  opacity: 1;
  visibility: visible;
}

.webchat-icon {
  color: var(--icon-color);
  --mdc-fab-container-shape: 50%;

  // make the icon slightly larger, nudge it down a bit so the bubble is
  // centered, flip it horizontally so the stem is pointing to the bottom
  // right or left corner of the window
  &--right {
    transform: scale(1.2) translateX(-0.5px) translateY(1.5px) scaleX(-1);
  }
  &--left {
    transform: scale(1.2) translateX(0.5px) translateY(1.5px);
  }
}
