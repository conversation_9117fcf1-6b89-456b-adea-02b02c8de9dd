<div class="mobile-overlay-area" (click)="onClickClose()" *ngIf="fullView()"></div>
<div class="chat-box-header" [ngClass]="{ 'chat-box-header-clickable': !fullView() }" (click)="onClickHeader()">
  <div class="chat-box-avatar" *ngIf="!!assistantAvatarUrl()">
    <glxy-avatar [src]="assistantAvatarUrl()" [name]="assistantName()"></glxy-avatar>
  </div>
  <div class="chat-box-header-content">
    <h3 class="chat-box-title">
      {{ welcomeMessage() || 'INBOX.WEBCHAT.SETTINGS.WEBCHAT_WELCOME_MESSAGE_PLACEHOLDER' | translate }}
    </h3>
    <div class="chat-status">
      <webchat-status></webchat-status>
      <div class="chat-box-status-title">
        {{ 'INBOX.WEBCHAT.WIDGET.WE_RESPOND_IMMEDIATELY' | translate }}
      </div>
    </div>
  </div>
  <button
    mat-icon-button
    matSuffix
    class="close-button"
    (click)="onClickClose()"
    [attr.aria-label]="'INBOX.WEBCHAT.WIDGET.CLOSE_WIDGET' | translate"
  >
    <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="currentColor">
      <path d="M0 0h24v24H0z" fill="none" />
      <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
    </svg>
  </button>
</div>

<div class="chat-messages">
  <glxy-chat-container #chatContainer [animationDuration]="200">
    <glxy-chat-message-group
      *ngIf="isReplying()"
      [isTyping]="true"
      [showProfilePic]="!!assistantAvatarUrl()"
      [messageFrom]="assistantName()"
      [sentFromYou]="false"
      [profilePicUrl]="assistantAvatarUrl()"
    ></glxy-chat-message-group>

    <ng-container *ngFor="let group of messageGroups(); trackBy: trackById">
      <glxy-chat-message-group
        [type]="(group | isSentFromYou: currentParticipantId()) ? 'sent' : 'received'"
        [showProfilePic]="!(group | isSentFromYou: currentParticipantId()) && !!assistantAvatarUrl()"
        [sentFromYou]="group | isSentFromYou: currentParticipantId()"
        [messageFrom]="assistantName()"
        [profilePicUrl]="!(group | isSentFromYou: currentParticipantId()) && assistantAvatarUrl()"
      >
        <ng-container *ngFor="let message of group.messages; trackBy: trackById">
          <glxy-chat-message
            [messageText]="
              message.body === 'INBOX.WEBCHAT.SETTINGS.WEBCHAT_CUSTOM_GREETING_PLACEHOLDER'
                ? (message.body | translate | linky | glxyMarkdown)
                : (message.body | linky | glxyMarkdown)
            "
            [messageStatus]="mapMessageStatus(message)"
            [messageTime]="message.created | date: 'h:mm a'"
            [messageIsMarkdown]="true"
          ></glxy-chat-message>

          @if (message | showBookingAvailability: messages$ | async) {
            <inbox-booking-availability-component
              class="booking-availability"
              [inputMessage]="message"
              (preferredBooking)="handleSetPreferredBooking($event)"
            ></inbox-booking-availability-component>
          }
        </ng-container>
      </glxy-chat-message-group>

      <glxy-chat-divider-text *ngIf="!!group.created" [matTooltip]="group.created | date">
        {{ group.created | date }}
      </glxy-chat-divider-text>
    </ng-container>
  </glxy-chat-container>
</div>

<div class="chat-box-footer" [ngClass]="{ 'mini-view-composer': !fullView() }">
  <glxy-chat-composer
    (keydown)="handleInput($event)"
    [enterKeySendsMessage]="true"
    (send)="sendMessage($event)"
    [textAreaMinRows]="1"
    [textAreaMaxRows]="5"
    [maxlength]="maxMessageLength"
    [customColor]="true"
    [placeholder]="placeholder()"
    [sendText]="'INBOX.WEBCHAT.WIDGET.SEND' | translate"
    [processing]="processing()"
  ></glxy-chat-composer>
  @let partnerName = poweredBy()?.name;
  @let partnerLink = poweredBy()?.url;
  @if (showFooterContent() && !!partnerName) {
    <p class="powered-by">
      @if (!!partnerName && !!partnerLink) {
        <a target="_blank" href="{{ partnerLink }}">
          {{ 'INBOX.WEBCHAT.WIDGET.POWERED_BY' | translate }} {{ partnerName }}
        </a>
      } @else if (!!partnerName) {
        {{ 'INBOX.WEBCHAT.WIDGET.POWERED_BY' | translate }} {{ partnerName }}
      }
    </p>
  }
</div>
