/* eslint-disable @typescript-eslint/no-non-null-assertion */
import {
  Conversation,
  ConversationChannel,
  GlobalParticipantType,
  LastSeenByParticipant,
  SubjectParticipant,
} from '@vendasta/conversation';
import { conversationUnseen, formatUnreadConversationsCount, getFollowViewID } from './conversation-utils';
import { FOLLOWING_VIEW_ID } from './inbox.constants';

describe('conversation-utils', () => {
  let conversation: Conversation;

  beforeEach(() => {
    conversation = {
      conversationId: 'CONVERSATION-ABC',
      externalConversationId: '',
      channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
      latestMsgSentTime: new Date('2021-10-25 08:00:00'),
      latestRelevantActivityTime: new Date('2021-10-25 08:00:00'),
      created: new Date('2021-10-25 08:00:00'),
      updated: new Date('2021-10-25 08:00:00'),
      deleted: new Date('2021-10-25 08:00:00'),
      subjectParticipants: [
        new SubjectParticipant({
          participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_IAM_USER,
          internalParticipantId: 'U-123',
        }),
      ],
    } as Conversation;
  });

  describe('conversationUnseen', () => {
    it('should return true if the participantId is provided, the useLastSeen field is false and the lastSeenByParticipants list is empty', () => {
      const result = conversationUnseen(conversation, 'participant-123');
      expect(result).toEqual(true);
    });
    it('should return true if the participantId is provided, the useLastSeen field is false and the participant is not present in the lastSeenByParticipants list', () => {
      conversation.lastSeenByParticipant = [
        {
          participantId: 'participant-456',
          lastSeenTime: new Date('2022-07-28 08:00:00'),
        },
      ] as LastSeenByParticipant[];
      const result = conversationUnseen(conversation, 'participant-123');
      expect(result).toEqual(true);
    });
    it('should return true if the participantId is provided, the useLastSeen field is false and the lastSeenTime of the participant is less than than latestMsgSentTime', () => {
      conversation.lastSeenByParticipant = [
        {
          participantId: 'participant-123',
          lastSeenTime: new Date('2020-10-28 08:00:00'),
        },
      ] as LastSeenByParticipant[];
      const result = conversationUnseen(conversation, 'participant-123');
      expect(result).toEqual(true);
    });
    it('should return false if the participantId is provided, the useLastSeen field is false and the lastSeenTime of the participant is greater than than latestMsgSentTime', () => {
      conversation.lastSeenByParticipant = [
        {
          participantId: 'participant-123',
          lastSeenTime: new Date('2022-07-28 08:00:00'),
        },
      ] as LastSeenByParticipant[];
      const result = conversationUnseen(conversation, 'participant-123');
      expect(result).toEqual(false);
    });
  });

  describe('getFollowViewID', () => {
    it('should return the correct default id', () => {
      const result = getFollowViewID('P-1234');
      expect(result).toEqual(`P-1234:${FOLLOWING_VIEW_ID}`);
    });
  });

  describe('formatUnreadConversationsCount', () => {
    it('should return an empty string if the input is an invalid value', () => {
      const result = formatUnreadConversationsCount(null!);
      expect(result).toEqual('');
    });
    it('should return an empty string if the input is 0', () => {
      const result = formatUnreadConversationsCount(0);
      expect(result).toEqual('');
    });
    it('should return a string value of the provided input', () => {
      const count = 5;
      const result = formatUnreadConversationsCount(count);
      expect(result).toEqual(count.toString());
    });
    it('should return the limit value with a + symbol if the provided input is equal or greater than the limit', () => {
      const limit = 20;
      const result = formatUnreadConversationsCount(20, limit);
      expect(result).toEqual(`${limit}+`);
    });
    it('should return the limit value with a + symbol if the provided input is greater than the limit', () => {
      const limit = 20;
      const result = formatUnreadConversationsCount(40, limit);
      expect(result).toEqual(`${limit}+`);
    });
  });
});
