import { Injectable, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ConversationChannel, Participant } from '@vendasta/conversation';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { Observable, catchError, firstValueFrom, switchMap, tap } from 'rxjs';
import { toFirestoreId } from './conversation-utils';
import { ConversationStatelessService } from './conversation-stateless.service';
import { InboxService } from './inbox.service';
import { ConversationDetail, SendMessage, SubjectParticipant } from './interface/conversation.interface';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  CONVERSATION_HOST_APP_INTERFACE_TOKEN,
  CONVERSATION_ROUTES_TOKEN,
  PARTNER_ID_TOKEN,
} from './tokens';
import { ViewModeService } from './view-mode.service';
import { ParticipantService } from './participant.service';

@Injectable()
export class InboxNavigationService {
  private readonly conversationStatelessService = inject(ConversationStatelessService);
  private readonly inboxService = inject(InboxService);
  private readonly analyticsService = inject(ProductAnalyticsService);
  private readonly hostAppInterface = inject(CONVERSATION_HOST_APP_INTERFACE_TOKEN);
  private readonly routes = toSignal(inject(CONVERSATION_ROUTES_TOKEN));
  private readonly accountGroupId = toSignal(inject(ACCOUNT_GROUP_ID_TOKEN));
  private readonly partnerId = toSignal(inject(PARTNER_ID_TOKEN));
  private readonly viewModeService = inject(ViewModeService);
  private readonly participantService = inject(ParticipantService);

  private createOrGetConversation(
    subjectParticipants: SubjectParticipant[],
    currentParticipant: Participant,
    channel: ConversationChannel,
  ): Observable<ConversationDetail> {
    return this.hostAppInterface.buildSendMessageParams(currentParticipant, subjectParticipants, channel).pipe(
      switchMap((sendMessage: SendMessage) =>
        this.conversationStatelessService.createConversation(
          sendMessage.participants ?? [],
          sendMessage.channel,
          sendMessage.location,
        ),
      ),
      tap((conversationResp) => {
        if (conversationResp?.conversation) {
          this.analyticsService.trackEvent('inbox', 'send-message', 'create-conversation-success', 0, {
            accountGroupId: this.accountGroupId(),
            partnerId: this.partnerId(),
            location: this.inboxService.platformLocation,
            channel,
          });
        }
      }),
      catchError((error) => {
        this.analyticsService.trackEvent('inbox', 'send-message', 'create-conversation-error', 0, {
          accountGroupId: this.accountGroupId(),
          partnerId: this.partnerId(),
          location: this.inboxService.platformLocation,
          channel,
          message: error?.error?.message,
        });

        throw new Error('Error creating a conversation');
      }),
      switchMap((conversationResp) =>
        this.conversationStatelessService.getConversationDetail(conversationResp.conversation.conversationId),
      ),
    );
  }

  /**
   * Go to or create a conversation between a current participant, and a list of Subject Participants
   * @param {SubjectParticipant[]} subjectParticipants - A list of subject participants
   * @param {Participant} currentParticipant - The current user that is going to the conversation
   * @param {ConversationChannel} channel - The channel of the conversation (reference value).
   * @param {string} prefilledMessage - A prefilled message in Inbox, this only works with the modal
   */
  gotoConversation(
    subjectParticipants: SubjectParticipant[],
    currentParticipant: Participant,
    channel: ConversationChannel,
    prefilledMessage?: string,
  ): void {
    if (!this.routes()?.useModal) {
      this.hostAppInterface.redirectToInternalConversation(currentParticipant, subjectParticipants, channel);
      return;
    }

    this.createOrGetConversation(subjectParticipants, currentParticipant, channel).subscribe((conversationDetails) =>
      this.gotoConversationID(toFirestoreId(conversationDetails.conversation.conversationId), prefilledMessage),
    );
  }

  /**
   * Go to or create a conversation between a current participant, and a list of Subject Participants for a given
   * channel. This method will resolve the current IAM user for the caller.
   * @param {SubjectParticipant[]} subjectParticipants - A list of subject participants
   * @param {ConversationChannel} channel - The channel that the conversation exists in.
   * @param {string} prefilledMessage - A prefilled message in Inbox, this only works with the modal
   */
  async gotoConversationChannel(
    subjectParticipants: SubjectParticipant[],
    channel: ConversationChannel,
    prefilledMessage?: string,
  ): Promise<void> {
    const currentParticipant = await firstValueFrom(this.participantService.buildIAMUserParticipant());
    if (!this.routes()?.useModal) {
      // get first value from currentParticipant$ and redirect to internal conversation
      this.hostAppInterface.redirectToInternalConversation(currentParticipant, subjectParticipants, channel);
      return;
    }

    const conversationDetails = await firstValueFrom(
      this.createOrGetConversation(subjectParticipants, currentParticipant, channel),
    );
    if (!conversationDetails?.conversation) {
      // TODO(warped-tour): better handle if we fail to get the conversation
      console.error('error to get firestore conversation by conversation id; unable to go to conversation');
      return;
    }
    this.gotoConversationID(toFirestoreId(conversationDetails?.conversation?.conversationId), prefilledMessage, true);
  }

  /**
   * Go to a conversation via a conversation ID
   * @param {string} conversationId - A firestore conversation ID
   */
  gotoConversationID(conversationId: string, prefilledMessage?: string, useConversationOverlay?: boolean): void {
    if (useConversationOverlay) {
      this.viewModeService.openConversationOverlay(conversationId, false, prefilledMessage);
    } else {
      this.viewModeService.open(conversationId, prefilledMessage);
    }
  }
}
