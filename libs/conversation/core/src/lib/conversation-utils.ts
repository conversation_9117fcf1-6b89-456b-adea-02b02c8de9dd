import { inject, NgZone } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { Router } from '@angular/router';
import {
  Conversation,
  ConversationChannel,
  GlobalParticipantType,
  Participant,
  PlatformLocation,
  MetadataIdentifier,
} from '@vendasta/conversation';
import { FIRESTORE_CONVERSATION_LIMIT, POSTHOG_CHANNEL_NAME } from './conversation.constants';
import { PlatformLocationToSKey } from './inbox-terms-of-service.service';
import { FOLLOWING_VIEW_ID } from './inbox.constants';
import { ConversationDetail } from './interface/conversation.interface';
import { CONVERSATION_ROUTES_TOKEN } from './tokens';

/**
 * conversation was not seen if the participant isn't in the `lastSeenTimeByParticipantField` or `lastSeenTime` of the participant is less than `latestMsgSentTime`
 * @param {FirestoreConversation} conversation - the conversation
 */
export function conversationUnseen(conversation: Conversation, participantId: string): boolean {
  if (!conversation?.latestRelevantActivityTime) {
    return false;
  }

  if (!participantId) {
    return true;
  } else {
    const lastSeenTimeByParticipant = conversation?.lastSeenByParticipant?.find(
      (lastSeenByParticipant) => lastSeenByParticipant?.participantId === participantId,
    );
    return (
      !lastSeenTimeByParticipant || lastSeenTimeByParticipant?.lastSeenTime < conversation?.latestRelevantActivityTime
    );
  }
}

export function getConversationByContactID(
  contactID: string,
  conversations: ConversationDetail[],
): ConversationDetail | null {
  let contactHasConversation: ConversationDetail | null = null;

  conversationsLoop: for (let i = 0; i < conversations.length; i++) {
    const conversation = conversations[i];
    for (let j = 0; j < conversation.participants.length; j++) {
      const participant = conversation.participants[j];
      if (participant?.internalParticipantId === contactID) {
        contactHasConversation = conversation;
        break conversationsLoop;
      }
    }
  }

  return contactHasConversation;
}

export function getPlatformLocationToSKey(platformLocation: PlatformLocation): PlatformLocationToSKey | undefined {
  switch (platformLocation) {
    case PlatformLocation.PLATFORM_LOCATION_VENDOR_CENTER:
    case PlatformLocation.PLATFORM_LOCATION_PARTNER_CENTER:
    case PlatformLocation.PLATFORM_LOCATION_TASK_MANAGER:
    case PlatformLocation.PLATFORM_LOCATION_SALES_CENTER:
      return { partnerId: 'ALL', feature: 'inbox-platform' };
    case PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP:
      return { partnerId: 'ALL', feature: 'inbox-conversation' };
  }
}

/**
 * Concat two URLs to generate an unique URL with both strings
 * @param {string} entryUrl - entry Url
 * @param {string} nextUrl - next Url
 * @return {string} - composed URL
 */
export function addNextUrl(entryUrl: string, nextUrl: string): string {
  if (!('URL' in window)) {
    return entryUrl + `?nextUrl=${encodeURIComponent(nextUrl)}`;
  }
  const urlObj = new URL(entryUrl);
  const params = new URLSearchParams(urlObj.search);
  params.append('nextUrl', nextUrl);
  urlObj.search = params.toString();
  return urlObj.toString();
}

/**
 * Create a following view id from a participant
 */
export function getFollowViewID(participantID: string): string {
  return participantID + ':' + FOLLOWING_VIEW_ID;
}

export function extractCurrentLocation(platformLocationsEnabled: PlatformLocation[]): PlatformLocation {
  const currentLocation = platformLocationsEnabled?.length > 0 ? platformLocationsEnabled[0] : null;
  if (!currentLocation) {
    throw new Error(
      'You need to set up the platformLocationsEnabled in the ConversationConfig. To fix see documentation in the README of @galaxy/conversation/core',
    );
  }
  return currentLocation;
}

/**
 * Format the number of unread conversations by view to display
 */
export function formatUnreadConversationsCount(count: number, limit = FIRESTORE_CONVERSATION_LIMIT): string {
  if (!count || count === 0) {
    return '';
  }
  return count < limit ? count.toString() : `${limit}+`;
}

const ID_PREFIX = 'CONVERSATION-';

export function toFirestoreId(id: string): string {
  return id.replace(ID_PREFIX, '');
}

export function fromFirestoreId(id: string): string {
  if (id && id.startsWith(ID_PREFIX)) {
    return id;
  }
  return ID_PREFIX + id;
}

export const redirectToConversationFactory = () => {
  const zone = inject(NgZone);
  const router = inject(Router);
  const routes = toSignal(inject(CONVERSATION_ROUTES_TOKEN), { requireSync: true });

  return (conversationId: string) => {
    zone.run(() => {
      router.navigate([routes().root + '/inbox/channel', toFirestoreId(conversationId)], {
        queryParamsHandling: 'merge',
      });
    });
  };
};

/**
 * Build the properties for the send message track event in Posthog
 */
export function buildSendMessageTrackProperties(
  conversationId: string,
  sender: Participant,
  channel: ConversationChannel,
  numberOfFiles: number,
  platformLocation: PlatformLocation,
) {
  return {
    id: conversationId,
    accountGroupId: sender.accountGroupId,
    participantName: sender?.name || '',
    partnerId: sender.partnerId,
    PID_AGID: getPosthogNamespace(sender.partnerId, sender.accountGroupId),
    location: platformLocation,
    channel: getPosthogChannelName(channel),
    type: sender?.participantType,
    numberOfFiles: numberOfFiles,
  };
}

export function getPosthogChannelName(channel: ConversationChannel): string {
  return POSTHOG_CHANNEL_NAME[channel];
}

export function getPosthogNamespace(partnerId: string, accountGroupId: string): string {
  return accountGroupId ? partnerId + '_' + accountGroupId : partnerId;
}

export function isAccountGroupAndCustomerConversation(conversation?: Conversation): boolean {
  // ideally we should be using subjectParticipantKey for this check.
  return (
    !!conversation &&
    conversation.subjectParticipantKey?.subjectParticipants?.some(
      (p) => p.participantType === GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP,
    ) &&
    conversation.subjectParticipantKey?.subjectParticipants?.some(
      (p) => p.participantType === GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_CUSTOMER,
    )
  );
}

export function getAccountGroupIDFromConversation(conversation?: Conversation): string {
  return (
    conversation?.subjectParticipantKey.subjectParticipants.find(
      (subjectParticipant) =>
        subjectParticipant?.participantType === GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP,
    )?.internalParticipantId || ''
  );
}

/**
 * Builds metadata with the current page URL for AI context
 * @returns Metadata array with sender information including current page URL
 */
export function buildAIContextMetadata() {
  return [
    {
      identifier: MetadataIdentifier.METADATA_IDENTIFIER_SENDER,
      data: {
        currentPageUrl: window.location.href,
      },
    },
  ];
}
