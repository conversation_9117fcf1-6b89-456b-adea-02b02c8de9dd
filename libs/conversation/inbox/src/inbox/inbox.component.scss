@use 'design-tokens' as *;
@use '../../../ui/src/styles/breaks' as *;

:host {
  display: block;
  position: relative;
  height: 100%;
  @media print {
    position: inherit;
  }

  .container {
    height: 100%;
  }

  .inbox-sidebar {
    position: fixed;
    top: $atlas-bar-height;
    height: calc(100dvh - $atlas-bar-height);
    max-width: 400px;
    width: 100%;

    ::ng-deep .mat-drawer-inner-container {
      overflow-x: hidden !important;
    }

    @include respond-to(mobile) {
      max-width: 100%;
    }
  }

  mat-sidenav-container {
    height: 100%;
    position: inherit;
    z-index: auto;
  }

  .mat-drawer-content {
    position: inherit;
  }
}

.inbox-modal {
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh; // fallback for older browsers
  height: 100dvh; // use dynamic viewport height for better mobile UX
  overflow: hidden;
  background-color: white;
  transition: opacity 0.5s ease;
}
