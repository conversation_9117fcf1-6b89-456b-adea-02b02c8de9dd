import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  DestroyRef,
  effect,
  HostListener,
  inject,
  Injectable,
  input,
  OnInit,
  output,
  signal,
} from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { ActivatedRoute, MaybeAsync, Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { DefaultWidgetPosition, KnowledgeAppType, Widget } from './widget';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { InboxService } from '../../../../../core/src/lib/inbox.service';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { KnowledgeSource } from '@vendasta/embeddings';
import { AiKnowledgeService, ApplicationKnowledgeComponent } from '@galaxy/ai-knowledge';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { StickyFooterComponent } from '../../../components/sticky-footer/sticky-footer.component';
import { ConversationApiService, MessageType, WidgetPosition } from '@vendasta/conversation';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { GalaxyPageNoAccessUnauthorizedModule } from '@vendasta/galaxy/page-no-access-unauthorized';
import {
  catchError,
  combineLatest,
  EMPTY,
  firstValueFrom,
  map,
  shareReplay,
  startWith,
  switchMap,
  throwError,
} from 'rxjs';
import { ACCOUNT_GROUP_ID_TOKEN, PARTNER_ID_TOKEN, MARKET_ID_TOKEN } from '../../../../../core/src/lib/tokens';
import { ViewModeService } from '../../../../../core/src/lib/view-mode.service';
import { EnableLeadCaptureDialogComponent } from './enable-lead-capture-dialog/enable-lead-capture-dialog.component';
import { AdditionalInstructionsDialogComponent } from './additional-instructions-dialog/additional-instructions-dialog.component';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { ImageUploadComponent } from './image-upload/image-upload.component';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import { AiAssistantReferenceCardComponent } from '@galaxy/ai-assistant';
import { Assistant } from '@vendasta/ai-assistants';
import { GalaxyNavControlService } from '@vendasta/galaxy/nav';
import {
  MessageGroup,
  TEMP_AI_ASSISTANT_PARTICIPANT_ID,
  TEMP_PARTICIPANT_ID,
  WebchatBoxComponent,
} from '../../../../../webchat-widget/src';
import { WebchatButtonPreviewComponent } from '../../../../../webchat-widget/src/components/webchat-button/webchat-button-preview';
import { ConversationMessage } from '../../../../../core/src/lib/interface/conversation.interface';
import { BrandingV2Service, WhitelabelService } from '@galaxy/partner';

const COLOR_REGEX = /^#[a-fA-F0-9]{6}$/;
const ADDITIONAL_INSTRUCTIONS_LIMIT = 10000;
const WELCOME_MESSAGE_LIMIT = 70;
const CUSTOM_GREETING_MESSAGE_LIMIT = 600;

function getContrastYIQ(color: string): string {
  const len = 2;
  const baseHex = 16;

  // remove hash from hexa string
  const hexColor = color.substring(1);

  const r = parseInt(hexColor.substr(0, len), baseHex);
  const g = parseInt(hexColor.substr(2, len), baseHex);
  const b = parseInt(hexColor.substr(4, len), baseHex);
  const yiq = (r * 299 + g * 587 + b * 114) / 1000;
  return yiq >= 128 ? 'dark' : 'light';
}

@Injectable({ providedIn: 'root' })
export class UnsavedChangesGuard {
  private readonly confirmationModal = inject(OpenConfirmationModalService);
  private readonly hasUnsavedChanges = signal(false);

  canDeactivate(): MaybeAsync<boolean> {
    if (this.hasUnsavedChanges()) {
      return this.confirmationModal
        .openModal({
          type: 'warn',
          title: 'INBOX.UNSAVED_CHANGES.TITLE',
          message: 'INBOX.UNSAVED_CHANGES.MESSAGE',
          confirmButtonText: 'INBOX.UNSAVED_CHANGES.CONFIRM',
        })
        .pipe(map((confirmation) => !!confirmation));
    }
    return true;
  }

  notifyStateChanged(hasChanges: boolean): void {
    this.hasUnsavedChanges.set(hasChanges);
  }
}

@Component({
  selector: 'inbox-webchat-form',
  imports: [
    CommonModule,
    MatInputModule,
    MatCardModule,
    ReactiveFormsModule,
    FormsModule,
    MatButtonModule,
    GalaxyFormFieldModule,
    GalaxyLoadingSpinnerModule,
    GalaxyPageNoAccessUnauthorizedModule,
    TranslateModule,
    MatCheckboxModule,
    RouterModule,
    MatIconModule,
    MatButtonToggleModule,
    MatDialogModule,
    MatDividerModule,
    ApplicationKnowledgeComponent,
    StickyFooterComponent,
    GalaxyBadgeModule,
    ImageUploadComponent,
    GalaxyTooltipModule,
    GalaxyAvatarModule,
    AiAssistantReferenceCardComponent,
    WebchatBoxComponent,
    WebchatButtonPreviewComponent,
  ],
  templateUrl: './webchat-form.component.html',
  styleUrls: ['./webchat-form.component.scss'],
})
export class WebchatFormComponent implements OnInit {
  static webChatFormGroupFactory(webchat?: Widget): FormGroup {
    let namespace = webchat?.namespace;
    if (!namespace) {
      const partnerId = toSignal(inject(PARTNER_ID_TOKEN));
      const accountGroupId = toSignal(inject(ACCOUNT_GROUP_ID_TOKEN));
      namespace = accountGroupId() || partnerId();
    }
    return new FormGroup({
      // following are used for web chat form
      name: new FormControl(
        { value: webchat?.name || '', disabled: webchat?.isListingWebChat || false },
        Validators.required,
      ),
      assistantName: new FormControl(webchat?.assistantName || ''),
      assistantAvatarUrl: new FormControl(webchat?.assistantAvatarUrl || ''),
      color: new FormControl(webchat?.color || '#555555', [Validators.required, Validators.pattern(COLOR_REGEX)]),
      welcomeMessage: new FormControl(webchat?.welcomeMessage || '', [Validators.maxLength(WELCOME_MESSAGE_LIMIT)]),
      enableGreetingMessage: new FormControl(webchat?.enableGreetingMessage || false),
      customGreetingMessage: new FormControl(webchat?.customGreetingMessage || '', [
        Validators.maxLength(CUSTOM_GREETING_MESSAGE_LIMIT),
      ]),
      textColor: new FormControl(webchat?.textColor || 'light'),
      accentColor: new FormControl(webchat?.accentColor || '#1976d2', [
        Validators.required,
        Validators.pattern(COLOR_REGEX),
      ]),
      accentTextColor: new FormControl(webchat?.accentTextColor || 'light'),
      isListingWebChat: new FormControl(webchat?.isListingWebChat || false),
      enableLeadCapture: new FormControl(!webchat?.skipContactCapture),
      additionalPrompt: new FormControl(webchat?.additionalPromptInstructions || '', [
        Validators.maxLength(ADDITIONAL_INSTRUCTIONS_LIMIT),
      ]),
      position: new FormControl(webchat?.position || DefaultWidgetPosition),
      showMobileCta: new FormControl(!webchat?.hideMobileCta || true),

      // following are used only to store web chat state for reference (not shown in the form)
      webChatId: new FormControl(webchat?.widgetId || ''),
      namespace: new FormControl(namespace),
    });
  }

  static webChatFormGroupInit(form: FormGroup, webchat: Widget): void {
    if (!form || !webchat) return;

    form.get('name')?.setValue(webchat.name || '');
    if (webchat.isListingWebChat) form.get('name')?.disable(); // note if you enable the whole form this needs to be re-disabled
    form.get('assistantName')?.setValue(webchat.assistantName || '');
    form.get('assistantAvatarUrl')?.setValue(webchat.assistantAvatarUrl || '');
    form.get('color')?.setValue(webchat.color || '#555555');
    form.get('welcomeMessage')?.setValue(webchat.welcomeMessage || '');
    form.get('customGreetingMessage')?.setValue(webchat.customGreetingMessage || '');
    form.get('textColor')?.setValue(webchat.textColor || 'light');
    form.get('accentColor')?.setValue(webchat.accentColor || '#1976d2');
    form.get('accentTextColor')?.setValue(webchat.accentTextColor || 'light');
    form.get('isListingWebChat')?.setValue(webchat.isListingWebChat || false);
    form.get('enableLeadCapture')?.setValue(!webchat.skipContactCapture);
    form.get('additionalPrompt')?.setValue(webchat.additionalPromptInstructions || '');
    form.get('position')?.setValue(webchat.position || DefaultWidgetPosition);
    form.get('webChatId')?.setValue(webchat.widgetId || '');
    form.get('namespace')?.setValue(webchat.namespace || '');
    // @ts-expect-error true is unreachable because the left operand is never nullish
    form.get('showMobileCta')?.setValue(!webchat.hideMobileCta ?? true);
    form.get('enableGreetingMessage')?.setValue(webchat.enableGreetingMessage || false);
  }

  static knowledgeFormGroupFactory(webchatId?: string) {
    const kID = webchatId ? inject(InboxService).buildAppId(webchatId) : '';
    return new FormGroup({
      knowledgeSources: new FormControl<KnowledgeSource[]>([]),
      // following is used only to store web chat state
      knowledgeAppId: new FormControl<string>(kID),
    });
  }

  static knowledgeFormGroupInit(form: FormGroup, webchatId: string): void {
    if (!form || !webchatId) return;
    const kID = webchatId ? inject(InboxService).buildAppId(webchatId) : '';
    form.get('knowledgeAppId')?.setValue(kID);
  }

  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  private readonly marketId$ = inject(MARKET_ID_TOKEN);
  private readonly inboxService = inject(InboxService);
  private readonly bizNavControl = this.inboxService.isBusinessApp ? inject(GalaxyNavControlService) : null;
  private readonly viewModeService = inject(ViewModeService);
  private readonly viewMode = toSignal(this.viewModeService.viewMode$);
  protected readonly canShowPreview = computed(() => {
    return this.viewMode() === 'modal' || this.viewMode() === 'sidebar';
  });

  private readonly partnerServce = inject(WhitelabelService);
  private readonly partnerConfig$ = combineLatest([this.partnerId$, this.marketId$]).pipe(
    switchMap(([partnerId, marketId]) => this.partnerServce.getConfiguration(partnerId, marketId)),
    catchError(() => EMPTY),
  );
  private readonly brandingService = inject(BrandingV2Service);
  private readonly brandingConfig$ = combineLatest([this.partnerId$, this.marketId$]).pipe(
    switchMap(([partnerId, marketId]) => this.brandingService.getBranding(partnerId, marketId)),
    catchError(() => EMPTY),
  );
  protected readonly showFooterContent$ = this.partnerConfig$.pipe(
    map((config) => config?.businessCenterConfiguration?.showInboxWidgetFooterContent),
  );
  protected readonly poweredBy$ = combineLatest([this.partnerConfig$, this.brandingConfig$]).pipe(
    map(([partnerConfig, brandingConfig]) => ({
      name: brandingConfig?.name,
      url: partnerConfig?.mailingConfiguration?.mailingWebsiteAddress,
    })),
  );

  protected readonly canShowSidebyPreview = this.inboxService.isBusinessApp;
  private readonly dialog = inject(MatDialog);
  protected readonly tempParticipantId = TEMP_PARTICIPANT_ID;
  private readonly tempAssistantParticipantId = TEMP_AI_ASSISTANT_PARTICIPANT_ID;
  protected showWebchat = signal<boolean>(true);
  protected readonly left = WidgetPosition.WIDGET_POSITION_LEFT;
  protected readonly WebChatPosition = WidgetPosition;

  readonly webChatForm = input.required<ReturnType<typeof WebchatFormComponent.webChatFormGroupFactory>>();
  readonly knowledgeForm = input.required<ReturnType<typeof WebchatFormComponent.knowledgeFormGroupFactory>>();
  readonly backUrl = input.required<string>();
  readonly submitCTA = input<string>('');
  readonly associatedAssistant = input<Assistant>();

  readonly submitting = output<boolean>();

  protected readonly isLoading = signal(false);
  protected readonly additionalInstructionsLimit = ADDITIONAL_INSTRUCTIONS_LIMIT;
  protected readonly welcomeMessageCharLimit = WELCOME_MESSAGE_LIMIT;
  protected readonly customGreetingMessageCharLimit = CUSTOM_GREETING_MESSAGE_LIMIT;

  protected readonly diffPartnerNamespace$ = inject(PARTNER_ID_TOKEN).pipe(
    map((partnerId) => {
      const namespace = this.webChatForm().get('namespace')?.value;
      // on location switch in business app takes user to main inbox settings parent page
      if (!namespace || namespace.startsWith('AG-') || !partnerId) {
        return false;
      }
      return partnerId !== this.webChatForm().get('namespace')?.value;
    }),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  private destroyRef = inject(DestroyRef);
  private readonly conversationApi = inject(ConversationApiService);
  private readonly snackbarService = inject(SnackbarService);
  private readonly knowledgeService = inject(AiKnowledgeService);
  private readonly router = inject(Router);
  private readonly route = inject(ActivatedRoute);
  private readonly unsavedGuard = inject(UnsavedChangesGuard);

  private readonly hasUnsavedChanges = signal(false);
  private readonly initialSources = signal<KnowledgeSource[] | null>(null);

  protected readonly greetingMessageGroup = signal<MessageGroup[]>([]);

  constructor() {
    // piggy back of isLoading to let parent components know about the creating/saving state
    effect(() => this.submitting.emit(this.isLoading()));
    // leverage the unsavedGuard to prevent leaving current route if there are unsaved changes
    // unsavedGuard uses private signal to manage state so we need to allow signal writes
    effect(() => this.unsavedGuard.notifyStateChanged(this.hasUnsavedChanges()));
  }

  ngOnInit(): void {
    this.bizNavControl?.closeForThisPage();

    this.webChatForm()
      .valueChanges.pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((v) => {
        this.markChanges();
        if (v?.webChatId) {
          this.knowledgeForm().get('knowledgeAppId')?.setValue(this.inboxService.buildAppId(v.webChatId));
        }
      });

    this.knowledgeForm()
      .valueChanges.pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => this.markChanges());

    this.webChatForm()
      .get('enableGreetingMessage')
      ?.valueChanges.pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.enableGreetingMessageField();
      });

    const customGreetingControl = this.webChatForm().get('customGreetingMessage');
    customGreetingControl?.valueChanges
      .pipe(startWith(customGreetingControl?.value), takeUntilDestroyed(this.destroyRef))
      .subscribe((value) => {
        this.setGreetingMessageGroup(value);
      });
  }

  private knowledgeSourcesHaveChanged(): boolean {
    const currentSources = this.knowledgeForm().get('knowledgeSources')?.value;
    if (this.initialSources()?.length !== currentSources?.length) {
      return true;
    }
    for (const source of this.initialSources() || []) {
      if (!(currentSources || []).some((s) => s.id === source.id)) {
        return true;
      }
    }
    return false;
  }

  protected updateSelectedSources(sources: KnowledgeSource[] | null): void {
    if (this.initialSources() === null && sources !== null) {
      this.initialSources.set(JSON.parse(JSON.stringify(sources)));
    }
    this.knowledgeForm().get('knowledgeSources')?.setValue(sources);
  }

  protected setTextColor() {
    this.webChatForm()
      .get('textColor')
      ?.setValue(getContrastYIQ(this.webChatForm().get('color')?.value || ''), { emitEvent: false });
  }

  protected setAccentTextColor() {
    this.webChatForm()
      .get('accentTextColor')
      ?.setValue(getContrastYIQ(this.webChatForm().get('accentColor')?.value || ''), { emitEvent: false });
  }

  @HostListener('window: beforeunload', ['$event'])
  onBeforeUnload(event: BeforeUnloadEvent): void {
    if (this.hasUnsavedChanges()) {
      event.preventDefault();
      event.stopPropagation();
      event.returnValue = false;
    }
  }

  protected async submit(): Promise<void> {
    if (this.webChatForm().invalid || this.knowledgeForm().invalid) {
      this.snackbarService.openErrorSnack('INBOX.WEBCHAT.SETTINGS.FORM_INVALID');
      return;
    }

    if (this.webChatForm().get('webChatId')?.value) {
      await this.updateWebchat();
    } else {
      await this.createWebchat();
    }
  }

  private markChanges(): void {
    const hasChanges = this.webChatForm().dirty || this.knowledgeForm().dirty || this.knowledgeSourcesHaveChanged();
    this.hasUnsavedChanges.set(hasChanges);
  }

  private markAsSaved() {
    this.webChatForm().markAsPristine();
    this.knowledgeForm().markAsPristine();
    this.initialSources.set(JSON.parse(JSON.stringify(this.knowledgeForm().get('knowledgeSources')?.value)));
    this.hasUnsavedChanges.set(false);
  }

  private async createWebchat(): Promise<void> {
    this.isLoading.set(true);
    this.webChatForm().disable();

    const widget = this.toWebChat();
    try {
      const resp = await firstValueFrom(
        this.conversationApi.createWidget(widget).pipe(catchError((err) => throwError(() => err))),
      );

      const widgetId = resp.widget.widgetId;
      this.webChatForm().get('webChatId')?.setValue(widgetId);
      const configurationUrl = await this.inboxService.buildAiKnowledgeAppConfigurationUrl(widgetId);
      const knowledge = this.toKnowledgeSource();
      await this.knowledgeService
        .upsertKnowledgeForApp(
          this.inboxService.buildAppId(widgetId),
          resp.widget.name,
          configurationUrl,
          KnowledgeAppType,
          knowledge,
        )
        .catch(
          // ignore error here for now because it could orphan the widget otherwise
          // TODO: handle this better in the next iteration of the UI for AI Knowledge (a different step?)
          () => console.warn('Failed to save Chat Receptionist settings'),
        );

      this.markAsSaved();
      this.snackbarService.openSuccessSnack('INBOX.WEBCHAT.SETTINGS.WEBCHAT_CREATED');
      this.router.navigate(['..', resp.widget.widgetId], { relativeTo: this.route });
    } catch (e) {
      this.snackbarService.openErrorSnack('INBOX.WEBCHAT.SETTINGS.WEBCHAT_CREATE_ERROR');
    } finally {
      this.isLoading.set(false);
      this.webChatForm().enable();
      this.enableGreetingMessageField();
    }
  }

  private async updateWebchat(): Promise<void> {
    this.isLoading.set(true);
    this.webChatForm().disable();
    try {
      const widget = this.toWebChat();
      await firstValueFrom(
        this.conversationApi
          .updateWidget({
            ...widget,
            fieldMask: {
              paths: [
                'name',
                'color',
                'welcome_message',
                'text_color',
                'accent_color',
                'accent_text_color',
                'skip_contact_capture',
                'additional_prompt_instructions',
                'assistant_name',
                'assistant_avatar_url',
                'position',
                'hide_mobile_cta',
                'custom_greeting_message',
                'enable_greeting_message',
              ],
            },
          })
          .pipe(catchError((err) => throwError(() => err))),
      );

      this.markAsSaved();
      this.snackbarService.openSuccessSnack('INBOX.WEBCHAT.SETTINGS.WEBCHAT_UPDATED');
    } catch (e) {
      this.snackbarService.openErrorSnack('INBOX.WEBCHAT.SETTINGS.WEBCHAT_UPDATE_ERROR');
    } finally {
      this.isLoading.set(false);
      this.webChatForm().enable();
      this.enableGreetingMessageField();
    }
  }

  private toWebChat(): Widget {
    return {
      name: this.webChatForm().get('name')?.value || '',
      color: this.webChatForm().get('color')?.value || '',
      welcomeMessage: this.webChatForm().get('welcomeMessage')?.value || '',
      customGreetingMessage: this.webChatForm().get('customGreetingMessage')?.value || '',
      textColor: this.webChatForm().get('textColor')?.value || '',
      accentColor: this.webChatForm().get('accentColor')?.value || '',
      accentTextColor: this.webChatForm().get('accentTextColor')?.value || '',
      widgetId: this.webChatForm().get('webChatId')?.value || '',
      namespace: this.webChatForm().get('namespace')?.value || '',
      isListingWebChat: this.webChatForm().get('isListingWebChat')?.value || false,
      skipContactCapture: !this.webChatForm().get('enableLeadCapture')?.value || false,
      additionalPromptInstructions: this.webChatForm().get('additionalPrompt')?.value || '',
      assistantName: this.webChatForm().get('assistantName')?.value || '',
      assistantAvatarUrl: this.webChatForm().get('assistantAvatarUrl')?.value || '',
      position: this.webChatForm().get('position')?.value || WidgetPosition.WIDGET_POSITION_UNSPECIFIED,
      // @ts-expect-error false is unreachable because the left operand is never nullish
      hideMobileCta: !this.webChatForm().get('showMobileCta')?.value ?? false,
      enableGreetingMessage: this.webChatForm().get('enableGreetingMessage')?.value ?? false,
    };
  }

  private toKnowledgeSource(): KnowledgeSource[] {
    return this.knowledgeForm().get('knowledgeSources')?.value || [];
  }

  openEnableLeadCaptureDialog(): void {
    this.dialog.open(EnableLeadCaptureDialogComponent, {
      width: '550px',
    });
  }

  openAdditionalInstructionsDialog(): void {
    this.dialog.open(AdditionalInstructionsDialogComponent, {
      width: '100vh',
    });
  }

  onImageChanged(imageUrl: string): void {
    this.webChatForm().get('assistantAvatarUrl')?.markAsDirty();
    this.webChatForm().get('assistantAvatarUrl')?.setValue(imageUrl);
  }

  toggleChat(): void {
    this.showWebchat.update((value) => !value);
  }

  setGreetingMessageGroup(message: string): void {
    if (!this.webChatForm().get('enableGreetingMessage')?.value) {
      return;
    }
    const mg = new MessageGroup('MG-id', this.tempAssistantParticipantId, new Date());
    mg.addMessage({
      type: MessageType.MESSAGE_TYPE_MESSAGE,
      sender: {
        participantId: this.tempAssistantParticipantId,
      },
      body: message || 'INBOX.WEBCHAT.SETTINGS.WEBCHAT_CUSTOM_GREETING_PLACEHOLDER', // this is translated downstream in a template
      // with a translation pipe
    } as ConversationMessage);

    this.greetingMessageGroup.set([mg]);
  }

  enableGreetingMessageField(): void {
    if (!this.webChatForm().get('enableGreetingMessage')?.value) {
      this.webChatForm().get('customGreetingMessage')?.disable();
      this.greetingMessageGroup.set([]);
      return;
    }
    this.webChatForm().get('customGreetingMessage')?.enable();
  }
}
