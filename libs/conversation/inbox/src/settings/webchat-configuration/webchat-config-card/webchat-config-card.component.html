<mat-card appearance="outlined">
  <mat-card-header>
    <img mat-card-avatar [src]="webchatIcon" alt="avatar" />
    <mat-card-title class="title">Web Chat</mat-card-title>
    <inbox-ai-badge></inbox-ai-badge>
  </mat-card-header>
  <mat-card-content>
    <div class="statement">
      {{ 'INBOX.WEBCHAT.SETTINGS.DESCRIPTION' | translate }}
      <a (click)="learnMore()">{{ 'INBOX.WEBCHAT.SETTINGS.LEARN_MORE' | translate }}</a>
    </div>

    <!-- All existing widgets -->
    @for (widget of webchats(); track widget.widgetId) {
      <div class="widget-row-item">
        <div class="widget-item-container">
          <div>
            @if (showWebChatUpgradeModal()) {
              <a [routerLink]="['/restricted', 'inbox-ai-webchat']">{{ widget.name }}</a>
            } @else {
              <a [routerLink]="['../widgets', widget.widgetId, 'edit']">{{ widget.name }}</a>
            }
          </div>

          <div>
            @if (canShowMyListingsCTA() && !!widget.myListingUrl) {
              <button
                mat-stroked-button
                color="secondary"
                class="secondary-btn"
                (click)="webChatCardRedirectMyListing(widget)"
              >
                {{ 'INBOX.WEBCHAT.SETTINGS.GO_TO_MY_LISTING' | translate }}
              </button>
            }
            <span class="badge">
              <glxy-badge [color]="badgeState(widget).color">{{ badgeState(widget).name | translate }}</glxy-badge>
            </span>
          </div>
        </div>
        <mat-divider></mat-divider>
      </div>
    }

    <!-- Create Widget CTA -->
    @if (showCreateWebChatCTA() === true) {
      <div class="widget-row-item">
        <div class="setup-btn-container">
          <div>
            @if (showWebChatUpgradeModal()) {
              <button mat-flat-button color="primary" [routerLink]="['/restricted', 'inbox-ai-webchat']">
                {{ 'INBOX.WEBCHAT.SETTINGS.SETUP_WEBCHAT' | translate }}
              </button>
            } @else {
              <button mat-flat-button color="primary" [routerLink]="['../widgets', 'new']">
                {{ 'INBOX.WEBCHAT.SETTINGS.SETUP_WEBCHAT' | translate }}
              </button>
            }
          </div>
          <div>
            <span class="badge">
              <glxy-badge [color]="badgeState().color">{{ badgeState().name | translate }}</glxy-badge>
            </span>
          </div>
        </div>
        <mat-divider></mat-divider>
      </div>
    }

    <!-- Go to Local SEO for a demo CTA -->
    @if (goToMyListingUrl(); as url) {
      <div class="widget-row-item">
        <div class="widget-item-container">
          <div>
            <span>{{ 'INBOX.WEBCHAT.SETTINGS.LOCAL_SEO_TITLE' | translate }}</span>
          </div>
          <div>
            <button mat-stroked-button color="secondary" class="secondary-btn" (click)="webChatCardRedirectMyListing()">
              {{ 'INBOX.WEBCHAT.SETTINGS.GO_TO_MY_LISTING' | translate }}
            </button>
            <span class="badge">
              <glxy-badge [color]="badgeState(null, url).color">{{
                badgeState(null, url).name | translate
              }}</glxy-badge>
            </span>
          </div>
        </div>
        <mat-divider></mat-divider>
      </div>
    }
  </mat-card-content>
</mat-card>
