import { Component, OnInit } from '@angular/core';
import { FieldType, FieldValue, FormConfigField } from '@vendasta/forms_microservice';
import { FormSubmissionBaseCellComponent } from './base-cell.component';

@Component({
  template: ` <p [style]="contentWidth">{{ text }}</p> `,
  standalone: false,
})
export class FormSubmissionsCellComponent extends FormSubmissionBaseCellComponent implements OnInit {
  text: string;
  contentWidth: string;
  private isMobile(): boolean {
    return window.matchMedia('(max-width: 767px)').matches;
  }

  ngOnInit(): void {
    const value = this.element.formSubmission.values.find((value) => value.fieldId === this.columnDefinition.id);
    const fieldDefinition = this.element.formConfig.fields.find(
      (field) => (field.schema.unmappedField?.id || field.schema.mappedField?.id) === value?.fieldId,
    );
    this.text = value ? this.readValue(value.fieldValue, fieldDefinition) : '';

    this.contentWidth =
      (fieldDefinition?.schema.unmappedField?.type === FieldType.FIELD_TYPE_TEXT_AREA ||
        fieldDefinition?.schema.mappedField?.type === FieldType.FIELD_TYPE_TEXT_AREA) &&
      !this.isMobile()
        ? 'min-width : 400px'
        : '';
  }

  private readValue(value: FieldValue, formConfigField: FormConfigField): any {
    switch (formConfigField.schema.unmappedField?.type || formConfigField.schema.mappedField?.type) {
      case FieldType.FIELD_TYPE_STRING:
      case FieldType.FIELD_TYPE_DATE:
      case FieldType.FIELD_TYPE_RADIO:
      case FieldType.FIELD_TYPE_DROPDOWN:
      case FieldType.FIELD_TYPE_EMAIL:
      case FieldType.FIELD_TYPE_PHONE:
      case FieldType.FIELD_TYPE_BUSINESS_SEARCH:
        return value.string;
      case FieldType.FIELD_TYPE_TEXT_AREA:
        return this.isMobile() ? value.string.slice(0, 60) + '...' : value.string;
      case FieldType.FIELD_TYPE_CURRENCY:
      case FieldType.FIELD_TYPE_INTEGER:
        return value.integer;
      case FieldType.FIELD_TYPE_BOOLEAN:
        return value.boolean + ''; // table doesn't support boolean;
      default:
        console.error(
          `Type ${
            formConfigField.schema.unmappedField?.type || formConfigField.schema.mappedField?.type
          } not supported`,
        );
        return '';
    }
  }
}
