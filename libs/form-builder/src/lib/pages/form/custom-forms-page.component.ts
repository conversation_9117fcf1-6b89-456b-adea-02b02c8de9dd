import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatTabGroup, MatTabsModule } from '@angular/material/tabs';
import { ActivatedRoute, Router } from '@angular/router';
import { RxState as RxStateService } from '@rx-angular/state';
import { AuxiliaryDataModule } from '@vendasta/auxiliary-data-components';
import {
  FormConfig,
  FormConfigFieldInterface,
  FormConfigInterface,
  FormsApiService,
  StylesInterface,
} from '@vendasta/forms_microservice';
import { GalaxyNavControlService } from '@vendasta/galaxy/nav';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyPopoverModule } from '@vendasta/galaxy/popover';
import { GalaxySnackbarModule, SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { asapScheduler, Observable, of, Subject } from 'rxjs';
import {
  distinctUntilChanged,
  map,
  observeOn,
  shareReplay,
  startWith,
  switchMap,
  take,
  tap,
  withLatestFrom,
} from 'rxjs/operators';
import { ComponentCanDeactivate } from '../../pending-changes-guard.service';
import { EmbedDialogComponent } from '../list/embed-dialog.component';
import { SaveDialogComponent } from './save-dialog.component';
import {
  ConfigureFormComponent,
  FormConfig as ConfigurationTabSettings,
  FormConfigChanges as ConfigurationTabSettingsChanges,
} from './configure-form.component';
import {
  EditableFormField,
  FormBuilderDependencies,
  FormBuilderLibraryInjectionToken,
  FormChanges,
  StyleChanges,
} from '../../interface';
import { FormEditorComponent } from '../../components/form-editor/form-editor.component';
import { PreviewAndStyleComponent } from '../../components/preview-and-style/preview-and-style.component';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';

@Component({
  templateUrl: './custom-forms-page.component.html',
  styleUrls: ['./custom-forms-page.component.scss'],
  imports: [
    CommonModule,
    GalaxyPageModule,
    GalaxyPopoverModule,
    GalaxySnackbarModule,
    MatIconModule,
    MatTabsModule,
    MatButtonModule,
    ScrollingModule,
    TranslateModule,
    AuxiliaryDataModule,
    FormEditorComponent,
    PreviewAndStyleComponent,
    ConfigureFormComponent,
    GalaxyAlertModule,
  ],
  providers: [RxStateService],
})
export class CustomFormsPageComponent extends ComponentCanDeactivate implements OnInit {
  @ViewChild('realignInkBar') matTabGroup: MatTabGroup;
  @ViewChild('buildTab') buildTab;
  private formId$ = this.route.params.pipe(
    map((params) => params['formId'] || ''),
    distinctUntilChanged(),
    shareReplay({ bufferSize: 1, refCount: true }),
  );
  existingForm$: Observable<FormConfig> = this.formId$.pipe(
    switchMap((formId) => {
      if (!formId) {
        return of(null);
      }
      return this.formsApiService.getForm({ formId }).pipe(map((response) => response.formConfig as FormConfig));
    }),
    tap(() => this.state.set({ [stateUnsavedChanges]: false })),
    shareReplay({ bufferSize: 1, refCount: true }),
  );
  private formNameQueryParam$ = this.route.queryParams.pipe(
    take(1),
    map((params) => params['formName'] || ''),
    shareReplay({ bufferSize: 1, refCount: true }),
  );
  existingSubmitButtonLabel$: Observable<string> = this.existingForm$.pipe(
    map((form) => form?.submitButtonLabel || ''),
    startWith(''),
  );
  existingConfigurationTabSettings$: Observable<ConfigurationTabSettings> = this.existingForm$.pipe(
    withLatestFrom(this.formNameQueryParam$),
    map(([form, initialFormName]) => {
      let createInboxConversation = form?.createInboxConversation || false;
      // If the form is new we want to have createInboxConversation checked by default.
      // Since Edit and New routes use the same component, we only can tell if it's new by the query param.
      // Alternatively, we could check the route URL. Current approach and alternative are both approaches are brittle.
      if (initialFormName) {
        createInboxConversation = true;
      }
      return {
        name: form?.name || initialFormName,
        redirectUrl: form?.redirectUrl,
        createInboxConversation: createInboxConversation,
        recaptchaSiteKey: form?.recaptchaSiteKey,
        recaptchaSecretKey: form?.recaptchaSecretKey,
      };
    }),
  );
  editableFormFields$: Observable<EditableFormField[]> = this.existingForm$.pipe(
    map((form) => form?.fields || []),
    map((fields) => fields.map((f) => new EditableFormField(f))),
  );
  formFields$ = this.state.select(stateFormFields);
  formStyles$: Observable<StylesInterface> = this.existingForm$.pipe(map((form) => form?.styles));
  formConfig$$ = new Subject<ConfigurationTabSettingsChanges>();
  readonly saveButtonError$ = this.state.select('saveButtonError');
  buildTabErrorClass$ = new Observable<string>();
  designTabErrorClass$ = new Observable<string>();
  configureTabErrorClass$ = new Observable<string>();
  tabGroupErrorClass$ = new Observable<string>();
  saveButtonListener$$ = new Subject<void>();
  editLink$ = new Observable<string>();
  previousPageRoute$ = this.config.routePrefix$.pipe(map((prefix) => prefix));
  constructor(
    @Inject(FormBuilderLibraryInjectionToken) readonly config: FormBuilderDependencies,
    private readonly formsApiService: FormsApiService,
    private readonly snackBarService: SnackbarService,
    private state: RxStateService<FormState>,
    private readonly route: ActivatedRoute,
    private router: Router,
    private dialog: MatDialog,
    private sideNavControl: GalaxyNavControlService,
  ) {
    super();

    this.editLink$ = this.config.routePrefix$.pipe(map((prefix) => `${prefix}/edit`));

    this.state.set({
      [stateUnsavedChanges]: false,
      [stateFormFields]: [],
      [stateStyles]: {},
      [stateStylesInvalid]: true,
      [stateStylesPending]: false,
      selectedTabIndex: 0,
      saveButtonError: null,
      configurationTabSettings: {
        valid: false,
        pending: false,
        touched: false,
        config: {
          name: '',
          redirectUrl: '',
          createInboxConversation: false,
          recaptchaSiteKey: '',
          recaptchaSecretKey: '',
        },
      },
    });
  }

  ngOnInit(): void {
    this.sideNavControl.closeForThisPage();

    window.setTimeout(() => this.matTabGroup.realignInkBar(), 300); // https://github.com/angular/components/issues/3048
    this.state.connect(
      this.formConfig$$.pipe(
        map((config) => {
          const newState: Partial<FormState> = {
            configurationTabSettings: config,
            unsavedChanges: true,
          };
          return newState;
        }),
      ),
    );

    this.formNameQueryParam$.subscribe((formName: string) => {
      if (!formName) return;
      this.formConfig$$.next({
        pending: false,
        valid: false,
        touched: false,
        config: {
          name: formName,
          redirectUrl: '',
          createInboxConversation: false,
          recaptchaSiteKey: '',
          recaptchaSecretKey: '',
        },
      });
    });

    this.buildTabErrorClass$ = this.state
      .select(stateFormInvalid)
      .pipe(map((invalid) => this.getTabHtmlClass(invalid)));
    this.designTabErrorClass$ = this.state
      .select(stateStylesInvalid)
      .pipe(map((invalid) => this.getTabHtmlClass(invalid)));
    this.configureTabErrorClass$ = this.state
      .select('configurationTabSettings')
      .pipe(map((config) => this.getTabHtmlClass(!config?.valid && config?.touched)));
    this.tabGroupErrorClass$ = this.state.select().pipe(
      map((state) => {
        const invalidForm = state[stateFormInvalid];
        const invalidStyles = state[stateStylesInvalid];
        const configInvalid = !state.configurationTabSettings?.valid;
        const selectedTabIndex = state.selectedTabIndex;
        if (
          (selectedTabIndex === 0 && invalidForm) ||
          (selectedTabIndex === 1 && invalidStyles) ||
          (selectedTabIndex === 2 && configInvalid)
        ) {
          return 'error-on-selected-tab';
        }
        return '';
      }),
    );
  }

  formChanged(changes: FormChanges): void {
    this.state.set({
      [stateFormFields]: changes?.fields || [],
      [stateUnsavedChanges]: changes.changedByUser,
      [stateFormInvalid]: changes?.invalidForm,
    });
  }

  handleStyleChanges(changes: StyleChanges): void {
    if (changes?.pending) {
      this.state.set({
        [stateStylesPending]: true,
        [stateStylesInvalid]: true,
        [stateUnsavedChanges]: true,
      });
    } else if (changes?.invalid) {
      this.state.set({
        [stateStylesPending]: false,
        [stateStylesInvalid]: true,
        [stateUnsavedChanges]: true,
      });
    } else if (changes?.styles) {
      this.state.set({
        [stateStylesPending]: false,
        [stateStylesInvalid]: false,
        [stateUnsavedChanges]: changes.changedByUser,
        [stateStyles]: changes.styles,
      });
    } else {
      console.count('empty form styles');
    }
  }

  submitButtonLabelChanged(label: string): void {
    this.state.set({ submitButtonLabel: label });
  }

  save(): void {
    this.saveButtonListener$$.next();
    if (
      this.state.get(stateFormInvalid) ||
      this.state.get(stateStylesInvalid) ||
      !this.state.get('configurationTabSettings').valid
    ) {
      this.state.set({ saveButtonError: 'CUSTOM_FORMS.EDIT.CANT_SAVE' });
      return;
    }

    if (!this.state.get(stateUnsavedChanges)) {
      this.snackBarService.openSuccessSnack('CUSTOM_FORMS.SUCCESS_SAVING_FORM');
      return;
    }

    if (this.state.get(stateStylesPending) || this.state.get('configurationTabSettings').pending) {
      this.state.set({ saveButtonError: 'CUSTOM_FORMS.EDIT.SAVE_TOO_FAST' });
      return;
    }

    this.state
      .select(stateStyles)
      .pipe(
        withLatestFrom(
          this.config.namespace$,
          this.formId$,
          this.state.select(stateFormFields),
          this.state.select('configurationTabSettings'),
          this.state.select('submitButtonLabel'),
        ),
        observeOn(asapScheduler),
        take(1),
        switchMap(([styles, namespace, formId, fields, configTab, submitButtonLabel]) => {
          const formConfig: FormConfigInterface = {
            formId: formId,
            namespace: namespace,
            name: configTab.config.name,
            // TODO have the correct formType
            formType: 'stub-form',
            fields: fields,
            styles: styles,
            createInboxConversation: configTab?.config?.createInboxConversation || false,
            submitButtonLabel: submitButtonLabel,
            recaptchaSiteKey: configTab.config.recaptchaSiteKey,
            recaptchaSecretKey: configTab.config.recaptchaSecretKey,
          };
          if (configTab.config.redirectUrl) {
            formConfig.redirectUrl = configTab.config.redirectUrl;
          }
          if (formId) {
            return this.formsApiService.updateForm({ formConfig });
          }
          return this.formsApiService.createForm({ formConfig });
        }),
      )
      .subscribe(
        (response) => {
          this.editLink$.pipe(take(1)).subscribe((editLink) => {
            if ('errors' in response) {
              console.error('Validation error: ', response);
              this.state.set({ [stateUnsavedChanges]: true });
              if (response.errors.length > 0 && 'message' in response.errors[0]) {
                this.snackBarService.openErrorSnack(response.errors[0].message);
                return;
              }
              this.snackBarService.openErrorSnack('CUSTOM_FORMS.ERROR_SAVING_FORM');
              return;
            }
            if ('formId' in response) {
              this.router.navigateByUrl(`${editLink}/${response['formId']}`);
            }
            this.state.set({ [stateUnsavedChanges]: false });
            this.snackBarService.openSuccessSnack('CUSTOM_FORMS.SUCCESS_SAVING_FORM');
          });
        },
        (error) => {
          console.error('Error saving the form', error);
          this.state.set({ [stateUnsavedChanges]: true });
          if (error.error && 'message' in error.error) {
            this.snackBarService.openErrorSnack(error.error.message);
          } else {
            this.snackBarService.openErrorSnack('CUSTOM_FORMS.ERROR_SAVING_FORM');
          }
        },
      );
  }

  hideSaveButtonError(): void {
    this.state.set({ saveButtonError: null });
  }

  canDeactivate(): boolean {
    return !this.state.get(stateUnsavedChanges);
  }

  openEmbedMenu(): void {
    this.formId$.pipe(take(1)).subscribe((formId) => {
      if (formId == '' || this.state.get(stateUnsavedChanges)) {
        const saveDialogRef = this.dialog.open(SaveDialogComponent, {
          width: '750px',
        });
        saveDialogRef.afterClosed().subscribe((result) => {
          if (result) {
            this.save();
          }
        });
      } else {
        this.dialog.open(EmbedDialogComponent, {
          width: '750px',
          data: {
            formId: formId,
          },
        });
      }
    });
  }

  private getTabHtmlClass(tabHasError: boolean): string {
    if (tabHasError) {
      return 'tab-has-error';
    }
    return '';
  }

  selectedTabChanged(index: number): void {
    this.state.set({ selectedTabIndex: index });
  }
}

// FIXME refactor this component to use the reactive interface of RxState,
//  so we don't need these key-constants. Look at how much easier configurationTabSettings is.
//  I think the FormState interface could just be implicit, and still get auto-complete, etc...
const stateExistingForm = 'existingForm';
const stateEditableForm = 'editableForm';
const stateFormFields = 'formFieldsForSave';
const stateUnsavedChanges = 'unsavedChanges';
const stateFormPreviewJson = 'formPreviewJson';
const stateStyles = 'styles';
const stateStylesInvalid = 'stylesInvalid';
const stateStylesPending = 'stylesPending';
const stateFormInvalid = 'formInvalid';

interface FormState {
  [stateExistingForm]: FormConfig;
  [stateEditableForm]: EditableFormField[];
  [stateFormFields]: FormConfigFieldInterface[];
  [stateUnsavedChanges]: boolean;
  [stateFormPreviewJson]: object;
  [stateStyles]: StylesInterface;
  submitButtonLabel: string;
  [stateStylesInvalid]: boolean;
  [stateStylesPending]: boolean;
  [stateFormInvalid]: boolean;
  selectedTabIndex: number;
  saveButtonError: string;
  configurationTabSettings: ConfigurationTabSettingsChanges;
}
