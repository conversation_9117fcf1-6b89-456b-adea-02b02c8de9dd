import { CommonModule, DOCUMENT } from '@angular/common';
import {
  AfterContentChecked,
  AfterViewInit,
  Component,
  ElementRef,
  Inject,
  Input,
  NgZone,
  OnChanges,
  OnDestroy,
  ViewChild,
} from '@angular/core';
import { StylesInterface } from '@vendasta/forms_microservice';
import { Observable, Subject, combineLatest, BehaviorSubject } from 'rxjs';
import { StylesService } from '../preview-and-style/styles-service';
import { Environment, EnvironmentService } from '@galaxy/core';
import { StyleChanges } from '../../interface';
import { filter, map, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { encode } from 'js-base64';

function getWidgetHostURL(env: Environment): string {
  switch (env) {
    case Environment.PROD:
      return 'https://www.cdnstyles.com';
    default:
      return 'https://vff-demo.appspot.com';
  }
}

@Component({
  selector: 'form-builder-jsonform-preview',
  template: ` <div #widgetPreviewContainer></div> `,
  styles: [
    `
      :host {
        width: 80%;
        margin: 0 auto;
        padding: 36px;
      }
    `,
  ],
  imports: [CommonModule],
  providers: [],
})
export class JsonformPreviewComponent implements AfterViewInit, OnDestroy, OnChanges, AfterContentChecked {
  @Input() jsonSchema = '{}';
  @Input() jsonUiSchema = '[]';

  @ViewChild('widgetPreviewContainer') widgetPreviewIframe: ElementRef = null as unknown as ElementRef;

  private visible$$ = new BehaviorSubject(false);
  private changed$$ = new Subject<void>();
  private reloadIframeOnVisibilityChanges$ = this.visible$$.pipe(
    distinctUntilChanged(),
    filter((visible) => visible),
  );
  private destroyed$$ = new Subject<void>();

  constructor(
    @Inject(DOCUMENT) private document: Document,
    private zone: NgZone,
    private readonly styleService: StylesService,
    private environmentService: EnvironmentService,
  ) {}

  private stylesAreValid(styles?: StylesInterface): styles is StylesInterface {
    return !!styles;
  }

  ngAfterContentChecked(): void {
    // manage visibility of div that needs to render the iframe
    // so that iframe is re-rendered when user changes tabs/component comes into view
    const visible = this.widgetPreviewIframe?.nativeElement.offsetParent !== null;
    if (this.visible$$.value !== visible) {
      this.visible$$.next(visible);
    }
  }

  ngAfterViewInit(): void {
    this.zone.runOutsideAngular(() => {
      this.createPreviewData()
        .pipe(takeUntil(this.destroyed$$))
        .subscribe((previewData) => {
          if (!this.widgetPreviewIframe) {
            return;
          }

          this.widgetPreviewIframe.nativeElement.innerHTML = '';
          const iframeElement = this.document.createElement('iframe');
          iframeElement.setAttribute('frameBorder', '0');
          iframeElement.setAttribute('data-cy', 'preview-widget');

          const script = this.document.createElement('script');
          script.setAttribute('id', '__custom_form_widget');
          script.setAttribute(
            'src',
            `${getWidgetHostURL(
              this.environmentService.getEnvironment(),
            )}/static/custom_form_widget/v1/custom_form.widget.js`,
          );
          script.setAttribute('preview-data', previewData);
          script.addEventListener('load', () => {
            // set size of iframe 100ms after elements were introduced to the DOM
            // to avoid scrollable frames
            window.setTimeout(() => {
              if (iframeElement.contentWindow?.document.body) {
                iframeElement.contentWindow.document.body.style.display = 'block';
                const styleNode = document.createElement('style');
                styleNode.innerHTML = `
                       .d-none {
                         display: none;
                       }
                `;
                iframeElement.contentWindow.document.body.appendChild(styleNode);
              }
              iframeElement.style.width = `${this.widgetPreviewIframe.nativeElement.clientWidth || 450}px`;
              iframeElement.style.height = `${iframeElement.contentWindow?.document.body.scrollHeight || 450}px`;
            }, 100);
          });
          script.async = true;
          script.defer = true;

          this.widgetPreviewIframe.nativeElement.appendChild(iframeElement);
          iframeElement.contentWindow?.document.open();
          iframeElement.contentWindow?.document.write(
            `<body style="margin: 21px;display:none;overflow:hidden;"></body>`,
          );
          iframeElement.contentWindow?.document.body.appendChild(script);
          iframeElement.contentWindow?.document.close();
        });
    });
  }

  private createPreviewData(): Observable<string> {
    const validStyles$ = this.styleService.styleChanged$.pipe(
      filter((changes: StyleChanges) => !changes?.pending && !changes?.invalid && !!changes?.styles),
      map((changes: StyleChanges) => changes?.styles),
      filter(this.stylesAreValid),
      debounceTime(300),
    );

    return combineLatest([validStyles$, this.changed$$, this.reloadIframeOnVisibilityChanges$]).pipe(
      map(([styles]) => {
        return encode(
          JSON.stringify({
            jsonSchema: this.jsonSchema,
            jsonUiSchema: this.jsonUiSchema,
            styles: styles,
          }),
        );
      }),
    );
  }

  ngOnDestroy(): void {
    this.destroyed$$.next();
  }

  ngOnChanges() {
    this.changed$$.next();
  }
}
