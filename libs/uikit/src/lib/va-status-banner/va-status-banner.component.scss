@use 'design-tokens' as *;

.container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 8px;
  border-radius: 2px;

  .message {
    display: flex;
    align-items: center;
    color: $dark-gray;
    font-family: Roboto, 'Helvetica Neue', sans-serif;

    .message--title {
      margin: 0px 5px 0px 12px;
      font-weight: bold;
    }

    .noTitle {
      margin-left: 12px;
    }
  }

  .action {
    display: flex;
    align-items: center;
  }
}

.warning {
  background-color: #fff8e3;
  .icon {
    color: #f4ae00;
  }
}
.border {
  border: 1px solid $yellow;
}
.info {
  background-color: #dff0fd;
  border: 1px solid #90caf9; /* mat blue 200 */

  .icon {
    color: $blue;
  }
}
