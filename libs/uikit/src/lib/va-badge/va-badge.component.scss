@use 'design-tokens' as *;

:host {
  display: inline-block;
  vertical-align: middle;

  &[small] .badge {
    padding: 0 5px;
    line-height: 16px;
    min-width: 16px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    text-align: center;
  }
}

.badge {
  display: inline-block;
  vertical-align: middle;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: bold;
  line-height: 1;
  white-space: nowrap;
  color: $gray;
  background-color: $lighter-gray;
  border-radius: 12px;
}

.badge[class*='solid'] {
  color: $white;
}

.badge--red,
.badge--warn {
  color: $dark-red;
  background-color: $light-red;
}
.badge--red-solid {
  background-color: $red;
}

.badge--yellow,
.badge--caution {
  color: $dark-yellow;
  background-color: $light-yellow;
}
.badge--yellow-solid {
  background-color: $yellow;
}

.badge--green,
.badge--positive {
  color: $dark-green;
  background-color: $light-green;
}
.badge--green-solid {
  background-color: $green;
}

.badge--blue,
.badge--primary {
  color: $dark-blue;
  background-color: $light-blue;
}
.badge--blue-solid {
  background-color: $blue;
}

.badge--light-gray,
.badge--disabled {
  color: $gray;
  background-color: $lighter-gray;
}
.badge--gray {
  color: $dark-gray;
  background-color: $light-gray;
}
.badge--gray-solid {
  background-color: $gray;
}
.badge--dark-gray-solid {
  background-color: $dark-gray;
}
.badge--black-solid {
  background-color: $darker-gray;
}

.badge--purple {
  color: #5e35b1;
  background-color: #f3e5f5;
}
