{"INTEGRATION_PAGE": {"PLATFORM_INTEGRATION": "Integrations", "CONNECT_INTEGRATION_NAME": "Connect {{integrationDisplayName}}", "SYNC_SETTINGS_CONTENT_TITLE": "Set your default sync preferences before connecting to {{integrationDisplayName}}", "REQUEST_CONNECTION": "Request {{integrationDisplayName}} Connection"}, "INTEGRATION_CARD": {"CONNECTED": "Connected", "CONNECT": "Connect", "SETTINGS": "Settings", "DISCONNECT": "Disconnect", "DISCONNECTED": "Disconnected", "RESUME_CONNECTION": "Resume connection", "RECONNECT": "Reconnect", "BROWSE": "Browse", "MANAGE": "Manage", "CONTINUE": "Continue", "CONNECTION_NOT_CONFIGURED": "Connection not configured", "CREATE_CONNECTION_SUCCESS": "Successfully created connection", "TITLE": "Disconnect {{title}}?", "CANCEL_CONNECTION_MSG": "Cancelling this Connection will stop data from syncing. Data that was previously synced will remain, but will not be updated.", "CANCEL_CONNECTION_TITLE": "Cancel Connection to {{title}}", "CANCEL_CONNECTION": "Cancel Connection", "KEEP_CONNECTION": "Keep Connection", "SYNC_SETTING_TITLE": "Turn off {{title}}", "TURN_OFF": "Turn off", "SYNC_SETTING_UPDATE_ERROR": "Unable to update sync settings", "SYNC_SETTING_MESSAGE": "Are you sure you want to turn off {{title}}?", "MESSAGE": "Disconnecting this account will stop data from syncing. Data that was previously synced will remain, but will not be updated.", "FACEBOOK_DISCONNECT_WARNING_MESSAGE": "Disconnecting this account will stop changes from your Business Profile from being synced to your Facebook Page, prevent publishing and scheduling social posts to Facebook, and disable reviews from being pulled in from Facebook.", "GOOGLE_BUSINESS_PROFILE_DISCONNECT_WARNING_MESSAGE": "Disconnecting this account will stop changes from your Business Profile from being synced to your Google Business Profile, prevent publishing and scheduling social posts to Google, and disable instant review from being pulled in from Google.", "CANCEL": "Cancel", "DISCONNECT_WARNING_PROBLEM_VENDOR_MANAGED": "<b>Connection Issue Detected:</b> We are working on it. You can disconnect if you are no longer using this integration.", "DISCONNECT_WARNING_PROBLEM": "There is a problem with your <b>{{ title }}</b> connection. Reconnect your account to continue syncing data.", "DISCONNECT_TWO_ACCOUNTS_WARNING_PROBLEM": "There is a problem with your <b>{{ firstTitle }}</b> and <b>{{ secondTitle }}</b> connections. Reconnect your account to continue syncing data.", "DISCONNECT_MULTIPLE_ACCOUNTS_WARNING_PROBLEM": "There is a problem with your <b>{{ title }}</b> and <b>{{ count }} other</b> connections. Reconnect your account to continue syncing data.", "CANCEL_SUCCESS_MESSAGE": "{{title}} connection process has been successfully cancelled", "DELETE_SUCCESS_MESSAGE": "{{title}} integration has been successfully disconnected.", "DELETE_ERROR_MESSAGE": "Failed to disconnect the connection", "CREATE_ERROR_MESSAGE": "Failed to create the connection", "INTEGRATION_LIST_ERROR": "Failed to get the integrations", "INTEGRATION_ERROR": "Failed to get the integration", "GET_CONNECTION_ERROR": "Failed to get the connection", "GET_CONNECTIONS_LIST_ERROR": "Failed to get the connections", "PERFORM_SSO_ERROR": "Failed to perform SSO", "ADD_CONNECTION": "Add connection", "MANAGE_CONNECTION": "Manage connection", "REQUEST_CONNECTION": "Request connection", "PRECONNECT_WARNING_PROBLEM": "The <b>{{ title }}</b> connection process is pending. A notification will be sent on successful completion.", "PRECONNECT_SSO_BASED_WARNING_PROBLEM": "The <b>{{ title }}</b> connection process is pending. Click “Resume connection” to activate this integration.", "PRECONNECT_TWO_ACCOUNTS_WARNING_PROBLEM": "The <b>{{ firstTitle }}</b> and <b>{{ secondTitle }}</b> connections processes is pending. Please click on the respective connection card for more information", "PRECONNECT_MULTIPLE_ACCOUNTS_WARNING_PROBLEM": "The <b>{{ title }}</b> and <b>{{ count }} other</b> connection processes are pending. Please click on the respective connection card for more information", "EMPTY_PAGE_HEADING": "Connect your first integration", "EMPTY_PAGE_DESCRIPTION": "Connect the external tools you use every day to do more with Business App", "CONNECTED_ACCOUNTS": "Connected accounts", "CONNECT_ZAPIER": "Connect on Zapier", "SEND_CONNECT_REQUEST": "Send request", "CANCEL_CONNECTION_REQUEST": "Cancel connection request", "CONNECTION_PENDING": "Connection pending", "CONNECTION_ERROR_SSO": "Error happened", "PRE_CONNECT_FORMS": {"VENDOR_MANAGED": {"FORM_DESCRIPTION": "<p>Our team will work with {{integrationDisplayName}} to get your connection set up. This process typically takes 24-48 hours.</p><p>You will be notified once it's set up. Feel free to disconnect anytime by visiting this page.</p>"}, "JOBBER": {"TITLE": "Connect Jobber", "SUBTITLE": "Set your default sync preferences before connecting to Jobber"}}, "CANCEL_VENDOR_MANAGED_CONNECTION_REQUEST?": "Cancel {{integrationDisplayName}} connection request?", "THIS_WILL_STOP_CURRENT_CONNECTION_PROCESS_FOR_THIS_INTEGRATION": "This will stop current connection process for this integration", "CONNECTION_REQUEST_CANCELLED_SUCCESFULLY": "Connection request cancelled successfully", "FAILED_TO_CANCEL_CONNECTION_REQUEST": "Failed to cancel connection request", "CONNECTED_DATE": "Connected on {{date}}", "SMB_USERS_ONLY_CONNECTION_TOOLTIP": "Only members of this business can connect {{integrationName}}", "IMPERSONATE_USERS_CANNOT_CONNECT_TOOLTIP": "Cannot Create Connection as Impersonated User", "VERIFY_YOUR_BUSINESS": "Verify your Business"}, "CONNECTION_SYNC_SETTINGS": {"EDIT": "Edit", "SAVE": "Save", "CANCEL": "Cancel", "UNLOCK_PREMIUM_SERVICE": "Unlock", "SELF_SERVE_UPGRADE": "Self-serve upgrade", "CONTACT_SALESPERSON": "Contact a salesperson", "UPGRADE_TO_PRO": "Upgrade to Pro with a custom package", "AUTOMATED_REVIEW_REQUESTS": "Automated Review Requests", "AVAILABLE_ON_RM_PREMIUM": "Available on Reputation Management Premium", "PROMPT_CUSTOMERS": "Automatically prompt customers to leave reviews", "BUY_DESCRIPTION": "Discover the power of automated review generation with our exclusive Premium feature. Boost your online presence, gather valuable feedback effortlessly, and enhance your reputation.", "CONTACT_SALES_DESCRIPTION": "Connect with us to access this exclusive feature, streamlining your review generation process, boosting online visibility, and effortlessly managing valuable customer feedback.", "BUY": "Buy", "CONTACT_SALES": "Contact sales", "UPDATE_SETTINGS": "{{label}} has been updated", "UPDATE_SYNC_ID_SETTINGS_ERROR": "Unable to update {{label}}", "UPDATE_SETTINGS_ERROR": "Unable to update Connection", "UPDATE_SYNC_SETTING_ERROR": "Unable to update Setting", "INVALID_PARTNER_CODE_ERROR": "Unable to update setting due to invalid partner code", "REQUIRED_FIELD_ERROR": "Please fill out the required fields", "UPDATE_SETTINGS_SUCCESS": "Updated Successfully", "MULTIPLE_VALUES_SAVE_SUCCESS": "{{title}} Values Saved Successfully", "SINGLE_VALUE_SAVE_SUCCESS": "{{title}} Saved Successfully"}, "ACCOUNT_DETAILS": {"ACCOUNT_NAME": "Account name", "CONNECTED_BY": "Connected by", "REQUESTED_BY": "Requested by", "CONNECTED": "Connected", "PROBLEM_DETECTED": "Problem Detected", "REQUESTED": "Requested", "DATA_LAST_RECEIVED": "Data last received", "ACCOUNT_URL": "Account URL", "LOCATION": "Location", "CONNECTED_MAILID": "Connected Mail ID"}, "CONFIG_PAGE": {"ACCOUNT_DETAILS": "Account details", "RESOURCES": "Resources", "COPY": "Copy"}, "INTEGRATION_BANNER": {"BANNER_HEADER": "Discover new integrations", "BANNER_DESCRIPTION": "Install once and sync data, streamline your common tasks, and more.", "NAVIGATE_TO_BROWSE_BUTTON": "Browse integrations", "NAVIGATE_TO_BROWSE": "Browse", "VIEW": "View"}, "SETTINGS": {"PAGE_TITLE": "Settings"}, "COMMON": {"PAGE_NOT_FOUND": "Page Not Found", "INTEGRATION_NOT_EXIST": "Integration does not exist", "BACK_TO_INTEGRATIONS": "Back to integrations", "COPY_TO_CLIPBOARD": "Copy to Clipboard"}}