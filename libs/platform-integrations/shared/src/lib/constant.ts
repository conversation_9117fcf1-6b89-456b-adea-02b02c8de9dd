import { InjectionToken } from '@angular/core';
import { MeetingSourceOrigin } from '@vendasta/meetings';

export const PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$ = 'PI_PARTNER_ID';
export const PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$ = 'PI_NAMESPACE';
export const PLATFORM_INTEGRATIONS_ORIGIN_INJECTION_TOKEN = new InjectionToken<MeetingSourceOrigin>('Origin');
export const PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$ = 'PI_CONTEXT';
export const PLATFORM_INTEGRATIONS_MARKET_ID_INJECTION_TOKEN$ = 'PI_MARKET_ID';
