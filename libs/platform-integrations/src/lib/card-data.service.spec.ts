import { CardDataService } from './card-data.service';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  ConnectionsApiService,
  CreateConnectionRequest,
  CreateConnectionResponse,
  IntegrationMarketingApiService,
  ListConnectionRequest,
  ListIntegrationMarketingRequest,
  OAuthApiService,
  SupportedContexts,
} from '@vendasta/platform-integrations';
import { EMPTY, of, throwError } from 'rxjs';
import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import {
  PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_MARKET_ID_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_ORIGIN_INJECTION_TOKEN,
  PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$,
} from '@galaxy/platform-integrations/shared';
import { FeatureFlagService } from './feature-flag.service';
import { MeetingSourceOrigin } from '@vendasta/meetings';

describe('CardDataService', () => {
  let service: CardDataService;
  let integrationServiceMock: { list: jest.Mock };
  let snackbarServiceMock: any;
  let connectionServiceMock: { list: jest.Mock; create: jest.Mock };
  let authServiceMock: {
    getAuthorizationCodeRedirectUrl: jest.Mock;
  };
  let featureFlagServiceMock: {
    checkFeatureFlag: jest.Mock;
  };
  const testContext: SupportedContexts = SupportedContexts.PI_CONTEXT_SMB;
  const testNamespace = of('VUNI');
  const testPID = of('VUNI');
  const testMarketID = of('default');

  beforeEach(() => {
    integrationServiceMock = {
      list: jest.fn().mockReturnValue(
        of({
          integrations: [],
          pagingMetadata: {},
        }),
      ),
    };

    connectionServiceMock = {
      list: jest.fn().mockReturnValue(
        of({
          connections: [],
          pagingMetadata: {},
        }),
      ),
      create: jest.fn().mockReturnValue(
        of({
          connectionId: '',
        }),
      ),
    };

    authServiceMock = {
      getAuthorizationCodeRedirectUrl: jest.fn().mockReturnValue(of(undefined)),
    };

    featureFlagServiceMock = {
      checkFeatureFlag: jest.fn().mockReturnValue(of([])),
    };

    snackbarServiceMock = {
      openErrorSnack: jest.fn(),
    };

    const tb = TestBed.configureTestingModule({
      providers: [
        CardDataService,
        { provide: IntegrationMarketingApiService, useValue: integrationServiceMock },
        { provide: ConnectionsApiService, useValue: connectionServiceMock },
        { provide: SnackbarService, useValue: snackbarServiceMock },
        { provide: OAuthApiService, useValue: authServiceMock },
        { provide: FeatureFlagService, useValue: featureFlagServiceMock },
        { provide: PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$, useValue: testContext },
        { provide: PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$, useValue: testNamespace },
        { provide: PLATFORM_INTEGRATIONS_ORIGIN_INJECTION_TOKEN, useValue: MeetingSourceOrigin.BusinessApp },
        { provide: PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$, useValue: testPID },
        { provide: PLATFORM_INTEGRATIONS_MARKET_ID_INJECTION_TOKEN$, useValue: testMarketID },
      ],
      imports: [HttpClientTestingModule],
    });
    tb.inject(HttpTestingController);
    service = tb.inject(CardDataService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getIntegrationsList', () => {
    it('should retrieve and return all integrations', (done) => {
      const fakeResponse = [
        {
          integrationId: 'integration1',
          logo: 'logo1.jpg',
          displayName: 'Integration 1',
          description: 'Sample description for Integration 1',
          integrationType: 'Type 1',
          status: 'Active',
          created: new Date('2023-01-01'),
          updated: new Date('2023-01-02'),
          deleted: null, // Assuming it's not deleted
        },
        {
          integrationId: 'integration2',
          logo: 'logo2.jpg',
          displayName: 'Integration 2',
          description: 'Sample description for Integration 2',
          integrationType: 'Type 2',
          status: 'Inactive',
          created: new Date('2023-02-01'),
          updated: new Date('2023-02-02'),
          deleted: null, // Assuming it's not deleted
        },
      ];

      integrationServiceMock.list.mockReturnValue(of({ integrations: fakeResponse }));
      featureFlagServiceMock.checkFeatureFlag.mockReturnValue(of([true, true]));

      service.getIntegrationsList().subscribe((integrations) => {
        expect(integrations).toEqual(fakeResponse);
        done();
      });

      expect(integrationServiceMock.list).toHaveBeenCalledWith(
        new ListIntegrationMarketingRequest({
          pagingOptions: {
            pageSize: 500,
          },
          supportedContexts: SupportedContexts.PI_CONTEXT_SMB,
          filters: {
            displayName: undefined,
          },
        }),
      );
    });

    it('should not return integration if feature flag is false', (done) => {
      const fakeResponse = [
        {
          integrationId: 'integration1',
          logo: 'logo1.jpg',
          displayName: 'Integration 1',
          description: 'Sample description for Integration 1',
          integrationType: 'Type 1',
          status: 'Active',
          created: new Date('2023-01-01'),
          updated: new Date('2023-01-02'),
          deleted: null, // Assuming it's not deleted
        },
        {
          integrationId: 'integration2',
          logo: 'logo2.jpg',
          displayName: 'Integration 2',
          description: 'Sample description for Integration 2',
          integrationType: 'Type 2',
          status: 'Inactive',
          created: new Date('2023-02-01'),
          updated: new Date('2023-02-02'),
          deleted: null, // Assuming it's not deleted
        },
      ];

      integrationServiceMock.list.mockReturnValue(of({ integrations: fakeResponse }));
      featureFlagServiceMock.checkFeatureFlag.mockReturnValue(of([false, false]));

      service.getIntegrationsList().subscribe((integrations) => {
        expect(integrations).toEqual([]);
        done();
      });

      expect(integrationServiceMock.list).toHaveBeenCalledWith(
        new ListIntegrationMarketingRequest({
          pagingOptions: {
            pageSize: 500,
          },
          supportedContexts: SupportedContexts.PI_CONTEXT_SMB,
          filters: {
            displayName: undefined,
          },
        }),
      );
    });

    it('should handle API error and show error message', (done) => {
      const errorMessage = 'API Error Message';
      integrationServiceMock.list.mockReturnValue(throwError(errorMessage));

      service.getIntegrationsList('').subscribe(
        () => {
          fail('Expected an error');
        },
        (error) => {
          expect(error).toBe(errorMessage);
          expect(snackbarServiceMock.openErrorSnack).toHaveBeenCalledWith('INTEGRATION_CARD.INTEGRATION_LIST_ERROR');
          done();
        },
      );

      expect(integrationServiceMock.list).toHaveBeenCalledWith(
        new ListIntegrationMarketingRequest({
          pagingOptions: {
            pageSize: 500,
          },
          supportedContexts: SupportedContexts.PI_CONTEXT_SMB,
          filters: {
            displayName: '',
          },
        }),
      );
    });
  });

  describe('getConnectionsList', () => {
    it('should retrieve and return connections', (done) => {
      const fakeConnection1 = {
        connectionId: 'connection1',
        namespace: 'namespace1',
        integrationId: 'integration1',
        userId: 'user1',
        created: new Date('2023-01-01'),
        updated: new Date('2023-01-02'),
        deleted: null, // Assuming it's not deleted
      };

      const fakeConnection2 = {
        connectionId: 'connection2',
        namespace: 'namespace2',
        integrationId: 'integration2',
        userId: 'user2',
        created: new Date('2023-02-01'),
        updated: new Date('2023-02-02'),
        deleted: null, // Assuming it's not deleted
      };

      const fakeResponse = [fakeConnection1, fakeConnection2];

      connectionServiceMock.list.mockReturnValue(of({ connections: fakeResponse }));

      service.getConnectionsList('your-namespace').subscribe((connections) => {
        expect(connections).toEqual(fakeResponse);
        done();
      });

      expect(connectionServiceMock.list).toHaveBeenCalledWith(
        new ListConnectionRequest({
          namespace: 'your-namespace',
          filters: {
            context: SupportedContexts.PI_CONTEXT_SMB,
          },
          pagingOptions: {
            pageSize: 25,
          },
        }),
      );
    });

    it('should handle API error and show error message', (done) => {
      const errorMessage = 'API Error Message';
      connectionServiceMock.list.mockReturnValue(throwError(() => new Error(errorMessage).message));

      service.getConnectionsList('GROOT').subscribe(
        () => {
          fail('Expected an error');
        },
        (error) => {
          expect(error).toBe(errorMessage);
          expect(snackbarServiceMock.openErrorSnack).toHaveBeenCalledWith(
            'INTEGRATION_CARD.GET_CONNECTIONS_LIST_ERROR',
          );
          done();
        },
      );

      expect(connectionServiceMock.list).toHaveBeenCalledWith(
        new ListConnectionRequest({
          namespace: 'GROOT',
          filters: {
            context: SupportedContexts.PI_CONTEXT_SMB,
          },
          pagingOptions: {
            pageSize: 25,
          },
        }),
      );
    });
  });

  describe('createConnection', () => {
    it('should create connection successfully', (done) => {
      const mockCreateResponse = new CreateConnectionResponse({
        connectionId: 'connection-id',
      });
      connectionServiceMock.create.mockReturnValue(of(mockCreateResponse));

      service
        .createConnection('groot-namespace', 'groot-integration-id', [], SupportedContexts.PI_CONTEXT_PARTNER)
        .subscribe((response) => {
          expect(response).toEqual(mockCreateResponse);
          done();
        });

      expect(connectionServiceMock.create).toHaveBeenCalledWith(
        new CreateConnectionRequest({
          namespace: 'groot-namespace',
          integrationId: 'groot-integration-id',
          customFields: [],
          context: SupportedContexts.PI_CONTEXT_PARTNER,
        }),
      );
    });

    it('should show error if create connection fails', () => {
      connectionServiceMock.create.mockReturnValue(EMPTY);

      service
        .createConnection('groot-namespace', 'groot-integration-id', [], SupportedContexts.PI_CONTEXT_PARTNER)
        .subscribe(
          () => {
            fail('Expected an error');
          },
          () => {
            expect(snackbarServiceMock.openErrorSnack).toHaveBeenCalledWith('Failed to create the connection');
          },
        );

      expect(connectionServiceMock.create).toHaveBeenCalledWith(
        new CreateConnectionRequest({
          namespace: 'groot-namespace',
          integrationId: 'groot-integration-id',
          customFields: [],
          context: SupportedContexts.PI_CONTEXT_PARTNER,
        }),
      );
    });
  });

  describe('Perform SSO', () => {
    // This test passes but throws an console error - Error: Not implemented: navigation (except hash changes)
    // In jest, there is no easy way to mock window.location.href, have to find a good way to mock it.
    xit('should successfully perform SSO', async () => {
      const namespace = 'groot-namespace';
      const integrationId = 'groot-integration-id';
      const connectionId = 'groot-connection-id';
      const mockRedirectURL = { url: 'http://finalCallbackUrl.com/' };
      const baseURL = 'https://base.com/';
      authServiceMock.getAuthorizationCodeRedirectUrl.mockReturnValue(of(mockRedirectURL));

      await service.performSSO(namespace, integrationId, connectionId, baseURL).subscribe(() => {
        expect(authServiceMock.getAuthorizationCodeRedirectUrl).toHaveBeenCalledWith({
          namespace: namespace,
          integrationId: integrationId,
          connectionId: connectionId,
        });
        expect(snackbarServiceMock.openErrorSnack).not.toHaveBeenCalled();
      });
    });

    it('should handle SSO failure', async () => {
      const namespace = 'groot-namespace';
      const integrationId = 'groot-integration-id';
      const connectionId = 'groot-connection-id';
      const errorMessage = 'SSO Error Message';
      const baseURL = 'https://base.com/';
      authServiceMock.getAuthorizationCodeRedirectUrl.mockReturnValue(
        throwError(() => new Error(errorMessage).message),
      );

      await service.performSSO(namespace, integrationId, connectionId, baseURL).subscribe(
        () => {
          fail('Expected error to be thrown');
        },
        (error) => {
          expect(authServiceMock.getAuthorizationCodeRedirectUrl).toHaveBeenCalledWith({
            namespace: namespace,
            integrationId: integrationId,
            connectionId: connectionId,
          });
          expect(snackbarServiceMock.openErrorSnack).toHaveBeenCalled();
          expect(error.message).toEqual('SSO Error Message');
        },
      );
    });
  });
});
