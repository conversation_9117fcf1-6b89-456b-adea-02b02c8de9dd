import { Inject, inject, Injectable, Injector, signal, WritableSignal } from '@angular/core';
import { map, Observable, of, switchMap } from 'rxjs';
import { IntegrationType } from '../model/connection-integration-detail';
import { CardDataService } from '../card-data.service';
import {
  ConnectionResponse,
  ConnectionStatus,
  CreateConnectionResponse,
  IntegrationMarketingResponse,
  SupportedContexts,
} from '@vendasta/platform-integrations';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { DynamicFormComponent } from '../connection-config/components/dynamic-form/dynamic-form.component';
import { PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$ } from '@galaxy/platform-integrations/shared';
import { ActivatedRoute, Router } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class ConnectionActionService {
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly cardService = inject(CardDataService);
  private injector = inject(Injector);
  private matDialog = inject(MatDialog);

  private integrationType: IntegrationType | undefined;
  private namespace = '';

  private integrationList$: Observable<IntegrationMarketingResponse[]> = of([]);
  private connectionList$: Observable<ConnectionResponse[]> = of([]);
  private connection$: Observable<ConnectionResponse | undefined> = of();
  private integration: WritableSignal<IntegrationMarketingResponse> = signal<IntegrationMarketingResponse>(
    new IntegrationMarketingResponse(),
  );
  connectionStatus: WritableSignal<ConnectionStatus> = signal<ConnectionStatus>(ConnectionStatus.UNSPECIFIED_STATUS);

  constructor(@Inject(PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$) public readonly context: SupportedContexts) {}

  initialize(integrationType: IntegrationType, nameSpace: string): void {
    this.integrationType = integrationType;
    this.namespace = nameSpace;
    this.integrationList$ = this.cardService.getIntegrationsList();
    this.connectionList$ = this.integrationList$.pipe(
      switchMap((integrations) => {
        const integration = integrations.find((integration) => integration.integrationType === this.integrationType);
        if (!integration) {
          return of([]);
        }
        this.integration.set(integration);
        return this.cardService.getConnectionsList(this.namespace, integration.integrationId);
      }),
    );
    this.connection$ = this.connectionList$.pipe(
      map((connectionList) => {
        const connections = connectionList?.filter(
          (connect) =>
            connect.status === ConnectionStatus.CONNECTED ||
            connect.status === ConnectionStatus.DISCONNECTED ||
            connect.status === ConnectionStatus.PRECONNECTED,
        );
        return connections?.length ? connections[0] : undefined;
      }),
    );

    this.connection$.subscribe((connection) => {
      this.refreshStatus(connection, this.connectionStatus);
    });
  }
  refreshStatus(connection: ConnectionResponse | undefined, connectionStatus: WritableSignal<ConnectionStatus>): void {
    connectionStatus.set(connection?.status || ConnectionStatus.UNSPECIFIED_STATUS);
  }

  getNextURL(): string {
    const mutableQP = { ...this.activatedRoute.snapshot.queryParams };
    mutableQP['src'] = 'sso';
    const urlWithoutQueryParams = this.router.url.split('?')[0];
    return (
      window.location.origin + this.router.createUrlTree([urlWithoutQueryParams], { queryParams: mutableQP }).toString()
    );
  }

  createConnection(): void {
    this.cardService
      .createConnection(this.namespace, this.integration().integrationId, [], this.context)
      .pipe(
        switchMap((resp: CreateConnectionResponse) => {
          return this.cardService.performSSO(
            this.namespace,
            this.integration().integrationId,
            resp.connectionId,
            this.getNextURL(),
          );
        }),
      )
      .subscribe();
  }

  continueConnection(): void {
    this.connection$
      .pipe(
        switchMap((connection) => {
          return this.cardService.performSSO(
            this.namespace,
            this.integration().integrationId,
            connection?.connectionId || '',
            this.getNextURL(),
          );
        }),
      )
      .subscribe();
  }

  openConnectModal(newConnection: boolean): void {
    if (!this.integration().preconnectFormFields) {
      if (newConnection) {
        return this.createConnection();
      }
      return this.continueConnection();
    }
    const dialogConfig = new MatDialogConfig();
    dialogConfig.width = '660px';
    dialogConfig.data = {
      integration: this.integration(),
    };
    const dialogRef = this.matDialog.open(DynamicFormComponent, dialogConfig);
    dialogRef.componentInstance.saveSyncSettingsEvent.subscribe((_data) => {
      if (newConnection) {
        return this.createConnection();
      } else {
        return this.continueConnection();
      }
    });
  }
}
