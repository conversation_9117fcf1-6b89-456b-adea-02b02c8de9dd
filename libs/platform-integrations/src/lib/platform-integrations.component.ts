import { CommonModule } from '@angular/common';
import { Component, computed, DestroyRef, Inject, inject, OnInit, Optional, signal, Signal } from '@angular/core';
import { takeUntilDestroyed, toSignal, toObservable } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { ActivatedRoute, Router } from '@angular/router';
import {
  PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$,
  PlatformIntegrationsI18nModule,
} from '@galaxy/platform-integrations/shared';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import {
  ConnectionMessage,
  ConnectionResponse,
  ConnectionStatus,
  IntegrationMarketingResponse,
  SupportedContexts,
} from '@vendasta/platform-integrations';
import { EMPTY, Observable, of } from 'rxjs';
import { catchError, debounceTime, distinctUntilChanged, map, switchMap } from 'rxjs/operators';
import { CardDataService } from './card-data.service';
import { IntegrationCardComponent } from './integration-card/integration-card.component';
import { PreconnectAlertComponent } from './custom-banner/preconnect-alert/preconnect-alert.component';
import { DisconnectAlertComponent } from './custom-banner/disconnect-alert/disconnect-alert.component';
import { User } from '@vendasta/iamv2';
import { UtilService } from './common/util.service';
import { IntegrationsBannerComponent } from './integrations-banner/integrations-banner.component';
import { IntegrationCard } from './model/Integration-connections-detail';
import { CookieService } from 'ngx-cookie-service';
import { GalaxyI18NModule } from '@vendasta/galaxy/i18n';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { GalaxyPageNoAccessUnauthorizedModule } from '@vendasta/galaxy/page-no-access-unauthorized';
import { ConnectionMessageComponent } from './custom-banner/connection-message/connection-message.component';
import { UserAction } from './const';
import { SearchBarComponent } from './search-bar/search-bar.component';

@Component({
  selector: 'platform-integration',
  templateUrl: './platform-integrations.component.html',
  styleUrls: ['./platform-integrations.component.scss'],
  imports: [
    CommonModule,
    MatTabsModule,
    IntegrationCardComponent,
    PlatformIntegrationsI18nModule,
    PreconnectAlertComponent,
    DisconnectAlertComponent,
    GalaxyEmptyStateModule,
    MatIconModule,
    MatButtonModule,
    GalaxyAlertModule,
    TranslateModule,
    GalaxyLoadingSpinnerModule,
    GalaxyPageModule,
    GalaxyI18NModule,
    GalaxyPageNoAccessUnauthorizedModule,
    IntegrationsBannerComponent,
    ConnectionMessageComponent,
    SearchBarComponent,
  ],
})
export class PlatformIntegrationsComponent implements OnInit {
  protected tab = UserAction.BROWSE;
  protected isLoading = false;
  protected readonly userAction = UserAction;
  protected readonly route = inject(Router);
  protected readonly SupportedContexts = SupportedContexts;

  private readonly activeRoute = inject(ActivatedRoute);
  private readonly destroyRef = inject(DestroyRef);
  private readonly cardService = inject(CardDataService);
  private readonly utilService = inject(UtilService);
  private readonly cookieService = inject(CookieService);
  private readonly router = inject(Router);

  protected readonly searchTerm = signal('');
  private readonly allIntegrations = signal<IntegrationMarketingResponse[]>([]);
  private readonly integrationCards$ = toObservable(this.searchTerm).pipe(
    debounceTime(300),
    distinctUntilChanged(),
    switchMap((term) =>
      this.cardService.getIntegrationsList(term).pipe(
        catchError(() => {
          return of([]);
        }),
      ),
    ),
  );

  protected readonly noAccess$ = this.integrationCards$.pipe(
    map(() => false),
    catchError((err) => (err.status === 403 ? of(true) : of(false))),
  );

  protected readonly emptyPageLogo = this.utilService.getImageSrc('empty_page.png');
  private readonly snackbarService = inject(SnackbarService);
  private readonly connectionList = signal<ConnectionResponse[]>([]);
  protected readonly integrationCards = toSignal(this.integrationCards$);
  protected readonly userDetails = signal<Map<string, User | null>>(new Map<string, User | null>());

  protected readonly manageTabCards: Signal<IntegrationCard[]> = computed(() => {
    const connectionList = this.connectionList();
    const userDetails = this.userDetails();
    const integrationList = this.allIntegrations();
    const result: IntegrationCard[] = [];
    const qboPersonalPendingDisconnectConnectionID = this.cookieService.get('qbopersonal-disconnect');
    const instagramPendingDisconnectConnectionID = this.cookieService.get('instagram-disconnect');

    try {
      const integrations = new Map<string, IntegrationMarketingResponse>();
      integrationList?.forEach((integration: IntegrationMarketingResponse) => {
        integrations.set(integration.integrationId, integration);
      });
      connectionList.forEach((connection) => {
        if (
          qboPersonalPendingDisconnectConnectionID !== connection.connectionId &&
          instagramPendingDisconnectConnectionID !== connection.connectionId
        ) {
          result.push({
            namespace: this.namespace(),
            connection,
            integration: integrations.get(connection.integrationId),
            user: userDetails.get(connection.userId) || undefined,
          });
        }
      });
    } catch (error) {
      return result;
    }
    const sortedIntegrationList = result.sort((a, b) => {
      if (!a.integration || !b.integration) {
        return 0;
      }
      return a.integration.displayName.localeCompare(b.integration.displayName);
    });

    return sortedIntegrationList;
  });

  protected readonly browseTabCards: Signal<IntegrationCard[]> = computed(() => {
    const integrationList = this.integrationCards();
    const connectionList = this.connectionList();
    const qboPersonalPendingDisconnectConnectionID = this.cookieService.get('qbopersonal-disconnect');
    const instagramPendingDisconnectConnectionID = this.cookieService.get('instagram-disconnect');

    const connections = new Map<string, ConnectionResponse>();
    connectionList?.forEach((connection) => {
      if (
        qboPersonalPendingDisconnectConnectionID !== connection.connectionId &&
        instagramPendingDisconnectConnectionID !== connection.connectionId
      ) {
        connections.set(connection.integrationId, connection);
      }
    });

    const result: IntegrationCard[] = [];
    integrationList?.forEach((integration) => {
      result.push({
        namespace: this.namespace(),
        integration,
        connection: connections.get(integration.integrationId),
      });
    });

    const sortedIntegrationList = result.sort((a, b) => {
      if (!a.integration || !b.integration) {
        return 0;
      }
      return a.integration.displayName.localeCompare(b.integration.displayName);
    });

    return sortedIntegrationList;
  });

  constructor(
    @Optional()
    @Inject(PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$)
    public readonly partnerId$: Observable<string> | null,
    @Inject(PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$)
    public readonly namespace$: Observable<string>,
    @Inject(PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$)
    public readonly context: SupportedContexts,
  ) {}

  protected readonly disconnectedCards: Signal<IntegrationCard[]> = computed(() => {
    return this.manageTabCards()?.filter((card) => card?.connection?.status === ConnectionStatus.DISCONNECTED);
  });

  protected readonly preConnectedCards: Signal<IntegrationCard[]> = computed(() => {
    return this.manageTabCards()?.filter((card) => card?.connection?.status === ConnectionStatus.PRECONNECTED);
  });

  protected readonly browseDisconnectedCards: Signal<IntegrationCard[]> = computed(() => {
    return this.browseTabCards()?.filter((card) => card?.connection?.status === ConnectionStatus.DISCONNECTED);
  });

  protected readonly browsePreConnectedCards: Signal<IntegrationCard[]> = computed(() => {
    return this.browseTabCards()?.filter((card) => card?.connection?.status === ConnectionStatus.PRECONNECTED);
  });

  protected readonly connectedMessages: Signal<ConnectionMessage[]> = computed(() => {
    return this.manageTabCards()
      ?.flatMap((card) => card?.connection?.connectionMessages || [])
      .filter((message): message is ConnectionMessage => !!message);
  });

  protected readonly namespace = toSignal(this.namespace$);

  private setConnections(queryTab: number) {
    this.isLoading = true;
    const namespace = this.namespace();
    if (!namespace) {
      this.isLoading = false;
      return;
    }
    this.cardService
      .getConnectionsList(namespace)
      .pipe(
        map((connections) => {
          this.connectionList.set(connections);
          const userIds: string[] = connections
            ?.filter((connection) => connection?.userId)
            .map((connection) => connection.userId);
          const isConnectionPresent = connections?.some(
            (connection) =>
              connection.status === ConnectionStatus.CONNECTED ||
              connection.status === ConnectionStatus.DISCONNECTED ||
              connection.status === ConnectionStatus.PRECONNECTED,
          );

          switch (queryTab) {
            case UserAction.MANAGE:
              this.tab = UserAction.MANAGE;
              break;
            case UserAction.BROWSE:
              this.tab = UserAction.BROWSE;
              break;
            default:
              if (isConnectionPresent) {
                this.tab = UserAction.MANAGE;
              } else {
                this.tab = UserAction.BROWSE;
              }
          }

          if (!isConnectionPresent) {
            this.isLoading = false;
          }
          return userIds;
        }),
        switchMap((userIds) => {
          this.isLoading = false;
          return this.setUserDetails(userIds);
        }),
        catchError((err) => {
          console.log('error while fetching connections: ', err);
          return EMPTY;
        }),
      )

      .subscribe();
  }

  private setUserDetails(userIds: string[]) {
    return this.cardService.getMultiByUserId(userIds).pipe(
      map((users) => {
        const usersMap: Map<string, User | null> = new Map<string, User | null>(
          users
            .filter((user) => user?.userId)
            .map((user) => [user.userId, user.firstName || user.lastName ? user : null]),
        );
        this.userDetails.set(usersMap);
      }),
      catchError((err) => {
        console.error('error in setting user details: ', err);
        return EMPTY;
      }),
    );
  }

  changeTab(tab: UserAction) {
    this.route.navigate(['.'], {
      relativeTo: this.activeRoute,
      queryParams: { tab: tab },
    });
  }

  previousPageUrl(): string {
    if (this.context === SupportedContexts.PI_CONTEXT_PARTNER) {
      return `/settings`;
    } else if (this.context === SupportedContexts.PI_CONTEXT_SMB) {
      return `/account/location/${this.namespace()}/administration`;
    }
    return '';
  }

  ngOnInit(): void {
    // Load all integrations for manage tab first
    this.cardService
      .getIntegrationsList()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((integrations) => {
        this.allIntegrations.set(integrations);
      });
    const source = 'sso';
    this.activeRoute.queryParams
      .pipe(
        map((param) => {
          const queryTab = Number(param['tab']);
          this.setConnections(queryTab);
          const isSSO = param['src'] ? param['src'] : '';
          const errorCode = param['error'] ? param['error'] : '';
          if (errorCode) {
            const errorDescription = param['error_description']
              ? param['error_description']
              : 'INTEGRATION_CARD.CONNECTION_ERROR_SSO';
            this.snackbarService.openErrorSnack(errorDescription);
            this.route.navigate(['.'], { relativeTo: this.activeRoute, queryParams: { tab: UserAction.BROWSE } });
          } else if (isSSO === source) {
            this.snackbarService.openSuccessSnack('INTEGRATION_CARD.CREATE_CONNECTION_SUCCESS');
            this.route.navigate(['.'], { relativeTo: this.activeRoute, queryParams: { tab: UserAction.MANAGE } });
          }
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  onSearchTermChange(term: string) {
    this.searchTerm.set(term);
  }
}
