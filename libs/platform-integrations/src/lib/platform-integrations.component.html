<glxy-page [pagePadding]="false">
  @if (isLoading) {
    <glxy-loading-spinner></glxy-loading-spinner>
  } @else {
    @if ((noAccess$ | async) === true) {
      <glxy-page-no-access-unauthorized />
    } @else {
      <div class="custom-tab-bar">
        <mat-tab-group [(selectedIndex)]="tab" (selectedIndexChange)="changeTab($event)">
          <mat-tab class="small-tab-label" label="{{ 'INTEGRATION_CARD.BROWSE' | translate }}">
            <div class="search-bar-container">
              <platform-integration-search-bar
                class="full-width"
                [searchTerm]="searchTerm()"
                (searchTermChange)="onSearchTermChange($event)"
              ></platform-integration-search-bar>
            </div>
            <div class="container">
              <div class="tab-container">
                <div class="alert-margin">
                  <platform-integration-preconnect-alert
                    [preConnectedCards]="browsePreConnectedCards"
                  ></platform-integration-preconnect-alert>
                </div>
                <div class="alert-margin">
                  <platform-integration-disconnect-alert
                    [disconnectedCards]="browseDisconnectedCards"
                  ></platform-integration-disconnect-alert>
                </div>
                <platform-integration-connection-message [messages]="connectedMessages()">
                </platform-integration-connection-message>
                <div class="row">
                  @for (card of browseTabCards(); track card) {
                    @if (card.integration) {
                      <div class="integration-card-container w-100 browse-tab">
                        <platform-integration-card
                          class="w-100"
                          [integration]="card.integration"
                          [tab]="tab"
                          [connection]="card.connection"
                        ></platform-integration-card>
                      </div>
                    }
                  }
                </div>
              </div>
            </div>
          </mat-tab>
          <mat-tab class="small-tab-label" label="{{ 'INTEGRATION_CARD.MANAGE' | translate }}">
            <div class="container">
              <div class="tab-container">
                @if (context === SupportedContexts.PI_CONTEXT_SMB) {
                  <platform-integration-banner
                    (navigateToBrowse)="changeTab(userAction.BROWSE)"
                  ></platform-integration-banner>
                }
                <div class="alert-margin">
                  <platform-integration-preconnect-alert
                    [preConnectedCards]="preConnectedCards"
                  ></platform-integration-preconnect-alert>
                </div>
                <div class="alert-margin">
                  <platform-integration-disconnect-alert
                    [disconnectedCards]="disconnectedCards"
                  ></platform-integration-disconnect-alert>
                </div>
                <platform-integration-connection-message [messages]="connectedMessages()">
                </platform-integration-connection-message>
                <div class="row">
                  @for (card of manageTabCards(); track card) {
                    @if (card.integration) {
                      <div class="integration-card-container w-100">
                        <platform-integration-card
                          class="w-100"
                          [integration]="card.integration"
                          [connection]="card.connection"
                          [user]="card.user"
                          [tab]="tab"
                        ></platform-integration-card>
                      </div>
                    }
                  }
                </div>
                @if (!manageTabCards()?.length) {
                  <glxy-empty-state class="empty" [size]="'normal'">
                    <glxy-empty-state-hero>
                      <img [src]="emptyPageLogo" alt="emptypage" />
                    </glxy-empty-state-hero>
                    <glxy-empty-state-title>{{
                      'INTEGRATION_CARD.EMPTY_PAGE_HEADING' | translate
                    }}</glxy-empty-state-title>
                    <p>{{ 'INTEGRATION_CARD.EMPTY_PAGE_DESCRIPTION' | translate }}</p>
                    <glxy-empty-state-actions>
                      <button mat-flat-button color="primary" (click)="changeTab(userAction.BROWSE)">
                        Browse integrations
                      </button>
                    </glxy-empty-state-actions>
                  </glxy-empty-state>
                }
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
        <div class="empty-space"></div>
      </div>
    }
  }
  <glxy-page-toolbar>
    <glxy-page-nav>
      <glxy-page-nav-button
        [previousPageUrl]="previousPageUrl()"
        [previousPageTitle]="'SETTINGS.PAGE_TITLE' | translate"
      >
      </glxy-page-nav-button>
    </glxy-page-nav>
    <glxy-page-title>{{ 'INTEGRATION_PAGE.PLATFORM_INTEGRATION' | translate }}</glxy-page-title>
  </glxy-page-toolbar>
</glxy-page>
