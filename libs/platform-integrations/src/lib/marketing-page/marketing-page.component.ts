import { CommonModule, Location as LocationService } from '@angular/common';
import { Component, Inject, inject, ViewEncapsulation, DestroyRef } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Router } from '@angular/router';
import { Environment, EnvironmentService, SessionService } from '@galaxy/core';
import {
  PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_MARKET_ID_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$,
  PlatformIntegrationsI18nModule,
} from '@galaxy/platform-integrations/shared';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { User } from '@vendasta/iamv2';
import {
  ConnectionMethods,
  ConnectionStatus,
  CreateConnectionResponse,
  SupportedContexts,
} from '@vendasta/platform-integrations';
import { ColorPickerModule } from 'ngx-color-picker';
import { BehaviorSubject, combineLatest, EMPTY, filter, map, Observable, of, switchMap, tap } from 'rxjs';
import { catchError, take } from 'rxjs/operators';
import { CardDataService } from '../card-data.service';
import { IntegrationUtilService } from '../common/integration-util.service';
import { DynamicFormComponent } from '../connection-config/components/dynamic-form/dynamic-form.component';
import { IntegrationCardComponent } from '../integration-card/integration-card.component';
import { LegacyAuthService } from '../legacy-auth.service';
import { ConnectionIntegrationDetail, CustomField, IntegrationType } from '../model/connection-integration-detail';
import { IntegrationConnectionsDetail } from '../model/Integration-connections-detail';
import { ButtonConfigPipe } from './button-config-pipe';
import { ButtonTemplateComponent } from './button-template/button-template.component';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { App } from '@vendasta/marketplace-apps';
import { ResourcesComponent } from '../config-page/resources/resources.component';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { IAMService, PersonaType } from '@vendasta/iam';
import { WorksWithComponent } from './works-with/works-with.component';
import { UserAction } from '../const';
import { NotFoundComponent } from '../common/not-found/not-found';

const ZAPIER_BUSINESS_APP = 'https://zapier.com/apps/business-app/integrations';
const ZAPIER_PARTNER_APP = 'https://zapier.com/apps/vendasta/integrations';
const ZAPIER_DEMO_APP = 'https://zapier.com/developer/public-invite/192324/6793394683cc25ed38ca80f93c530f71/';

export interface ButtonConfigModel {
  title: string;
  buttonType: string;
  color: string;

  action?: string;
  isDisabled?: boolean;
}

export interface ProductInfo {
  appId: string;
  iconUrl: string;
  name: string;
}

@Component({
  selector: 'platform-integration-marketing-page',
  imports: [
    CommonModule,
    MatButtonModule,
    MatChipsModule,
    MatDividerModule,
    MatIconModule,
    PlatformIntegrationsI18nModule,
    GalaxyPipesModule,
    TranslateModule,
    IntegrationCardComponent,
    GalaxyPageModule,
    GalaxyBadgeModule,
    ColorPickerModule,
    GalaxyAlertModule,
    ButtonTemplateComponent,
    ButtonConfigPipe,
    GalaxyLoadingSpinnerModule,
    ResourcesComponent,
    GalaxyTooltipModule,
    WorksWithComponent,
    NotFoundComponent,
  ],
  templateUrl: './marketing-page.component.html',
  styleUrls: ['./marketing-page.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class MarketingPageComponent {
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly cardService = inject(CardDataService);
  private readonly legacyAuthService = inject(LegacyAuthService);
  private readonly snackbarService = inject(SnackbarService);
  private readonly translate = inject(TranslateService);
  private readonly router = inject(Router);
  private readonly environmentService = inject(EnvironmentService);
  private readonly destroyRef = inject(DestroyRef);
  private model = inject(MatDialog);
  private readonly integrationUtilService = inject(IntegrationUtilService);
  protected readonly connectionMethods = ConnectionMethods;
  protected readonly userAction = UserAction;
  protected isLoading = true;
  protected readonly IntegrationType = IntegrationType;
  protected readonly SupportedContexts = SupportedContexts;

  constructor(
    @Inject(PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$) public readonly namespace$: Observable<string>,
    @Inject(PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$)
    public readonly partnerId$: Observable<string>,
    @Inject(PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$)
    @Inject(PLATFORM_INTEGRATIONS_MARKET_ID_INJECTION_TOKEN$)
    public readonly marketId$: Observable<string>,
    @Inject(PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$)
    public readonly context: SupportedContexts,
    @Inject('IS_IMPERSONATING')
    public readonly isImpersonating$: Observable<boolean>,
    private readonly confirmationModal: OpenConfirmationModalService,
    private locationService: LocationService,
    private sessionService: SessionService,
    private iamService: IAMService,
  ) {}

  public connection_status: ConnectionStatus = ConnectionStatus.UNSPECIFIED_STATUS;
  protected readonly connectionStatus = ConnectionStatus;
  protected readonly connectionStatus$ = new BehaviorSubject(this.connection_status);
  private readonly integrationIdFromParam$ = this.activatedRoute.paramMap.pipe(
    map((params) => params.get('integrationId')),
  );
  private readonly integrationList$ = this.cardService.getIntegrationsList();

  private readonly integrationId$ = combineLatest([this.integrationIdFromParam$, this.integrationList$]).pipe(
    map(([integrationIdFromParam, integrationList]) => {
      const integration = integrationList.find(
        (integration) =>
          integration.integrationId === integrationIdFromParam ||
          integration.integrationType === integrationIdFromParam,
      );
      if (!integration) {
        const path = `${this.getCurrentPath()}/not-found`;
        this.router.navigate([path]);
      }
      return integration.integrationId;
    }),
  );

  private readonly connectionList$ = combineLatest([this.connectionStatus$, this.namespace$, this.integrationId$]).pipe(
    filter(([_, namespace, integrationId]) => !!namespace && !!integrationId),
    map(([_, namespace, integrationId]) => ({ namespace, integrationId })),
    switchMap(({ namespace, integrationId }) =>
      namespace && integrationId ? this.cardService.getConnectionsList(namespace, integrationId as string) : of([]),
    ),
  );
  private readonly cardDetail$ = combineLatest([this.integrationList$, this.connectionList$, this.integrationId$]).pipe(
    map(([integrationList, connectionList, integrationId]) => {
      connectionList = connectionList?.filter((connection) => {
        if (connection.integrationId === integrationId) {
          return true;
        }
        return false;
      });
      const integrationDetail = integrationList?.find(
        (integration) => integration.integrationId === integrationId || integration.integrationType === integrationId,
      );
      if (integrationDetail) {
        this.connection_status = connectionList?.[0]?.status || ConnectionStatus.UNSPECIFIED_STATUS;
        connectionList = connectionList?.filter(
          (connect) =>
            connect.integrationId === integrationId &&
            (connect.status === ConnectionStatus.CONNECTED ||
              connect.status === ConnectionStatus.DISCONNECTED ||
              connect.status === ConnectionStatus.PRECONNECTED),
        );
        return { integration: integrationDetail, connections: connectionList ? connectionList : [] };
      }
      return {};
    }),
  );

  protected readonly productInfo$: Observable<ProductInfo[]> = combineLatest([
    this.integrationList$,
    this.integrationId$,
  ]).pipe(
    switchMap(([integrationList, integrationId]) => {
      const integration = integrationList.find((integrations) => integrations.integrationId === integrationId);
      if (integration && integration.additionalInfo?.productIds) {
        const productIds = integration.additionalInfo.productIds;
        return this.cardService.getProductDetailsByProductIds(productIds).pipe(
          map((apps: App[]) => {
            const validApps = apps.filter((app) => app && app.key?.appId);
            return validApps.map((app) => ({
              appId: app.key.appId,
              iconUrl: app.sharedMarketingInformation.iconUrl || '',
              name: app.sharedMarketingInformation.name || '',
            })) as ProductInfo[];
          }),
        );
      }

      return of([]);
    }),
  );

  protected readonly isSMB$: Observable<boolean> = combineLatest([
    this.partnerId$,
    this.sessionService.getSessionId(),
  ]).pipe(
    switchMap(([partnerID, sessionId]) => {
      if (!sessionId) return of(false);
      return this.iamService.getSubjectBySession(sessionId, PersonaType.smb, partnerID).pipe(
        map((persona) => !!persona?.userId),
        catchError(() => of(false)),
      );
    }),
  );

  protected readonly cards$: Observable<ConnectionIntegrationDetail[]> = combineLatest([
    this.integrationList$,
    this.connectionList$,
    this.integrationId$,
  ]).pipe(
    switchMap(([integrationList, connectionList, integrationId]) => {
      connectionList = connectionList?.filter((connection) => {
        if (connection.integrationId === integrationId) {
          return true;
        }
        return false;
      });
      const userIds: string[] = connectionList
        ?.filter((connection) => connection?.userId)
        .map((connection) => connection.userId);
      return this.cardService.getMultiByUserId(userIds).pipe(
        map((usersInfo) => {
          const usersMap: Map<string, User> = new Map<string, User>(usersInfo.map((user) => [user.userId, user]));
          return connectionList?.reduce((result: ConnectionIntegrationDetail[], connection) => {
            const integrationDetails = integrationList?.find(
              (integration) => integration.integrationId === integrationId,
            );
            this.connection_status = connectionList?.[0]?.status || ConnectionStatus.UNSPECIFIED_STATUS;
            if (integrationDetails && connection.integrationId === integrationId) {
              const userInfo = usersMap.get(connection.userId) || null;
              result.push({ ...connection, integration: integrationDetails, userInfo: userInfo });
            }
            return result;
          }, []);
        }),
      );
    }),
  );

  private readonly getBusinessName$: Observable<string> = combineLatest([this.partnerId$, this.marketId$]).pipe(
    switchMap(([partnerID, marketID]) => {
      return this.cardService.getPlatformName(partnerID, marketID);
    }),
  );

  private readonly getBusinessName = toSignal(this.getBusinessName$);
  protected pageData$ = combineLatest([
    this.cardDetail$,
    this.namespace$,
    this.integrationId$,
    this.isSMB$,
    this.isImpersonating$,
  ]).pipe(
    map(([cardDetail, namespace, integrationId, isSMB, isImpersonatedUser]) => {
      this.isLoading = false;
      let platformName = 'the platform';
      const businessName = this.getBusinessName();
      if (this.context === SupportedContexts.PI_CONTEXT_SMB && businessName) {
        platformName = businessName;
      }

      if (cardDetail.integration) {
        cardDetail.integration.longDescription = cardDetail.integration.longDescription
          .replace('{{agid}}', namespace || '')
          .replaceAll('{{platformName}}', platformName);
      }
      return {
        cardDetail: cardDetail,
        namespace: namespace,
        integrationId: integrationId,
        isSMB: isSMB,
        isImpersonatedUser: isImpersonatedUser,
      };
    }),
  );

  marketingPreconnectMessage(cardDetail: ConnectionIntegrationDetail): string {
    switch (cardDetail?.integration?.connectionMethod) {
      case this.connectionMethods.OAUTH2:
        return this.translate.instant('INTEGRATION_CARD.PRECONNECT_SSO_BASED_WARNING_PROBLEM', {
          title: cardDetail.integration.displayName,
        });
      default:
        return this.translate.instant('INTEGRATION_CARD.PRECONNECT_WARNING_PROBLEM', {
          title: cardDetail.integration.displayName,
        });
    }
  }

  //Used to get path of URL to make it generic for Both PC and BusinessApp
  // Example, if URL is https://vendasta-university-dot-vbc-demo.appspot.com/account/location/AG-JP5VN52N7P/settings/integrations/a085804b-e25f-4418-bfb0-56028916cf8d
  // we extract only account/location/AG-JP5VN52N7P/settings/integrations , which used to redirect.
  getCurrentPath(): string {
    let path = window.location.pathname.slice(1);
    const lastIndex = path.lastIndexOf('/');
    path = path.substring(0, lastIndex);
    return path;
  }

  manageConnection() {
    const path = this.getCurrentPath();
    this.router.navigate([path], {
      queryParams: { tab: UserAction.MANAGE },
    });
  }

  navigateBack(): string {
    const path = this.getCurrentPath() + `?tab=${UserAction.BROWSE}`;
    return path;
  }

  /**
   * Redirects to connection setting page for a provided connection id
   * Currently redirects to {server}/account/location/{CURRENT_AG}/settings/integrations/config/{CONNECTION_ID}
   *
   * @remarks Applicable Cases
   *  - This is applicable for all integration where the number of connection is set to one. On clicking manage connection on Marketing page this will redirect to the individual connection setting page
   *
   * @param connectionId
   */
  manageIndividualConnection(connectionId: string) {
    const path = this.getCurrentPath();

    // redirect to {path}/config/{connectionId} which is the connection setting page
    this.router.navigate([`${path}/config/${connectionId}`]);
  }

  buttonAction(
    data: { cardDetail: IntegrationConnectionsDetail; namespace: string; integrationId: string },
    action: string,
    isDisabled: boolean,
  ) {
    if (isDisabled) {
      return;
    }
    const connectionMethod = data.cardDetail.integration?.connectionMethod;
    const maxConnections = data.cardDetail.integration?.maxConnections;
    const connectionStatus = data.cardDetail.connections[0]?.status;
    if (data.cardDetail.integration?.integrationType.toLowerCase() === IntegrationType.ZAPIER.toLowerCase()) {
      this.OpenFormBasedOnIntegrationType(data);
    }
    if (
      (data.cardDetail?.connections?.length ?? 0) >= maxConnections &&
      connectionStatus !== ConnectionStatus.PRECONNECTED
    ) {
      if ((data.cardDetail?.connections?.length ?? 0) > 1) {
        this.manageConnection();
      } else {
        this.manageIndividualConnection(data.cardDetail.connections[0]?.connectionId);
      }
    } else if (connectionMethod == ConnectionMethods.OAUTH2) {
      if (connectionStatus == ConnectionStatus.PRECONNECTED) {
        if (action === 'CANCEL') {
          this.cancelConnection(data);
        } else if (action === 'RESUME') {
          this.manageConnection();
        }
      } else {
        this.OpenFormBasedOnIntegrationType(data);
      }
    } else if (connectionMethod == ConnectionMethods.VENDOR_MANAGED) {
      if (connectionStatus == ConnectionStatus.PRECONNECTED) {
        const options = {
          title: this.translate.instant('INTEGRATION_CARD.CANCEL_VENDOR_MANAGED_CONNECTION_REQUEST?', {
            integrationDisplayName: data.cardDetail.integration?.displayName,
          }),
          message: this.translate.instant(
            'INTEGRATION_CARD.THIS_WILL_STOP_CURRENT_CONNECTION_PROCESS_FOR_THIS_INTEGRATION',
          ),
          confirmButtonText: this.translate.instant('INTEGRATION_CARD.CANCEL_CONNECTION_REQUEST'),
          successMessage: this.translate.instant('INTEGRATION_CARD.CONNECTION_REQUEST_CANCELLED_SUCCESFULLY'),
          errorMessage: this.translate.instant('INTEGRATION_CARD.FAILED_TO_CANCEL_CONNECTION_REQUEST'),
        };
        this.cancelConnectionWithDialog(data, options);
      } else {
        this.OpenFormBasedOnIntegrationType(data);
      }
    } else if (connectionMethod == ConnectionMethods.APIKEY) {
      if (connectionStatus == ConnectionStatus.PRECONNECTED) {
        const options = {
          title: this.translate.instant('INTEGRATION_CARD.TITLE', {
            title: data.cardDetail.integration?.displayName,
          }),
          message: this.translate.instant('INTEGRATION_CARD.MESSAGE'),
          confirmButtonText: this.translate.instant('INTEGRATION_CARD.DISCONNECT'),
          successMessage: this.translate.instant('INTEGRATION_CARD.DELETE_SUCCESS_MESSAGE', {
            title: data.cardDetail.integration?.displayName,
          }),
          errorMessage: this.translate.instant('INTEGRATION_CARD.DELETE_ERROR_MESSAGE'),
        };
        this.cancelConnectionWithDialog(data, options);
      } else {
        this.OpenFormBasedOnIntegrationType(data);
      }
    } else if (data.cardDetail.integration?.connectionMethod === ConnectionMethods.LEGACY_OAUTH) {
      this.isLoading = true;
      this.legacyAuthService.HandleLegacyIntegration(data);
    }
  }

  OpenFormBasedOnIntegrationType(data: {
    cardDetail: IntegrationConnectionsDetail;
    namespace: string;
    integrationId: string;
  }) {
    const integrationType = data.cardDetail.integration?.integrationType;
    if (integrationType?.toLowerCase() === IntegrationType.ZAPIER.toLowerCase()) {
      this.openZapierSyncSettingModel();
    } else {
      this.openSyncSettingModel(data);
    }
  }

  openSyncSettingModel(cardDetail: {
    cardDetail: IntegrationConnectionsDetail;
    namespace: string;
    integrationId: string;
  }) {
    const dialogConfig = new MatDialogConfig();
    const integrationConnectionMethod = cardDetail.cardDetail.integration?.connectionMethod;
    const integrationDetails = cardDetail.cardDetail.integration;
    const syncSettingIds = this.integrationUtilService.getFieldIdsFromConfig(
      integrationDetails?.preconnectFormFields?.fields,
    );
    dialogConfig.width = '660px';
    dialogConfig.data = {
      integration: integrationDetails,
    };
    if (integrationConnectionMethod === ConnectionMethods.VENDOR_MANAGED) {
      const dialogRef = this.model.open(DynamicFormComponent, dialogConfig);
      dialogRef.componentInstance.saveSyncSettingsEvent.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((data) => {
        this.createNewConnection(cardDetail, this.mapToCustomFields(data, syncSettingIds));
      });
    } else if (integrationConnectionMethod === ConnectionMethods.OAUTH2) {
      const preConnectFormFields = cardDetail.cardDetail.integration?.preconnectFormFields;
      if (!preConnectFormFields || Object.keys(preConnectFormFields).length <= 0) {
        this.createNewConnection(cardDetail, []);
      } else {
        const dialogRef = this.model.open(DynamicFormComponent, dialogConfig);
        dialogRef.componentInstance.saveSyncSettingsEvent
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe((data) => {
            this.createNewConnection(cardDetail, this.mapToCustomFields(data, syncSettingIds));
          });
      }
    } else if (integrationConnectionMethod === ConnectionMethods.APIKEY) {
      const dialogRef = this.model.open(DynamicFormComponent, dialogConfig);
      dialogRef.componentInstance.saveSyncSettingsEvent.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((data) => {
        this.createNewConnection(cardDetail, this.mapToCustomFields(data, syncSettingIds));
      });
    }
  }

  // Zapier Sync Settings
  openZapierSyncSettingModel() {
    if (this.environmentService.getEnvironment() === Environment.DEMO) {
      window.open(ZAPIER_DEMO_APP, '_blank');
    } else if (this.context === SupportedContexts.PI_CONTEXT_SMB) {
      window.open(ZAPIER_BUSINESS_APP, '_blank');
    } else {
      window.open(ZAPIER_PARTNER_APP, '_blank');
    }
  }

  mapToCustomFields(data: any, settingIds: string[]): CustomField[] {
    const customFields: CustomField[] = [];
    settingIds.forEach((id) => {
      if (!(id === 'reviewRequestData' && this.context === SupportedContexts.PI_CONTEXT_PARTNER)) {
        customFields.push({
          label: id,
          value: data[id].toString(),
        });
      }
    });
    return customFields;
  }

  // This function removes the active route from a URL path. Really it could be re-labeled as removing a sub-path.
  // The path and route must be passed without trailing slashes.
  removeActiveRouteFromPath(path: string, route: string) {
    if (route !== '') {
      // Remove the route from the end of the path
      return LocationService.stripTrailingSlash(path.slice(0, -route.length));
    }
    return path;
  }

  // getPathWithoutActiveRoute gets the current URL path, then removes the active route from it.
  getPathWithoutActiveRoute() {
    // Get entire path including the current route, then split to remove the query parameters
    const fullPath = this.locationService.path().split('?')[0];
    const activeRoute = decodeURIComponent(this.activatedRoute.snapshot.url.toString());

    return this.removeActiveRouteFromPath(
      LocationService.stripTrailingSlash(fullPath),
      LocationService.stripTrailingSlash(activeRoute),
    );
  }

  createNewConnection(
    cardDetail: { cardDetail: IntegrationConnectionsDetail; namespace: string; integrationId: string },
    syncData: CustomField[],
  ) {
    if (!cardDetail) return null;
    const { cardDetail: card, namespace, integrationId } = cardDetail;
    this.isLoading = true;

    const handleError = () => {
      this.isLoading = false;
      this.snackbarService.openErrorSnack('INTEGRATION_CARD.CREATE_ERROR_MESSAGE');
      return EMPTY;
    };

    const handleSuccess = (resp: CreateConnectionResponse) => {
      this.isLoading = false;
      return of(resp);
    };

    const createConnection = () => this.cardService.createConnection(namespace, integrationId, syncData, this.context);

    switch (card.integration.connectionMethod) {
      case ConnectionMethods.OAUTH2:
        return createConnection()
          .pipe(
            switchMap((resp: CreateConnectionResponse) => {
              const mutableQP = { ...this.activatedRoute.snapshot.queryParams };
              mutableQP['src'] = 'sso';
              const basePath = this.getPathWithoutActiveRoute().split('/');
              const nextURL =
                window.location.origin +
                this.router
                  .createUrlTree([...basePath, 'config', resp.connectionId], { queryParams: mutableQP })
                  .toString();
              return this.cardService.performSSO(namespace, integrationId, resp.connectionId, nextURL);
            }),
            catchError(handleError),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe();
      case ConnectionMethods.VENDOR_MANAGED:
        return createConnection()
          .pipe(
            switchMap((resp: CreateConnectionResponse) => {
              this.snackbarService.openSuccessSnack('INTEGRATION_CARD.CREATE_CONNECTION_SUCCESS');
              this.connectionStatus$.next(ConnectionStatus.PRECONNECTED);
              return handleSuccess(resp);
            }),
            tap((resp) => {
              this.router.navigate([
                `/account/location/${namespace}/administration/integrations/config/${resp.connectionId}`,
              ]);
            }),
            catchError(handleError),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe();

      case ConnectionMethods.APIKEY:
        return createConnection()
          .pipe(
            switchMap((resp: CreateConnectionResponse) => {
              this.snackbarService.openSuccessSnack('INTEGRATION_CARD.CREATE_CONNECTION_SUCCESS');
              this.manageIndividualConnection(resp.connectionId);
              return handleSuccess(resp);
            }),
            tap((resp) => {
              this.router.navigate([
                `/account/location/${namespace}/administration/integrations/config/${resp.connectionId}`,
              ]);
            }),
            catchError(handleError),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe();

      default:
        this.snackbarService.openErrorSnack('INTEGRATION_CARD.CONNECTION_NOT_CONFIGURED');
        return null;
    }
  }

  cancelConnectionWithDialog(
    data: {
      namespace: string;
      cardDetail: IntegrationConnectionsDetail;
      integrationId: string;
    },
    options: {
      title: string;
      message: string;
      confirmButtonText: string;
      successMessage: string;
      errorMessage: string;
    },
  ) {
    this.confirmationModal
      .openModal({
        type: 'warn',
        title: options.title,
        message: options.message,
        cancelButtonText: 'Close',
        confirmButtonText: options.confirmButtonText,
      })
      .pipe(take(1))
      .subscribe((confirm) => {
        if (confirm) {
          this.cardService
            .deleteConnection(data.namespace, data.cardDetail.connections[0].connectionId)
            .pipe(
              take(1),
              switchMap(() => {
                this.snackbarService.openSuccessSnack(options.successMessage);
                this.connectionStatus$.next(ConnectionStatus.PRECONNECTED);
                return EMPTY;
              }),
              catchError(() => {
                this.snackbarService.openErrorSnack(options.errorMessage);
                return EMPTY;
              }),
              takeUntilDestroyed(this.destroyRef),
            )
            .subscribe();
        }
      });
  }

  cancelConnection(data: { namespace: string; cardDetail: IntegrationConnectionsDetail; integrationId: string }) {
    this.cardService
      .deleteConnection(data.namespace, data.cardDetail.connections[0].connectionId)
      .pipe(
        take(1),
        switchMap(() => {
          this.snackbarService.openSuccessSnack(
            this.translate.instant('INTEGRATION_CARD.CONNECTION_REQUEST_CANCELLED_SUCCESFULLY'),
          );
          this.connectionStatus$.next(ConnectionStatus.PRECONNECTED);
          return EMPTY;
        }),
        catchError(() => {
          this.snackbarService.openErrorSnack('INTEGRATION_CARD.FAILED_TO_CANCEL_CONNECTION_REQUEST');
          return EMPTY;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
