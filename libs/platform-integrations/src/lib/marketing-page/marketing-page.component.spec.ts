import { TestBed } from '@angular/core/testing';
import { ActivatedRoute, UrlSegment } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ConnectionMethods, CreateConnectionResponse, SupportedContexts } from '@vendasta/platform-integrations';
import { of, throwError } from 'rxjs';
import { CardDataService } from '../card-data.service';
import { MarketingPageComponent } from './marketing-page.component';
import {
  PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_MARKET_ID_INJECTION_TOKEN$,
} from '@galaxy/platform-integrations/shared';
import { SSOService } from '@vendasta/sso';
import { HttpClient } from '@angular/common/http';
import { fakeAsync, tick } from '@angular/core/testing';
import { convertToParamMap } from '@angular/router';
import { Location as LocationService } from '@angular/common';

describe('MarketingPageComponent', () => {
  let component: MarketingPageComponent;
  let cardService: CardDataService;
  let snackbarService: SnackbarService;
  let locationService: LocationService;
  let activatedRoute: ActivatedRoute;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        MarketingPageComponent,
        {
          provide: CardDataService,
          useValue: {
            getIntegrationsList: jest.fn(),
            getConnectionsList: jest.fn(),
            createConnection: jest.fn(),
            performSSO: jest.fn(),
            getRMAppSettings: jest.fn(),
            getRMAppDetail: jest.fn(),
          },
        },
        {
          provide: PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$,
          useValue: of('test_namespace'),
        },
        {
          provide: PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$,
          useValue: of('test_partnerID'),
        },
        {
          provide: 'IS_IMPERSONATING',
          useValue: of(true),
        },
        {
          provide: SnackbarService,
          useValue: {
            openErrorSnack: jest.fn(),
            openSuccessSnack: jest.fn(),
          },
        },
        {
          provide: SSOService,
          useValue: {},
        },
        {
          provide: TranslateService,
          useValue: {},
        },
        {
          provide: SupportedContexts,
          useValue: SupportedContexts,
        },
        {
          provide: PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$,
          useValue: of('PI_CONTEXT'),
        },
        {
          provide: PLATFORM_INTEGRATIONS_MARKET_ID_INJECTION_TOKEN$,
          useValue: of('test_marketID'),
        },
        {
          provide: HttpClient,
          useValue: {},
        },
        {
          provide: 'USER_ID',
          useValue: of('test_userID'),
        },
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of(convertToParamMap({ integrationId: 'notUsedInTests' })),
            snapshot: {
              queryParams: { v: 'testVersion' },
              url: [new UrlSegment('default', {})],
            },
            url: [],
          },
        },
        {
          provide: LocationService,
          useValue: {
            path: jest.fn(),
            // stripTrailingSlash is a copy of the code from the Angular Location library
            stripTrailingSlash: jest.fn((url: string) => {
              const match = url.match(/#|\?|$/);
              const pathEndIdx = (match && match.index) || url.length;
              const droppedSlashIdx = pathEndIdx - (url[pathEndIdx - 1] === '/' ? 1 : 0);
              return url.slice(0, droppedSlashIdx) + url.slice(pathEndIdx);
            }),
          },
        },
      ],
    });

    component = TestBed.inject(MarketingPageComponent);
    cardService = TestBed.inject(CardDataService);
    snackbarService = TestBed.inject(SnackbarService);
    locationService = TestBed.inject(LocationService);
    activatedRoute = TestBed.inject(ActivatedRoute);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should create a new connection using OAuth2 in business app', fakeAsync(() => {
    const agid = 'AG-123';
    const integrationId = '12345';

    const cardDetail = {
      cardDetail: {
        integration: {
          integrationType: 'test_integrationType',
          connectionMethod: ConnectionMethods.OAUTH2,
        },
      } as any,
      namespace: agid,
      integrationId: integrationId,
      customFields: [
        {
          label: 'key',
          value: 'value',
        },
      ],
    };
    const basePath = 'account/location/' + agid + '/settings/integrations';
    const connectionId = 'mockConnectionId';
    const mockCreateConnectionResponse = new CreateConnectionResponse({ connectionId: connectionId });
    const currentRoutePath = integrationId;
    const newRoutePath = 'config/' + connectionId;

    jest.spyOn(cardService, 'createConnection').mockReturnValue(of(mockCreateConnectionResponse));
    jest.spyOn(locationService, 'path').mockReturnValue(basePath + currentRoutePath);
    jest.replaceProperty(activatedRoute.snapshot, 'url', [new UrlSegment(currentRoutePath, {})]);
    jest.spyOn(cardService, 'performSSO').mockReturnValue(of(undefined));

    component.createNewConnection(cardDetail, []);
    tick();

    expect(cardService.createConnection).toHaveBeenCalled();
    expect(cardService.performSSO).toHaveBeenCalledWith(
      cardDetail.namespace,
      cardDetail.integrationId,
      connectionId,
      'http://localhost/' + basePath + '/' + newRoutePath + '?v=testVersion&src=sso',
    );
  }));

  it('should create a new connection using OAuth2 in partner center', fakeAsync(() => {
    const agid = 'AG-123';
    const integrationId = '12345';

    const cardDetail = {
      cardDetail: {
        integration: {
          integrationType: 'test_integrationType',
          connectionMethod: ConnectionMethods.OAUTH2,
        },
      } as any,
      namespace: agid,
      integrationId: integrationId,
      customFields: [
        {
          label: 'key',
          value: 'value',
        },
      ],
    };

    const basePath = 'integrations';
    const connectionId = 'mockConnectionId';
    const mockCreateConnectionResponse = new CreateConnectionResponse({ connectionId: connectionId });
    const currentRoutePath = integrationId;
    const newRoutePath = 'config/' + connectionId;

    jest.spyOn(cardService, 'createConnection').mockReturnValue(of(mockCreateConnectionResponse));
    jest.spyOn(locationService, 'path').mockReturnValue(basePath + currentRoutePath);
    jest.replaceProperty(activatedRoute.snapshot, 'url', [new UrlSegment(currentRoutePath, {})]);
    jest.spyOn(cardService, 'performSSO').mockReturnValue(of(undefined));

    component.createNewConnection(cardDetail, []);
    tick();

    expect(cardService.createConnection).toHaveBeenCalled();
    expect(cardService.performSSO).toHaveBeenCalledWith(
      cardDetail.namespace,
      cardDetail.integrationId,
      connectionId,
      'http://localhost/' + basePath + '/' + newRoutePath + '?v=testVersion&src=sso',
    );
  }));

  it('should create a new connection using OAuth2 when used in a complex route', fakeAsync(() => {
    const agid = 'AG-123';
    const integrationId = '12345';

    const cardDetail = {
      cardDetail: {
        integration: {
          integrationType: 'test_integrationType',
          connectionMethod: ConnectionMethods.OAUTH2,
        },
      } as any,
      namespace: agid,
      integrationId: integrationId,
      customFields: [
        {
          label: 'key',
          value: 'value',
        },
      ],
    };

    const basePath = 'some/path/integrations';
    const connectionId = 'mockConnectionId';
    const mockCreateConnectionResponse = new CreateConnectionResponse({ connectionId: connectionId });
    const newRoutePath = 'config/' + connectionId;
    const complexRoute = 'complex/route/' + integrationId;

    jest.spyOn(cardService, 'createConnection').mockReturnValue(of(mockCreateConnectionResponse));
    jest.spyOn(locationService, 'path').mockReturnValue(basePath + complexRoute);
    jest.replaceProperty(activatedRoute.snapshot, 'url', [new UrlSegment(complexRoute, {})]);
    jest.spyOn(cardService, 'performSSO').mockReturnValue(of(undefined));

    component.createNewConnection(cardDetail, []);
    tick();

    expect(cardService.createConnection).toHaveBeenCalled();
    expect(cardService.performSSO).toHaveBeenCalledWith(
      cardDetail.namespace,
      cardDetail.integrationId,
      connectionId,
      'http://localhost/' + basePath + '/' + newRoutePath + '?v=testVersion&src=sso',
    );
  }));

  it('should handle non-OAuth2 connection method', () => {
    const cardDetail = {
      cardDetail: {
        integration: {
          connectionMethod: 'DummyOAuth2',
        },
      } as any,
      namespace: 'groot',
      integrationId: 'groot123',
    };

    component.createNewConnection(cardDetail, []);
    expect(snackbarService.openErrorSnack).toHaveBeenCalledWith('INTEGRATION_CARD.CONNECTION_NOT_CONFIGURED');
  });

  it('should handle errors during connection creation', fakeAsync(() => {
    const cardDetail = {
      cardDetail: {
        integration: {
          connectionMethod: ConnectionMethods.OAUTH2,
        },
      } as any,
      namespace: 'groot',
      integrationId: 'groot123',
    };
    jest.spyOn(cardService, 'createConnection').mockReturnValue(throwError('Error'));
    component.createNewConnection(cardDetail, []);
    tick();
    expect(snackbarService.openErrorSnack).toHaveBeenCalledWith('INTEGRATION_CARD.CREATE_ERROR_MESSAGE');
  }));
});
