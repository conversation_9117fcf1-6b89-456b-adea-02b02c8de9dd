import { Component, EventEmitter, inject, Inject, Input, Output } from '@angular/core';
import { MatDialogActions, MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { Form<PERSON>uilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import {
  PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$,
  PlatformIntegrationsI18nModule,
} from '@galaxy/platform-integrations/shared';
import { ActionType, FieldType, FormConfig, SupportedContexts } from '@vendasta/platform-integrations';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatRadioModule } from '@angular/material/radio';
import { CommonModule } from '@angular/common';
import { GalaxyInputModule } from '@vendasta/galaxy/input';
import { MatDividerModule } from '@angular/material/divider';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { ActionTypeComponent } from '../../action-type/action-type.component';

@Component({
  selector: 'platform-integration-api-key-based-form',
  imports: [
    MatDialogActions,
    MatIconModule,
    MatDialogTitle,
    MatDialogContent,
    MatButtonModule,
    MatCardModule,
    MatSlideToggleModule,
    TranslateModule,
    ReactiveFormsModule,
    FormsModule,
    MatCheckboxModule,
    MatRadioModule,
    CommonModule,
    GalaxyInputModule,
    MatDividerModule,
    MatInputModule,
    MatFormFieldModule,
    GalaxyFormFieldModule,
    PlatformIntegrationsI18nModule,
    ActionTypeComponent,
  ],
  templateUrl: './apikey-based.component.html',
  styleUrl: './apikey-based.component.scss',
})
export class ApikeyBasedComponent {
  @Input() preconnectFormFields!: FormConfig;
  @Input() integrationDisplayName!: string;
  @Input() dialogRef!: MatDialogRef<any>;
  @Output() saveSyncSettingsEvent = new EventEmitter<any>();
  private formBuilder = inject(FormBuilder);

  constructor(
    @Inject(PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$)
    public readonly context: SupportedContexts,
  ) {}

  ngOnInit(): void {
    this.initializeFormGroup();
  }

  initializeFormGroup() {
    this.syncSettingsForm = this.formBuilder.group({});
    const preconnectFormFields = this.preconnectFormFields?.fields;
    if (preconnectFormFields.length > 0) {
      preconnectFormFields.forEach((field) => {
        const control = this.formBuilder.control(field.defaultValue || '', field.required ? Validators.required : []);
        this.syncSettingsForm.addControl(field.id, control);
      });
    }
  }

  protected actionType = ActionType;
  protected fieldType = FieldType;
  syncSettingsForm!: FormGroup;

  handleAction(action: ActionType) {
    if (action === this.actionType.ACTION_TYPE_CANCEL) {
      this.close();
    } else if (action === this.actionType.ACTION_TYPE_ADD_CONNECTION) {
      if (this.syncSettingsForm.valid) {
        this.continue();
      }
    }
  }

  close() {
    this.dialogRef.close();
  }

  continue() {
    this.saveSyncSettingsEvent.emit(this.syncSettingsForm.getRawValue());
    this.dialogRef.close({ send: true });
  }
}
