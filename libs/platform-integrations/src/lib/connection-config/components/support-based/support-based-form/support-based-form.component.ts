import { Component, EventEmitter, Inject, inject, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogActions, MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { ActionType, FieldType, FormConfig, SupportedContexts } from '@vendasta/platform-integrations';
import { PlatformIntegrationsI18nModule } from '@galaxy/platform-integrations/shared';
import { ActionTypeComponent } from '../../action-type/action-type.component';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { IntegrationUtilService } from '../../../../common/integration-util.service';
import { IntegrationType } from '../../../../model/connection-integration-detail';

import { PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$ } from '@galaxy/platform-integrations/shared';
@Component({
  selector: 'platform-integration-support-based-form',
  imports: [
    MatDialogActions,
    MatIconModule,
    MatDialogTitle,
    MatDialogContent,
    MatButtonModule,
    MatCardModule,
    MatSlideToggleModule,
    TranslateModule,
    ReactiveFormsModule,
    FormsModule,
    MatCheckboxModule,
    CommonModule,
    PlatformIntegrationsI18nModule,
    ActionTypeComponent,
    GalaxyFormFieldModule,
  ],
  templateUrl: './support-based-form.component.html',
  styleUrls: ['./support-based-form.component.scss'],
})
export class SupportBasedFormComponent {
  syncSettingsForm!: FormGroup;
  @Input() preconnectFormFields!: FormConfig;
  @Input() integrationDisplayName!: string;
  @Input() dialogRef!: MatDialogRef<any>;
  @Output() saveSyncSettingsEvent = new EventEmitter<any>();
  private readonly integrationUtilService = inject(IntegrationUtilService);
  protected readonly integrationTypeEnums = IntegrationType;
  private formBuilder = inject(FormBuilder);

  constructor(
    @Inject(PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$)
    public readonly context: SupportedContexts,
  ) {}

  ngOnInit(): void {
    this.initializeFormGroup();
  }

  initializeFormGroup() {
    this.syncSettingsForm = this.formBuilder.group({});
    const preconnectFormFields = this.preconnectFormFields?.fields;
    if (preconnectFormFields.length > 0) {
      preconnectFormFields.forEach((field) => {
        const control = this.formBuilder.control(field.defaultValue || '', field.required ? Validators.required : []);
        this.syncSettingsForm.addControl(field.id, control);
      });
    }
  }

  protected actionType = ActionType;
  protected fieldType = FieldType;

  handleAction(action: ActionType) {
    if (action === this.actionType.ACTION_TYPE_CANCEL) {
      this.close();
    } else if (action === this.actionType.ACTION_TYPE_SEND_REQUEST) {
      if (this.syncSettingsForm.valid) {
        this.send();
      } else {
        this.syncSettingsForm.markAllAsTouched();
      }
    }
  }

  close() {
    this.dialogRef.close();
  }

  send() {
    this.saveSyncSettingsEvent.emit(this.syncSettingsForm.getRawValue());
    this.dialogRef.close({ send: true });
  }
}
