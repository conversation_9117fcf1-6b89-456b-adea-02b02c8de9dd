@use 'design-tokens' as *;

.card-container {
  overflow: hidden;
}

.glxy-alert {
  margin: $spacing-3 0;
}

.empty {
  flex: 50%;
  margin-top: 10%;
}

.container {
  margin: 16px 24px;
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.integration-card-container {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-flow: row wrap;
}

.browse-tab platform-integration-card {
  width: 323px;
}

.w-100 {
  width: 100%;
}

glxy-alert {
  margin: 0px 10px;
}

.row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  column-gap: $spacing-3;
  justify-content: center;
  align-self: center;
  max-width: 1000px;
  padding-top: $spacing-3;

  @media screen and (min-width: 1700px) {
    grid-template-columns: repeat(4, 1fr);
    max-width: 1300px;
  }

  @media screen and (max-width: 1200px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media screen and (max-width: 650px) {
    grid-template-columns: repeat(1, 1fr);
  }
}

.tab-container {
  align-self: center;
}

.alert-margin {
  margin-bottom: $spacing-3 + 2;
}

.search-bar-container {
  background-color: #f5f5f5;
  height: 80px;
  display: flex;
  align-items: center;
  padding-left: 24px;
  padding-top: 24px;

  @media screen and (max-width: 650px) {
    min-width: 1000px;
  }

  .full-width {
    width: 100%;
  }
}
