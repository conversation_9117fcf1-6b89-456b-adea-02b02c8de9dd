import { App } from '@galaxy/marketplace-apps';
import { Currency as PackageCurrency, Frequency, Package, Pricing } from '@vendasta/marketplace-packages';
import {
  BillingPeriod,
  DurationInterface,
  DurationPeriod,
  LineItem,
  Order,
  Revenue,
  RevenueComponent,
  RevenuePeriod,
} from '@vendasta/sales-orders';
import { UILineItem } from '@vendasta/store';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { AppWithRetailPricing } from '../components/line-items/line-items.interface';
import { defaultRevenue, defaultRevenuePeriodMonthly } from './constants';
import { UILineItemWithEditionPricing } from './interface';
import { RetailSummary } from '../components/retail-summary/retail-summary.component';
import { getRevenueTotalFromLineItems, getTaxAmountForRevenuePeriod } from '../components/edit-order/revenue-utils';
import { FuturePaymentDisplayItemsWithCurrency } from '../components/future-payments-display/future-payments-display.component';
import { ItemRowMapByDateAndDuration } from '@galaxy/inventory-ui';
import { FuturePaymentDisplayItem } from '../components/future-payments-display/future-payments-display.component';

dayjs.extend(utc);

export function packageToUILineItem(pkg: Package, currency: string): UILineItem {
  return {
    lineItem: packageToSalesOrderLineItem(pkg, currency),
    billingId: pkg.packageId,
    name: pkg.name,
    iconUrl: pkg.icon,
    bannerUrl: pkg.headerImageUrl,
    isMultiActivatable: true,
    storeLineItem: packageToSalesOrderLineItem(pkg, currency),
    discountPercentage: 0,
    description: pkg.tagline,
  };
}

export function packageToSalesOrderLineItem(pkg: Package, currency: string): LineItem {
  return new LineItem({
    packageId: pkg.packageId,
    currencyCode: !!pkg.pricing && !!pkg.pricing.currency ? PackageCurrency[pkg.pricing.currency] : currency,
    currentRevenue: new Revenue({ revenueComponents: buildPackageRevenueComponentsList(pkg.pricing) }),
    quantity: 1,
  });
}

// Pricing input is specific to Package type
// The pricing prices list may contain only a single object with just the startingAt property set to true in the case of contact sales and free packages
export function buildPackageRevenueComponentsList(pricing: Pricing): RevenueComponent[] {
  const prices = !!pricing && !!pricing.prices ? pricing.prices : [];
  if (prices.length === 0) {
    return [defaultRevenue];
  }
  return prices.map((p) => {
    return new RevenueComponent({
      value: p.price && p.price > -1 ? p.price : 0,
      period: packageFrequencyToSalesOrderPeriod(p.frequency),
      isStartingRevenue: p.isStartingPrice,
    });
  });
}

export function packageFrequencyToSalesOrderPeriod(f: Frequency): RevenuePeriod {
  switch (f) {
    case Frequency.DAILY:
      return RevenuePeriod.DAILY;
    case Frequency.MONTHLY:
      return RevenuePeriod.MONTHLY;
    case Frequency.ONCE:
      return RevenuePeriod.ONETIME;
    case Frequency.WEEKLY:
      return RevenuePeriod.WEEKLY;
    case Frequency.YEARLY:
      return RevenuePeriod.YEARLY;
    default:
      return defaultRevenuePeriodMonthly;
  }
}

export function convertSelectedAppToLineItem(
  app: App | AppWithRetailPricing,
  defaultDisplayCurrency: string,
): UILineItem {
  const uiLineItem: UILineItemWithEditionPricing = {
    lineItem: appToSalesOrderLineItem(app, defaultDisplayCurrency),
    name: app.sharedMarketingInformation.name,
    editionName: app.sharedMarketingInformation.editionName,
    iconUrl: app.sharedMarketingInformation.iconUrl,
    bannerUrl: app.sharedMarketingInformation.bannerImageUrl,
    storeLineItem: appToSalesOrderLineItem(app, defaultDisplayCurrency),
    discountPercentage: 0,
    description: app.sharedMarketingInformation.tagline,
    appType: app.appType,
    billingId: app.externalIdentifiers?.billingId,
  };

  if (app.price) {
    uiLineItem.usesCustomPricing = app.price.usesCustomPricing;
  }

  if (app.activationInformation) {
    uiLineItem.isMultiActivatable = app.activationInformation.multipleActivationsEnabled;
  }

  if ('retailPricing' in app) {
    // override the default revenue component if retail pricing exists
    if (app.retailPricing.revenue.revenueComponents.length > 0) {
      uiLineItem.lineItem.currentRevenue = app.retailPricing.revenue;
      uiLineItem.lineItem.currencyCode = app.retailPricing.currencyCode;
      uiLineItem.storeLineItem.currentRevenue = app.retailPricing.revenue;
      uiLineItem.storeLineItem.currencyCode = app.retailPricing.currencyCode;
    }
  }

  if (app.parentRequirements?.enabled) {
    uiLineItem.parentIconUrl = app.parentRequirements.parentDetails.iconUrl;
    uiLineItem.parentName = app.parentRequirements.parentDetails.name;
    uiLineItem.parentAppId = app.parentRequirements.parentDetails.key.appId;
  }
  return uiLineItem;
}

export function appToSalesOrderLineItem(app: App, currency: string): LineItem {
  return new LineItem({
    appKey: app.key,
    currencyCode: currency,
    currentRevenue: new Revenue({ revenueComponents: [defaultRevenue] }),
    quantity: 1,
  });
}

export function calculateDiscountPercentage(uiLineItem: UILineItem): number {
  const storeRevenueComponents = uiLineItem.storeLineItem?.currentRevenue?.revenueComponents;
  if (storeRevenueComponents === null) {
    return 0;
  }

  const storeYearlyTotalEstimate = storeRevenueComponents.reduce((total, storeRevenueComponent) => {
    return total + getYearlyTotal(storeRevenueComponent);
  }, 0);
  if (storeYearlyTotalEstimate <= 0) {
    return 0;
  }

  const orderRevenueComponents = uiLineItem.lineItem?.currentRevenue?.revenueComponents;
  if (orderRevenueComponents === null) {
    return 0;
  }
  const orderYearlyTotalEstimate = orderRevenueComponents.reduce((total, orderRevenueComponent) => {
    return total + getYearlyTotal(orderRevenueComponent);
  }, 0);
  if (orderYearlyTotalEstimate <= 0) {
    return 0;
  }

  const discountPercentage = ((storeYearlyTotalEstimate - orderYearlyTotalEstimate) * 100) / storeYearlyTotalEstimate;
  if (discountPercentage > 100 || discountPercentage < 0) {
    return 0;
  }
  if (0 < discountPercentage && discountPercentage < 1) {
    return 1;
  }
  return Math.floor(discountPercentage);
}

function getYearlyTotal(revenueComponent: RevenueComponent): number {
  switch (revenueComponent.period) {
    case RevenuePeriod.YEARLY:
      return revenueComponent.value;
    case RevenuePeriod.MONTHLY:
      return revenueComponent.value * 12;
    case RevenuePeriod.BIWEEKLY:
      return revenueComponent.value * 26;
    case RevenuePeriod.WEEKLY:
      return revenueComponent.value * 52;
    case RevenuePeriod.DAILY:
      return revenueComponent.value * 365;
    default:
      return revenueComponent.value;
  }
}

export function applyContractTermToLineItems(
  lineItems: LineItem[],
  startDate: Date,
  contractDuration: DurationInterface,
): LineItem[] {
  startDate = startDate ?? new Date();
  let endDate = undefined;
  if (contractDuration) {
    let duration: 'months' | 'years' = 'months';
    if (contractDuration.duration === DurationPeriod.YEAR) {
      duration = 'years';
    }
    endDate = dayjs(startDate).add(contractDuration.value, duration).toDate();
  }
  return lineItems.map((lineItem) => {
    const billingFrequency = lineItem.currentRevenue?.revenueComponents[0]?.period ?? RevenuePeriod.ONETIME;
    if (billingFrequency === RevenuePeriod.ONETIME) return lineItem;

    return new LineItem({
      ...lineItem,
      billingPeriod: new BillingPeriod({
        startDate: lineItem.billingPeriod?.startDate,
        endDate: endDate,
      }),
    });
  });
}

export function updateBillingTermEndDateWithNewStartDate(lineItem: LineItem, startDate: Date): LineItem {
  const billingPeriod = lineItem.billingPeriod;
  if (!billingPeriod) return lineItem;

  let newEndDate = undefined;
  if (billingPeriod.endDate) {
    const billingPeriodLength = dayjs(billingPeriod.endDate).diff(billingPeriod.startDate, 'months');
    newEndDate = dayjs(startDate).add(billingPeriodLength, 'months').toDate();
  }

  return new LineItem({
    ...lineItem,
    billingPeriod: new BillingPeriod({
      startDate: lineItem.billingPeriod?.startDate,
      endDate: newEndDate,
    }),
  });
}

// getRetailSummaryFromOrder takes a sales order and returns a RetailSummary to pass to the RetailSummaryComponent
export function getRetailSummaryFromOrder(salesOrder: Order): RetailSummary {
  if (!salesOrder || !salesOrder.lineItems || salesOrder.lineItems.length === 0) {
    return null;
  }
  // Get line items that will be charged immediately
  const dueNowLineItems = getLineItemsDueToDate(salesOrder.lineItems, salesOrder.requestedActivation);

  const hasScheduledLineItems = dueNowLineItems.length < salesOrder.lineItems.length;

  const currencyCode = salesOrder.lineItems[0]?.currencyCode;
  // Get the subtotal from the revenue of all line items that are due now
  const subTotal = getRevenueTotalFromLineItems(dueNowLineItems);
  // Get the total tax from the revenue of all line items that are due now
  const taxAmount = salesOrder?.taxOptions?.reduce((total, taxOption) => total + taxOption.initialAmount, 0) || 0;

  // The actual first payment is the revenue total + any taxes
  const firstPayment = subTotal + taxAmount;

  // Figure out the revenue values per frequency
  const monthlySubTotal = getRevenueTotalFromLineItems(salesOrder.lineItems, RevenuePeriod.MONTHLY);
  const yearlySubTotal = getRevenueTotalFromLineItems(salesOrder.lineItems, RevenuePeriod.YEARLY);

  // Also get the taxes per frequency to determine recurring charges
  const monthlyTax = getTaxAmountForRevenuePeriod(salesOrder, RevenuePeriod.MONTHLY);
  const yearlyTax = getTaxAmountForRevenuePeriod(salesOrder, RevenuePeriod.YEARLY);

  return {
    subtotal: subTotal,
    currencyCode: currencyCode,
    taxAmount: taxAmount,
    firstPayment: firstPayment,
    monthlyTotal: monthlySubTotal + monthlyTax,
    yearlyTotal: yearlySubTotal + yearlyTax,
    hasScheduledLineItems: hasScheduledLineItems,
  } as RetailSummary;
}

// getLineItemsDueNow gets all line items that will be charged immediately
export function getLineItemsDueToDate(lineItems: LineItem[], dueDate: Date): LineItem[] {
  if (!lineItems || !lineItems.length || !dueDate) return [];
  // Truncate to the day because we want to compare the start day in UTC
  const dueDateUtcDay = Date.UTC(dueDate.getUTCFullYear(), dueDate.getUTCMonth(), dueDate.getUTCDate());

  return lineItems.filter((item) => {
    const start = item.billingPeriod?.startDate;
    if (!start) {
      return true; // If start is invalid or not defined, include the item
    }
    const startDate = new Date(start);
    // Truncate to the day because we want to compare with the current day in UTC
    const startUtcDay = Date.UTC(startDate.getUTCFullYear(), startDate.getUTCMonth(), startDate.getUTCDate());

    return startUtcDay <= dueDateUtcDay;
  });
}

export function convertFuturePaymentItemsMapIntoFuturePaymentDisplayItemArray(
  futurePaymentsMap: ItemRowMapByDateAndDuration,
  currencyCode = 'USD',
): FuturePaymentDisplayItemsWithCurrency {
  if (!futurePaymentsMap || !Object.keys(futurePaymentsMap)?.length) return {} as FuturePaymentDisplayItemsWithCurrency;
  const lineItemsArray: FuturePaymentDisplayItem[] = [];

  /*
    Javascript maps sort the numeric keys in ascending order
    the object entries will always be ordered for the main dictionary
    The middle(duration dictionary) is sorted in ascending order, except -1 (renewing indefinitely)
    inner dictionary (period) is sorted as (onetime, monthly, yearly)
   */
  //Start date dictionary
  Object.entries(futurePaymentsMap)
    .sort((a, b) => {
      return Number(a[0]) - Number(b[0]);
    })
    .forEach(([startDate, itemsByDuration]: [string, { [_: number]: { [_ in Frequency]?: number } }]) => {
      //Duration dictionary
      Object.entries(itemsByDuration).forEach(
        ([duration, priceByFrequency]: [string, { [_ in Frequency]?: number }]) => {
          //Period dictionary
          Object.entries(priceByFrequency).forEach(([frequency, price]: [string, number]) => {
            lineItemsArray.push({
              startDate: Number(startDate),
              duration: Number(duration),
              price: price,
              frequency: frequency,
            } as FuturePaymentDisplayItem);
          });
        },
      );
    });

  return { lineItems: lineItemsArray, currencyCode: currencyCode };
}
