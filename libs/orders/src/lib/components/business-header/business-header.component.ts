import { Component, Input } from '@angular/core';
import { CondensedOrderDetailsService, OrderDetails } from '../condensed-order-details/condensed-order-details.service';

import { AsyncPipe } from '@angular/common';

import { Observable } from 'rxjs';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'orders-business-header',
  templateUrl: './business-header.component.html',
  styleUrls: ['./business-header.component.scss'],
  providers: [CondensedOrderDetailsService],
  imports: [TranslateModule, AsyncPipe],
})
export class BusinessHeaderComponent {
  public readonly orderDetails$: Observable<OrderDetails> = this.condensedOrderDetailsService.orderDetails$;

  @Input() orderId: string;

  constructor(private condensedOrderDetailsService: CondensedOrderDetailsService) {}
}
