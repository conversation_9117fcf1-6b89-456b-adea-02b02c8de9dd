@use 'design-tokens' as *;

.card {
  display: flex;
  align-items: center;
}

.stencil-shimmer {
  height: $spacing-4;
  width: 45%;
}

.edit-button {
  margin-left: $spacing-2;
}

.edit-spacer {
  margin-top: $spacing-2;
}

.spacer {
  margin-top: $spacing-3;
}

.no-card-message {
  color: $secondary-text-color;
  margin-bottom: $spacing-1;
}

.empty-card-layout {
  display: flex;
  flex-direction: column;
  margin-top: 12px;
}

.empty-card-button {
  align-self: flex-end;
}

.method-list {
  display: flex;
  flex-flow: column;
  gap: $spacing-2;
}

.default-badge {
  margin: $spacing-2;
}

.method-row {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
}

.remove-button {
  font-size: $font-preset-3-size;
  align-content: center;
}

.use-default-button {
  margin-left: $spacing-2;
}

.section-header {
  margin: $spacing-3 0;
  font-weight: 500;
}

.remove-top-margin {
  margin-top: 0;
}

.charge-payment-method {
  margin-right: 0px;
  margin-left: auto;
}
