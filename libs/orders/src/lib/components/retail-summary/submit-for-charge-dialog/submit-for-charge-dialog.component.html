<glxy-confirmation-body>
  <mat-icon glxyConfirmationIcon class="icon" inline="true" color="primary">info</mat-icon>

  <glxy-confirmation-title
    >{{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.SUBMIT_FOR_CHARGE_DIALOG.TITLE' | translate }}
  </glxy-confirmation-title>
  <glxy-confirmation-custom-content>
    <div [innerHTML]="'LIB_ORDERS.COMMON.EDIT_ORDERS.SUBMIT_FOR_CHARGE_DIALOG.MESSAGE' | translate"></div>
    <div class="checkboxes">
      <mat-checkbox [(ngModel)]="confirmRetailCharge"
        >{{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.SUBMIT_FOR_CHARGE_DIALOG.CONFIRM_CHECKBOX' | translate }}
      </mat-checkbox>
      <mat-checkbox [(ngModel)]="confirmWholesaleCharge"
        >{{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.SUBMIT_FOR_CHARGE_DIALOG.CONFIRM_PARTNER_BILLED_CHECKBOX' | translate }}
      </mat-checkbox>
    </div>
  </glxy-confirmation-custom-content>
</glxy-confirmation-body>

<glxy-confirmation-actions>
  <glxy-confirmation-primary-actions>
    <button mat-stroked-button matDialogClose>
      {{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.SUBMIT_FOR_CHARGE_DIALOG.CANCEL' | translate }}
    </button>

    <button
      [trackEvent]="{
        eventName: 'charge-and-activate',
        category: 'unified-orders',
        action: 'click',
        properties: { orderStatus: (order$ | async)?.status },
      }"
      mat-flat-button
      color="primary"
      [disabled]="!confirmRetailCharge || !confirmWholesaleCharge"
      (click)="chargeAndSubmit()"
    >
      {{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.SUBMIT_FOR_CHARGE_DIALOG.CHARGE_AND_ACTIVATE' | translate }}
    </button>
  </glxy-confirmation-primary-actions>
</glxy-confirmation-actions>
