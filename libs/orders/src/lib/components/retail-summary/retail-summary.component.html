<mat-expansion-panel [expanded]="true">
  <mat-expansion-panel-header>
    <mat-panel-title> {{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.RETAIL_SUMMARY.TITLE' | translate }}</mat-panel-title>
  </mat-expansion-panel-header>
  @if (summary) {
    <div class="pricing-row">
      <span>{{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.RETAIL_SUMMARY.SUBTOTAL' | translate }}</span>
      <span class="pricing">
        <billing-ui-simple-price-display
          [price]="summary.subtotal"
          [currencyCode]="summary.currencyCode"
          [alwaysShowNumber]="true"
        ></billing-ui-simple-price-display>
      </span>
    </div>
    @if (summary.taxAmount) {
      <div class="pricing-row">
        <span>{{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.RETAIL_SUMMARY.TAXES' | translate }}</span>
        <span class="pricing">
          <billing-ui-simple-price-display
            [price]="summary.taxAmount"
            [currencyCode]="summary.currencyCode"
          ></billing-ui-simple-price-display>
        </span>
      </div>
    }
    <mat-divider></mat-divider>
    <div class="pricing-row due-now">
      <span>{{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.RETAIL_SUMMARY.DUE_NOW' | translate }}</span>
      <span class="pricing">
        <billing-ui-simple-price-display
          [price]="summary.firstPayment"
          [currencyCode]="summary.currencyCode"
          [alwaysShowNumber]="true"
        ></billing-ui-simple-price-display>
      </span>
    </div>
    @if (!summary.hasScheduledLineItems) {
      <div class="pricing-row">
        <span>{{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.RETAIL_SUMMARY.RECURRING_CHARGES' | translate }}</span>
        <span class="pricing">
          <div>
            @if (summary.monthlyTotal) {
              <billing-ui-simple-price-display
                [price]="summary.monthlyTotal"
                [currencyCode]="summary.currencyCode"
                [frequency]="Frequency.MONTHLY"
                [alwaysShowNumber]="true"
              ></billing-ui-simple-price-display>
            }
            @if (summary.yearlyTotal) {
              <billing-ui-simple-price-display
                [price]="summary.yearlyTotal"
                [currencyCode]="summary.currencyCode"
                [frequency]="Frequency.YEARLY"
                [alwaysShowNumber]="true"
              ></billing-ui-simple-price-display>
            }
            @if (!summary.monthlyTotal && !summary.yearlyTotal) {
              <billing-ui-simple-price-display
                [price]="summary.yearlyTotal"
                [currencyCode]="summary.currencyCode"
                [frequency]="Frequency.ONE_TIME"
                [alwaysShowNumber]="true"
              ></billing-ui-simple-price-display>
            }
          </div>
        </span>
      </div>
    } @else {
      @if (futurePaymentLineItems$ | async; as futurePaymentLineItems) {
        <orders-future-payments-display
          [futurePayments]="futurePaymentLineItems.lineItems"
          [currencyCode]="futurePaymentLineItems.currencyCode"
        />
      }
    }
    @if (canCollectPaymentFromCustomer$ | async) {
      @if ((loadingPaymentMethods$$ | async) === false) {
        <mat-divider class="divider-bottom-margin"></mat-divider>
        <orders-payment-method-selector
          class="payment-method-selector"
          [stripeConnectId]="stripeConnectId$ | async"
          [merchantId]="partnerId"
          [customerId]="accountGroupId"
          [orderId]="orderId"
          [stripeKey]="stripeKey"
          [editButton]="'LIB_ORDERS.COMMON.EDIT_ORDERS.RETAIL_SUMMARY.EDIT' | translate"
          [addCardButton]="'LIB_ORDERS.COMMON.EDIT_ORDERS.RETAIL_SUMMARY.ADD_CUSTOMER_PAYMENT_METHOD' | translate"
          [isEditable]="canEditPaymentMethod"
          [submitDialogText]="'LIB_ORDERS.COMMON.EDIT_ORDERS.RETAIL_SUMMARY.SUBMIT_TEXT' | translate"
          [emptyCardHintText]="'LIB_ORDERS.COMMON.EDIT_ORDERS.RETAIL_SUMMARY.EMPTY_TEXT' | translate"
          [paymentMethods]="paymentMethods$$ | async"
          [defaultPaymentMethod]="defaultPaymentMethod$$ | async"
          [orderCustomerRecipient]="orderCustomerRecipient"
          [canChargeOrder]="canChargeOrder"
          (selectedPaymentMethod)="onPaymentMethodSelected($event)"
          (orderCharged)="onOrderCharged($event)"
        ></orders-payment-method-selector>
      } @else {
        <div matLine>
          <div class="stencil-shimmer"></div>
        </div>
      }
    }
  }
</mat-expansion-panel>
