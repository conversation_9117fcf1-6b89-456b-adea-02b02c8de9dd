import { Component } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import { AsyncPipe, DatePipe } from '@angular/common';
import { CondensedOrderDetailsService, OrderDetails } from './condensed-order-details.service';
import { CompanyNameComponent } from '../order-details/company-details/company-name.component';
import { CompanyAddressComponent } from '../order-details/company-details/company-address.component';
import { DurationInterface } from '@vendasta/sales-orders';
import { FormatDurationPipe } from './format-duration.pipe';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { OrderContractStartComponent } from '../order-details/contract-details/contract-start.component';
import { OrderContractDurationComponent } from '../order-details/contract-details/contract-duration.component';
import { MatDivider } from '@angular/material/divider';
import { MatIcon } from '@angular/material/icon';

@Component({
  selector: 'orders-condensed-order-details',
  templateUrl: './condensed-order-details.component.html',
  styleUrls: ['./condensed-order-details.component.scss'],
  providers: [CondensedOrderDetailsService],
  imports: [
    TranslateModule,
    AsyncPipe,
    CompanyNameComponent,
    CompanyAddressComponent,
    DatePipe,
    FormatDurationPipe,
    OrderContractStartComponent,
    GalaxyBadgeModule,
    OrderContractDurationComponent,
    MatDivider,
    MatIcon,
  ],
})
export class CondensedOrderDetailsComponent {
  public readonly orderDetails$: Observable<OrderDetails> = this.condensedOrderDetailsService.orderDetails$;

  constructor(private condensedOrderDetailsService: CondensedOrderDetailsService) {}

  handleContractStartDateChange(newContractStartDate: Date): void {
    this.condensedOrderDetailsService.updateContractStartDate(newContractStartDate);
  }

  handleContractDurationChange(newDuration: DurationInterface): void {
    this.condensedOrderDetailsService.updateContractDuration(newDuration);
  }
}
