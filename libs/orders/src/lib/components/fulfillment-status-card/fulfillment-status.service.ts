import { Inject, Injectable } from '@angular/core';
import { GetMultiAppRequest } from '@galaxy/marketplace-apps';
import { catchError, map, take, tap } from 'rxjs/operators';
import { combineLatest, Observable, of } from 'rxjs';
import { App, AppKey, AppType, PartnerApiService } from '@vendasta/marketplace-apps';
import { ActivationStatus, ProductActivation, SalesOrdersService, Status } from '@vendasta/sales-orders';
import { ActivationFailureDetails } from '@vendasta/order-fulfillment/lib/_internal/objects';
import { FailureReason, FulfillmentOrder, OrderFulfillmentService } from '@vendasta/order-fulfillment';
import { OrderStoreService } from '../../core/orders.service';
import * as e from '@vendasta/order-fulfillment/lib/_internal/enums';
import { PAGE_ORDER_CONFIG_TOKEN, PageOrderConfig } from '../../core/tokens';

export interface FulfillmentRowData {
  name: string;
  iconUrl: string;
  type: AppType;
  parentName: string;
  activationStatus: ActivationStatus;
  failureDetails: string; // Only set if activation status is ERRORED
  menuItems?: MenuItemInterface[];
  fulfillmentStatus?: e.FulfillmentOrderStatus;
}

export enum MenuItemEventType {
  IGNORE_ACTIVATION_ERRORS = 'ignore-errors',
}

export interface MenuItemClickedInterface {
  value: string;
  eventType: MenuItemEventType;
}

export interface MenuItemInterface {
  label: string;
  icon: string;
  value: string;
  eventType: MenuItemEventType;
}

@Injectable()
export class FulfillmentStatusService {
  private validationErrorMsgs = new Map<string, string>([
    ['1', 'App Not Found'],
    ['2', 'No Order Form SubmissionID'],
    ['3', 'Order Form Not Found'],
    ['4', 'Order Form Already Used'],
    ['5', 'Order Form Not Supported'],
    ['6', 'Invalid Entry URL'],
    ['7', 'Entry URL Not Provided'],
    ['8', 'Invalid Dependency'],
    ['9', 'Already Active'],
    ['10', 'Edition Not Provided'],
    ['11', 'Editions Not Supported'],
    ['12', 'Invalid Edition'],
    ['13', 'Trial Not Supported'],
    ['14', 'Account Group Not Found'],
    ['15', 'No Country Information'],
    ['16', 'Unsupported Country'],
    ['17', 'Can Not Change Edition of Trial'],
    ['18', 'App Not Found'],
    ['20', 'No longer available for activation'],
  ]);

  constructor(
    private partnerApiService: PartnerApiService,
    private orderFulfillmentService: OrderFulfillmentService,
    private salesOrdersService: SalesOrdersService,
    private readonly orderStoreService: OrderStoreService,
    @Inject(PAGE_ORDER_CONFIG_TOKEN) private readonly pageOrderConfig: PageOrderConfig,
  ) {}

  getRows(
    productActivations: ProductActivation[],
    workOrders: FulfillmentOrder[],
    partnerId: string,
    marketId: string,
  ): Observable<FulfillmentRowData[]> {
    const appKeys = productActivations.map(
      (act) => new AppKey({ appId: act.productId, editionId: act.editionId || '' }),
    );

    const workOrdersMap = this.convertWorkOrdersArrayToMap(workOrders);
    const productActivationErrorsMap$ = this.getProductActivationErrorsMap(productActivations);

    const marketplaceApps$ = this.partnerApiService
      .getMultiApp(
        new GetMultiAppRequest({
          partnerId: partnerId,
          marketId: marketId,
          appKeys: appKeys,
        }),
      )
      .pipe(
        take(1),
        map((response) => response?.apps || ([] as App[])),
        catchError(() => of([] as App[])),
      );

    return combineLatest([
      productActivationErrorsMap$,
      marketplaceApps$,
      this.orderStoreService.order$,
      this.pageOrderConfig.orderPermissions$,
    ]).pipe(
      take(1),
      map(([errorsMap, apps, order, orderPermissions]) =>
        this.constructRows(
          productActivations,
          errorsMap,
          apps,
          workOrdersMap,
          order.status,
          orderPermissions.accessManageOrders,
        ),
      ),
    );
  }

  ignoreErrors(orderId: string, businessId: string, productActivationUniqueId: string): Observable<boolean> {
    return this.salesOrdersService
      .ignoreProductActivationError(orderId, businessId, productActivationUniqueId)
      .pipe(tap(() => this.orderStoreService.reloadOrder()));
  }

  private constructRows(
    productActivations: ProductActivation[],
    errorsMap: Map<string, ActivationFailureDetails>,
    apps: App[],
    workOrdersMap: Map<string, FulfillmentOrder>,
    orderStatus: Status,
    canManageOrders: boolean,
  ): FulfillmentRowData[] {
    return productActivations.map((act) => {
      const item = apps.find((app) => app?.key?.appId === act.productId);
      let failureDetails = '';
      let menuItems = [];
      let activationStatus = act.activationStatus;
      if (act.activationStatus === ActivationStatus.ERRORED && errorsMap.has(act.uniqueId)) {
        failureDetails = this.translateFailureDetails(errorsMap.get(act.uniqueId));
        if (canManageOrders) {
          menuItems = [
            {
              label: 'LIB_ORDERS.FULFILLMENT_STATUS.IGNORE_ERRORS_OPTION_LABEL',
              icon: '',
              value: act.uniqueId,
              eventType: MenuItemEventType.IGNORE_ACTIVATION_ERRORS,
            },
          ];
        }
      }
      if (!activationStatus && (orderStatus === Status.CANCELLED || orderStatus === Status.ARCHIVED)) {
        activationStatus = ActivationStatus.CANCELED;
      }
      return {
        name: item?.sharedMarketingInformation?.name || act.productId,
        iconUrl: item?.sharedMarketingInformation?.iconUrl || '',
        type: item?.appType,
        parentName: item?.parentRequirements?.parentDetails?.name || '',
        activationStatus: activationStatus,
        failureDetails: failureDetails,
        menuItems: menuItems,
        fulfillmentStatus: workOrdersMap.get(act.productId)?.status || null,
      } as FulfillmentRowData;
    });
  }

  private getProductActivationErrorsMap(
    productActivations: ProductActivation[],
  ): Observable<Map<string, ActivationFailureDetails>> {
    const orderItemIDs = productActivations
      .filter((act) => act.activationStatus === ActivationStatus.ERRORED)
      .map((act) => act.uniqueId);

    if (!orderItemIDs.length) {
      return of(new Map<string, ActivationFailureDetails>());
    }

    return this.orderFulfillmentService.getMultiActivationErrorsForOrderItems(orderItemIDs).pipe(
      take(1),
      catchError(() => of([] as ActivationFailureDetails[])),
      map((errorDetails) => this.createErrorDetailsMap(errorDetails)),
    );
  }

  private createErrorDetailsMap(errorDetails: ActivationFailureDetails[]): Map<string, ActivationFailureDetails> {
    return errorDetails.reduce((map, curr) => {
      map.set(curr.orderItemId, curr);
      return map;
    }, new Map<string, ActivationFailureDetails>());
  }

  private translateFailureDetails(activationFailureDetails: ActivationFailureDetails): string {
    switch (activationFailureDetails.failureReason) {
      case FailureReason.FAILURE_REASON_VALIDATION_ERROR:
        return this.translateValidationError(activationFailureDetails.details);
      case FailureReason.FAILURE_REASON_CHARGE_ERROR:
        return activationFailureDetails.details
          ? this.formattedChargeError(activationFailureDetails.details[0])
          : 'Payment could not be processed for this product';
      case FailureReason.FAILURE_REASON_VENDOR_REJECTED_ERROR:
        return activationFailureDetails.details ? activationFailureDetails.details[0] : 'Vendor rejected the order';
      case FailureReason.FAILURE_REASON_ACTIVATION_ERROR:
        return activationFailureDetails.details
          ? activationFailureDetails.details[0]
          : 'Product failed to activate. Contact Support for assistance.';
    }
    return activationFailureDetails.details ? activationFailureDetails.details[0] : '';
  }

  private translateValidationError(details: string[]): string {
    let failureMsg = '';
    details.forEach((detail) => {
      const validationMsg = this.validationErrorMsgs.has(detail) ? this.validationErrorMsgs.get(detail) : '';
      if (failureMsg === '') {
        failureMsg += validationMsg;
      } else {
        failureMsg += ' / ' + validationMsg;
      }
    });
    return failureMsg;
  }

  private formattedChargeError(details: string): string {
    const toCapitalize = details.split('_');
    toCapitalize.forEach((line) => {
      line.toUpperCase();
    });
    return toCapitalize.join(' ');
  }

  // Returns map of appID to work order.
  // There is a one to one mapping between appID and work order, unless the app is multi-activatable.
  // Multi-activatable apps in the same sales order share a single work order.
  private convertWorkOrdersArrayToMap(workOrders: FulfillmentOrder[]): Map<string, FulfillmentOrder> {
    return workOrders.reduce((map, workOrder) => {
      map.set(workOrder.appId, workOrder);
      return map;
    }, new Map<string, FulfillmentOrder>());
  }
}
