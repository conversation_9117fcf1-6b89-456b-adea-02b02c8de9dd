import { FulfillmentRowData, FulfillmentStatusService, MenuItemEventType } from './fulfillment-status.service';
import { App, <PERSON><PERSON><PERSON><PERSON>, PartnerApiService } from '@vendasta/marketplace-apps';
import {
  FailureReason,
  FulfillmentOrder,
  FulfillmentOrderStatus,
  OrderFulfillmentService,
} from '@vendasta/order-fulfillment';
import { ActivationStatus, Order, ProductActivation, SalesOrdersService, Status } from '@vendasta/sales-orders';
import { ActivationFailureDetails } from '@vendasta/order-fulfillment/lib/_internal/objects';
import { OrderStoreService } from '../../core/orders.service';
import { of } from 'rxjs';
import { PageOrderConfig } from '../../core/tokens';

describe('FulfillmentStatusService', () => {
  let service: FulfillmentStatusService;

  const partnerApiServiceMock = {
    getMultiApp: jest.fn(),
  } as jest.Mocked<PartnerApiService>;

  const orderFulfillmentServiceMock = {
    getMultiActivationErrorsForOrderItems: jest.fn(),
  } as jest.Mocked<OrderFulfillmentService>;

  const salesOrdersServiceMock = {
    ignoreProductActivationError: jest.fn(),
  } as jest.Mocked<SalesOrdersService>;

  const pageOrderConfigMock = {
    orderPermissions$: of({
      accessManageOrders: true,
      accessMarketplace: true,
    }),
  } as jest.Mocked<PageOrderConfig>;

  let orderStoreServiceMock;

  const setup = (overrides?: any) => {
    orderStoreServiceMock = {
      reloadOrder: jest.fn(),
      ...overrides,
    } as unknown as jest.Mocked<OrderStoreService>;

    service = new FulfillmentStatusService(
      partnerApiServiceMock,
      orderFulfillmentServiceMock,
      salesOrdersServiceMock,
      orderStoreServiceMock,
      pageOrderConfigMock,
    );
  };

  const activationFailureDetailsMock: ActivationFailureDetails = {
    orderItemId: 'OI-1',
    failureReason: FailureReason.FAILURE_REASON_VALIDATION_ERROR,
    details: ['1', '2'],
  } as ActivationFailureDetails;

  const productActivationsMock: ProductActivation[] = [
    {
      uniqueId: 'OI-1',
      productId: 'A-1',
      activationStatus: ActivationStatus.ERRORED,
    },
    {
      uniqueId: 'OI-2',
      productId: 'MP-2',
      activationStatus: ActivationStatus.ACTIVATED,
      editionId: 'EDITION-2',
    },
  ] as ProductActivation[];
  const workOrdersMock: FulfillmentOrder[] = [
    {
      fulfillmentOrderId: '1',
      appId: 'A-1',
      status: FulfillmentOrderStatus.FULFILLMENT_ORDER_STATUS_IN_PROGRESS,
    },
    {
      fulfillmentOrderId: '2',
      appId: 'MP-2',
      status: FulfillmentOrderStatus.FULFILLMENT_ORDER_STATUS_COMPLETED,
    },
  ] as FulfillmentOrder[];

  const appsMock: App[] = [
    {
      key: new AppKey({ appId: 'A-1' }),
      sharedMarketingInformation: {
        name: 'Add-on 1',
        iconUrl: 'https://example.com/icon1.png',
      },
      appType: 2,
      parentRequirements: {
        parentDetails: {
          name: 'Parent Product',
        },
      },
    },
    {
      key: new AppKey({ appId: 'MP-2', editionId: 'EDITION-2' }),
      sharedMarketingInformation: {
        name: 'Product 2',
        iconUrl: 'https://example.com/icon2.png',
      },
      appType: 1,
      parentRequirements: {},
    },
  ] as App[];

  // Public functions ---------------------------------------------------------

  describe('getRows', () => {
    it('should return FulfillmentRowData[]', (done) => {
      setup({
        order$: of({
          status: Status.APPROVED,
        } as Order),
      });
      orderFulfillmentServiceMock.getMultiActivationErrorsForOrderItems = jest.fn(() =>
        of([activationFailureDetailsMock]),
      );
      partnerApiServiceMock.getMultiApp = jest.fn(() => of({ apps: appsMock }));

      const productActivations = productActivationsMock;
      const partnerId = 'partner-123';
      const marketId = 'market-456';

      service.getRows(productActivations, workOrdersMock, partnerId, marketId).subscribe((result) => {
        const expected: FulfillmentRowData[] = [
          {
            name: 'Add-on 1',
            iconUrl: 'https://example.com/icon1.png',
            type: 2,
            parentName: 'Parent Product',
            activationStatus: ActivationStatus.ERRORED,
            failureDetails: 'App Not Found / No Order Form SubmissionID',
            fulfillmentStatus: FulfillmentOrderStatus.FULFILLMENT_ORDER_STATUS_IN_PROGRESS,
            menuItems: [
              {
                label: 'LIB_ORDERS.FULFILLMENT_STATUS.IGNORE_ERRORS_OPTION_LABEL',
                icon: '',
                value: 'OI-1',
                eventType: MenuItemEventType.IGNORE_ACTIVATION_ERRORS,
              },
            ],
          },
          {
            name: 'Product 2',
            iconUrl: 'https://example.com/icon2.png',
            type: 1,
            fulfillmentStatus: FulfillmentOrderStatus.FULFILLMENT_ORDER_STATUS_COMPLETED,
            parentName: '',
            activationStatus: ActivationStatus.ACTIVATED,
            failureDetails: '',
            menuItems: [],
          },
        ] as FulfillmentRowData[];

        expect(result).toEqual(expected);
        done();
      });
    });
    it('should set activation status to cancelled if order is archived and has no activation status', (done) => {
      setup({
        order$: of({
          status: Status.ARCHIVED,
        } as Order),
      });
      orderFulfillmentServiceMock.getMultiActivationErrorsForOrderItems = jest.fn(() =>
        of([activationFailureDetailsMock]),
      );
      partnerApiServiceMock.getMultiApp = jest.fn(() => of({ apps: appsMock }));

      const productActivations = [
        {
          uniqueId: 'OI-2',
          productId: 'MP-2',
          editionId: 'EDITION-2',
        },
      ];
      const partnerId = 'partner-123';
      const marketId = 'market-456';

      service.getRows(productActivations, workOrdersMock, partnerId, marketId).subscribe((result) => {
        const expected: FulfillmentRowData[] = [
          {
            name: 'Product 2',
            iconUrl: 'https://example.com/icon2.png',
            type: 1,
            fulfillmentStatus: FulfillmentOrderStatus.FULFILLMENT_ORDER_STATUS_COMPLETED,
            parentName: '',
            activationStatus: ActivationStatus.CANCELED,
            failureDetails: '',
            menuItems: [],
          },
        ] as FulfillmentRowData[];

        expect(result).toEqual(expected);
        done();
      });
    });
    it('should set not change activation status of archived order if it exists', (done) => {
      setup({
        order$: of({
          status: Status.ARCHIVED,
        } as Order),
      });
      orderFulfillmentServiceMock.getMultiActivationErrorsForOrderItems = jest.fn(() =>
        of([activationFailureDetailsMock]),
      );
      partnerApiServiceMock.getMultiApp = jest.fn(() => of({ apps: appsMock }));

      const productActivations = [
        {
          uniqueId: 'OI-2',
          productId: 'MP-2',
          editionId: 'EDITION-2',
          activationStatus: ActivationStatus.SCHEDULED,
        } as ProductActivation,
      ];
      const partnerId = 'partner-123';
      const marketId = 'market-456';

      service.getRows(productActivations, workOrdersMock, partnerId, marketId).subscribe((result) => {
        const expected: FulfillmentRowData[] = [
          {
            name: 'Product 2',
            iconUrl: 'https://example.com/icon2.png',
            type: 1,
            fulfillmentStatus: FulfillmentOrderStatus.FULFILLMENT_ORDER_STATUS_COMPLETED,
            parentName: '',
            activationStatus: ActivationStatus.SCHEDULED,
            failureDetails: '',
            menuItems: [],
          },
        ] as FulfillmentRowData[];

        expect(result).toEqual(expected);
        done();
      });
    });
  });

  describe('ignoreErrors', () => {
    it('should call ignoreProductActivationError and reloadOrder', (done) => {
      setup();
      salesOrdersServiceMock.ignoreProductActivationError = jest.fn(() => of(true));
      service.reloadOrder = jest.fn(() => of(null));

      const orderId = 'O-123';
      const businessId = 'AG-456';
      const uniqueId = 'OI-1';

      service.ignoreErrors(orderId, businessId, uniqueId).subscribe((result) => {
        expect(result).toBe(true);
        expect(salesOrdersServiceMock.ignoreProductActivationError).toHaveBeenCalledWith(orderId, businessId, uniqueId);
        expect(orderStoreServiceMock.reloadOrder).toHaveBeenCalled();
        done();
      });
    });
  });

  // Private functions ---------------------------------------------------------

  describe('constructRows', () => {
    it('should convert product activations to FulfillmentRowData', () => {
      setup();
      const errorsMap = new Map<string, ActivationFailureDetails>();
      errorsMap.set('OI-1', activationFailureDetailsMock);

      const workOrderMap = (service as any).convertWorkOrdersArrayToMap(workOrdersMock);
      const result = (service as any).constructRows(
        productActivationsMock,
        errorsMap,
        appsMock,
        workOrderMap,
        null,
        true,
      );

      const expected: FulfillmentRowData[] = [
        {
          name: 'Add-on 1',
          iconUrl: 'https://example.com/icon1.png',
          type: 2,
          fulfillmentStatus: FulfillmentOrderStatus.FULFILLMENT_ORDER_STATUS_IN_PROGRESS,
          parentName: 'Parent Product',
          activationStatus: ActivationStatus.ERRORED,
          failureDetails: 'App Not Found / No Order Form SubmissionID',
          menuItems: [
            {
              label: 'LIB_ORDERS.FULFILLMENT_STATUS.IGNORE_ERRORS_OPTION_LABEL',
              icon: '',
              value: 'OI-1',
              eventType: MenuItemEventType.IGNORE_ACTIVATION_ERRORS,
            },
          ],
        },
        {
          name: 'Product 2',
          iconUrl: 'https://example.com/icon2.png',
          type: 1,
          fulfillmentStatus: FulfillmentOrderStatus.FULFILLMENT_ORDER_STATUS_COMPLETED,
          parentName: '',
          activationStatus: ActivationStatus.ACTIVATED,
          failureDetails: '',
          menuItems: [],
        },
      ] as FulfillmentRowData[];

      expect(result).toEqual(expected);
    });

    it('should not include ignore errors menu item if cannot manage orders', () => {
      setup();
      const errorsMap = new Map<string, ActivationFailureDetails>();
      errorsMap.set('OI-1', activationFailureDetailsMock);

      const workOrderMap = (service as any).convertWorkOrdersArrayToMap(workOrdersMock);
      const result = (service as any).constructRows(
        productActivationsMock,
        errorsMap,
        appsMock,
        workOrderMap,
        null,
        false,
      );

      const expected: FulfillmentRowData[] = [
        {
          name: 'Add-on 1',
          iconUrl: 'https://example.com/icon1.png',
          type: 2,
          fulfillmentStatus: FulfillmentOrderStatus.FULFILLMENT_ORDER_STATUS_IN_PROGRESS,
          parentName: 'Parent Product',
          activationStatus: ActivationStatus.ERRORED,
          failureDetails: 'App Not Found / No Order Form SubmissionID',
          menuItems: [],
        },
        {
          name: 'Product 2',
          iconUrl: 'https://example.com/icon2.png',
          type: 1,
          fulfillmentStatus: FulfillmentOrderStatus.FULFILLMENT_ORDER_STATUS_COMPLETED,
          parentName: '',
          activationStatus: ActivationStatus.ACTIVATED,
          failureDetails: '',
          menuItems: [],
        },
      ] as FulfillmentRowData[];

      expect(result).toEqual(expected);
    });

    describe('translateFailureDetails', () => {
      it('should return correct message for VALIDATION_ERROR', () => {
        setup();
        const validationFailureDetails: ActivationFailureDetails = {
          orderItemId: 'OI-1',
          failureReason: FailureReason.FAILURE_REASON_VALIDATION_ERROR,
          details: ['16'],
        } as ActivationFailureDetails;
        const result = (service as any).translateFailureDetails(validationFailureDetails);
        expect(result).toBe('Unsupported Country');
      });

      it('should return correct message for VALIDATION_ERROR when details array has multiple strings', () => {
        setup();
        const result = (service as any).translateFailureDetails(activationFailureDetailsMock);
        expect(result).toBe('App Not Found / No Order Form SubmissionID');
      });

      it('should return formatted details message for CHARGE_ERROR', () => {
        setup();
        const chargeErrorDetails: ActivationFailureDetails = {
          orderItemId: 'OI-3',
          failureReason: FailureReason.FAILURE_REASON_CHARGE_ERROR,
          details: ['PAYMENT_DECLINED'],
        } as ActivationFailureDetails;
        const result = (service as any).translateFailureDetails(chargeErrorDetails);
        expect(result).toBe('PAYMENT DECLINED');
      });

      it('should return default message for CHARGE_ERROR when details are missing', () => {
        setup();
        const missingDetailsError: ActivationFailureDetails = {
          orderItemId: 'OI-4',
          failureReason: FailureReason.FAILURE_REASON_CHARGE_ERROR,
          details: null,
        } as ActivationFailureDetails;
        const result = (service as any).translateFailureDetails(missingDetailsError);
        expect(result).toBe('Payment could not be processed for this product');
      });

      it('should return first string in details array for VENDOR_REJECTED_ERROR', () => {
        setup();
        const vendorErrorDetails: ActivationFailureDetails = {
          orderItemId: 'OI-4',
          failureReason: FailureReason.FAILURE_REASON_VENDOR_REJECTED_ERROR,
          details: ['Vendor error'],
        } as ActivationFailureDetails;
        const result = (service as any).translateFailureDetails(vendorErrorDetails);
        expect(result).toBe('Vendor error');
      });

      it('should return default message for VENDOR_REJECTED_ERROR when details are missing', () => {
        setup();
        const missingDetailsError: ActivationFailureDetails = {
          orderItemId: 'OI-4',
          failureReason: FailureReason.FAILURE_REASON_VENDOR_REJECTED_ERROR,
          details: null,
        } as ActivationFailureDetails;
        const result = (service as any).translateFailureDetails(missingDetailsError);
        expect(result).toBe('Vendor rejected the order');
      });

      it('should return first string in details array for ACTIVATION_ERROR', () => {
        setup();
        const activationErrorDetails: ActivationFailureDetails = {
          orderItemId: 'OI-4',
          failureReason: FailureReason.FAILURE_REASON_ACTIVATION_ERROR,
          details: ['Failed to activate product'],
        } as ActivationFailureDetails;
        const result = (service as any).translateFailureDetails(activationErrorDetails);
        expect(result).toBe('Failed to activate product');
      });

      it('should return default message for ACTIVATION_ERROR when details are missing', () => {
        setup();
        const missingDetailsError: ActivationFailureDetails = {
          orderItemId: 'OI-4',
          failureReason: FailureReason.FAILURE_REASON_ACTIVATION_ERROR,
          details: null,
        } as ActivationFailureDetails;
        const result = (service as any).translateFailureDetails(missingDetailsError);
        expect(result).toBe('Product failed to activate. Contact Support for assistance.');
      });
    });

    describe('translateValidationError', () => {
      it('translates valid and ignores invalid error codes', () => {
        setup();
        const details = ['1', '2', '999'];
        const result = (service as any).translateValidationError(details);
        expect(result).toBe('App Not Found / No Order Form SubmissionID / ');
      });
    });

    describe('formattedChargeError', () => {
      it('should format charge error messages correctly', () => {
        setup();
        const details = 'PAYMENT_DECLINED';
        const result = (service as any).formattedChargeError(details);
        expect(result).toBe('PAYMENT DECLINED');
      });
    });
  });

  describe('convertWorkOrdersArrayToMap', () => {
    it('should convert array to map', () => {
      setup();
      const workOrderMap = (service as any).convertWorkOrdersArrayToMap(workOrdersMock);
      const expected = new Map<string, FulfillmentOrder>();
      expected.set('A-1', workOrdersMock[0]);
      expected.set('MP-2', workOrdersMock[1]);
      expect(workOrderMap).toEqual(expected);
    });
    it('should convert empty array to empty map', () => {
      setup();
      const workOrderMap = (service as any).convertWorkOrdersArrayToMap([]);
      const expected = new Map<string, FulfillmentOrder>();
      expect(workOrderMap).toEqual(expected);
    });
  });
});
