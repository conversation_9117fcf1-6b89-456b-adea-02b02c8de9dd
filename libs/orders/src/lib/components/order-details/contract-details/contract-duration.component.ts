import { Component, EventEmitter, Input, Output } from '@angular/core';
import { DurationInterface, DurationPeriod } from '@vendasta/sales-orders';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'orders-contract-duration',
  styleUrls: ['../order-details.component.scss', './contract-duration.component.scss'],
  template: `
    <span
      class="column-title"
      matTooltip="{{ 'SALES_CONTRACT_DURATION.HINT' | translate }}"
      matTooltipPosition="above"
      [matTooltipDisabled]="hideTooltip"
    >
      {{ 'LIB_ORDERS.COMMON.ORDER_DETAILS.CONTRACT_DURATION' | translate }}
    </span>
    <span class="column-content contract-duration">
      <glxy-form-field class="duration-value" [showLabel]="false" [bottomSpacing]="'none'">
        <input
          matInput
          type="number"
          class="duration-value"
          min="1"
          [ngModel]="contractDuration?.value"
          (ngModelChange)="handleContractDurationValueChange($event)"
          [disabled]="viewOnly"
        />
        @if (contractDuration?.value < 1) {
          <mat-error>{{ 'LIB_ORDERS.COMMON.ORDERS.ERROR_LESS_THAN_ONE' | translate }}</mat-error>
        }
      </glxy-form-field>
      <glxy-form-field class="duration-period" [showLabel]="false" [bottomSpacing]="'none'">
        <mat-select
          [ngModel]="contractDuration?.duration"
          (ngModelChange)="handleContractDurationPeriodChange($event)"
          [placeholder]="deprecatedPeriodOption()?.i18nLabel | translate"
          [disabled]="viewOnly"
        >
          <mat-option
            *ngFor="let option of durationPeriodOptions"
            [value]="option.value"
            class="contract-duration-option"
          >
            {{ option.i18nLabel | translate }}
          </mat-option>
        </mat-select>
      </glxy-form-field>
    </span>
  `,
  imports: [GalaxyFormFieldModule, FormsModule, TranslateModule, MatTooltipModule, MatSelectModule, CommonModule],
})
export class OrderContractDurationComponent {
  @Input() hideTooltip = false;
  @Input() viewOnly = false;

  _contractDuration: DurationInterface;
  @Input()
  get contractDuration(): DurationInterface {
    return this._contractDuration;
  }
  set contractDuration(value: DurationInterface) {
    this._contractDuration = value;
  }

  @Output() contractDurationChange = new EventEmitter<DurationInterface>();

  handleContractDurationValueChange(value: number): void {
    this.contractDuration.value = value;
    this.contractDurationChange.emit(this.contractDuration);
  }

  handleContractDurationPeriodChange(period: DurationPeriod): void {
    this.contractDuration.duration = period;
    this.contractDurationChange.emit(this.contractDuration);
  }

  protected durationPeriodOptions = GetDurationPeriodsOptions();

  deprecatedPeriodOption(): DurationPeriodOption | undefined {
    const period = this.contractDuration.duration;
    return GetDeprecatedDurationPeriodOptions().find((option) => option.value === period);
  }
}

interface DurationPeriodOption {
  i18nLabel: string;
  value: number;
}

function GetDurationPeriodsOptions(): DurationPeriodOption[] {
  return [
    {
      i18nLabel: 'LIB_ORDERS.COMMON.ORDER_DETAILS.CONTRACT_DURATIONS.MONTH',
      value: DurationPeriod.MONTH,
    },
    {
      i18nLabel: 'LIB_ORDERS.COMMON.ORDER_DETAILS.CONTRACT_DURATIONS.YEAR',
      value: DurationPeriod.YEAR,
    },
  ];
}

function GetDeprecatedDurationPeriodOptions(): DurationPeriodOption[] {
  return [
    {
      i18nLabel: 'LIB_ORDERS.COMMON.ORDER_DETAILS.CONTRACT_DURATIONS.DAY',
      value: DurationPeriod.DAY,
    },
    {
      i18nLabel: 'LIB_ORDERS.COMMON.ORDER_DETAILS.CONTRACT_DURATIONS.WEEK',
      value: DurationPeriod.WEEK,
    },
  ];
}
