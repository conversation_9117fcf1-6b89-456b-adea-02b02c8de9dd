@use 'design-tokens' as dt;
@use 'utilities' as *;

:host {
  display: block;
}

.select-recipient-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  @include tablet {
    gap: dt.$spacing-3;
    flex-wrap: nowrap;
  }
}
.user-selector {
  flex-grow: 1;
  flex-shrink: 1;
  max-width: 100%;
  min-width: 100%;
  word-wrap: break-word;
  word-break: break-word;
  @include tablet {
    max-width: 70%;
    min-width: 0;
  }
}
.user-selector-input {
  flex: 1 1 0;
  margin-bottom: dt.$spacing-2;
  @include tablet {
    margin-bottom: dt.$spacing-4;
  }
}
.associate-user-button-spacing {
  margin-top: 0;
  margin-bottom: dt.$spacing-4;
  @include tablet {
    margin-top: dt.$spacing-1;
    margin-bottom: 0;
  }
}

.associate-user-button-empty-spacing {
  margin-bottom: dt.$spacing-4;
  @include tablet {
    margin-top: dt.$spacing-1;
    margin-bottom: 0;
  }
}

.associate-user-button {
  @include tablet {
    min-width: 27%;
  }
  word-break: break-word;
}
.associate-user-button-error {
  @include tablet {
    padding-bottom: dt.$spacing-4;
  }
}
