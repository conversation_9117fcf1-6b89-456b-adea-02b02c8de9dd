import { Inject, Injectable } from '@angular/core';
import { AccountGroup, ProjectionFilter, ReadFilter } from '@galaxy/account-group';
import { filterNullAndUndefined } from '@vendasta/rx-utils';
import {
  CommonField,
  ConfigInterface,
  CustomField,
  Field,
  NotificationsService,
  Order,
  SalesOrdersService,
  Status,
  SubscriptionLocation,
} from '@vendasta/sales-orders';
import { User } from '@vendasta/store';
import { BehaviorSubject, combineLatest, Observable, of, throwError } from 'rxjs';
import {
  catchError,
  distinctUntilChanged,
  filter,
  first,
  map,
  publishReplay,
  refCount,
  shareReplay,
  switchMap,
  switchMapTo,
  tap,
} from 'rxjs/operators';
import { OrderConfirmationActionConfig } from '../components/order-actions/order-actions.component';
import { CORE_ORDER_CONFIG_TOKEN, CoreOrderConfig, PAGE_ORDER_CONFIG_TOKEN, PageOrderConfig } from './tokens';
import { SalesOrderProjectionFilterInterface } from '@vendasta/sales-orders/lib/_internal/interfaces';

export interface OrderState {
  subscriptionLocations: Partial<Record<SubscriptionLocation, boolean>>;
}

const defaultOrderState: OrderState = {
  subscriptionLocations: {},
};

/**
 * This is a page service meant to track all the complicated state involved in an order.
 */
@Injectable()
export class OrderStoreService {
  private readonly state$$ = new BehaviorSubject<OrderState>(defaultOrderState);

  public readonly order$: Observable<Order>;
  public readonly orderConfig$: Observable<ConfigInterface>;
  public readonly orderConfirmationActionConfig$: Observable<OrderConfirmationActionConfig>;
  public readonly productIds$: Observable<string[]>;

  // Push values to this subject to cause the order to be re-fetched.
  private readonly reloadOrder$$ = new BehaviorSubject<unknown>(undefined);
  private readonly orderId$: Observable<string>;
  private readonly businessId$: Observable<string>;
  public readonly business$: Observable<AccountGroup>;
  public readonly businessUsers$: Observable<User[]>;

  loadingOrder$$ = new BehaviorSubject<boolean>(true);
  loadingOrder$ = this.loadingOrder$$.asObservable();

  constructor(
    @Inject(CORE_ORDER_CONFIG_TOKEN) public readonly coreOrderConfig: CoreOrderConfig,
    @Inject(PAGE_ORDER_CONFIG_TOKEN) public readonly pageOrderConfig: PageOrderConfig,
    private readonly salesOrdersService: SalesOrdersService,
    protected readonly salesOrdersNotificationService: NotificationsService,
  ) {
    this.orderId$ = this.pageOrderConfig.orderId$.pipe(distinctUntilChanged());
    this.businessId$ = this.pageOrderConfig.businessId$.pipe(distinctUntilChanged());

    this.business$ = this.businessId$.pipe(
      switchMap((businessId) => {
        const mask = new ProjectionFilter({ napData: true, accountGroupExternalIdentifiers: true });
        const readFilter = new ReadFilter({ includeDeleted: true });
        return this.coreOrderConfig.listenToBusiness(businessId, mask, readFilter);
      }),
      shareReplay(1),
    );

    this.businessUsers$ = this.business$.pipe(
      switchMap((business) => {
        if (!business || business.deleted) {
          return of([]);
        }
        return this.coreOrderConfig.listenToBusinessUsers(
          business.externalIdentifiers.partnerId,
          business.accountGroupId,
        );
      }),
      catchError((err) => {
        if (err.status === 400) {
          return of([]);
        }
        return throwError(err);
      }),
      shareReplay(1),
    );

    this.order$ = combineLatest([this.orderId$, this.businessId$, this.reloadOrder$$]).pipe(
      filter(([orderId, businessId]) => Boolean(orderId && businessId)),
      tap(() => {
        this.loadingOrder$$.next(true);
      }),
      switchMap(([orderId, businessId]) => {
        const projectionFilter = {
          includeUserInfoInStatusHistory: true,
          includeCustomerRecipient: true,
        } as SalesOrderProjectionFilterInterface;
        return this.salesOrdersService.get(orderId, businessId, projectionFilter);
      }),
      map((order) => {
        order.status = order.status ?? Status.SUBMITTED;
        return order;
      }),
      tap(() => {
        this.loadingOrder$$.next(false);
      }),
      catchError((err) => {
        // TODO: Add RXJS retry logic - only set loading to false after last retry fails
        this.loadingOrder$$.next(false);
        return throwError(err);
      }),
      shareReplay(1),
    );

    this.orderConfig$ = this.order$.pipe(
      filterNullAndUndefined(),
      distinctUntilChanged(),
      switchMap((order) => this.salesOrdersService.getConfig(order.partnerId, order.marketId)),
      shareReplay(1),
    );

    this.productIds$ = this.setupProductIds$();
    this.orderConfirmationActionConfig$ = combineLatest([this.orderConfig$]).pipe(
      map(([salesOrdersConfig]) => ({
        allowSendDirectToAdmin: salesOrdersConfig.workflowStepOptions.allowSendDirectToAdmin,
        allowSendToCustomer: salesOrdersConfig.workflowStepOptions.allowSendToCustomer,
        customSalespersonActions: salesOrdersConfig.workflowStepOptions.customSalespersonActions,
      })),
    );
  }

  // user is implicitly grabbed from the context in the Sales Orders service
  isUserSubscribedToOrderAtLocation(location: SubscriptionLocation): Observable<boolean> {
    return combineLatest([this.orderId$, this.businessId$]).pipe(
      switchMap(([orderId, businessId]) =>
        this.salesOrdersNotificationService.getSubscribedLocations(businessId, orderId),
      ),
      tap((locations) => {
        const subscriptionLocations: Partial<Record<SubscriptionLocation, boolean>> = (locations || []).reduce(
          (acc, curr) => {
            return { ...acc, [curr]: true };
          },
          {},
        );
        this.state$$.next({ ...this.state$$.getValue(), subscriptionLocations });
      }),
      switchMapTo(this.state$$.pipe(map((state) => state.subscriptionLocations[location] || false))),
    );
  }

  // user is implicitly grabbed from the context in the Sales Orders service
  async subscribeUserToOrderAtLocation(location: SubscriptionLocation): Promise<void> {
    await this.salesOrdersNotificationService
      .subscribe(await this.getBusinessId(), await this.getOrderId(), location)
      .toPromise();
    this.setSubscriptionLocation(location, true);
  }

  // user is implicitly grabbed from the context in the Sales Orders service
  async unSubscribeUserToOrderAtLocation(location: SubscriptionLocation): Promise<void> {
    await this.salesOrdersNotificationService
      .unsubscribe(await this.getBusinessId(), await this.getOrderId(), location)
      .toPromise();
    this.setSubscriptionLocation(location, false);
  }

  async sendExistingOrderToCustomerForApproval(
    userId: string,
    expiry: Date | null,
    orderIsSmbPayable?: boolean,
  ): Promise<unknown> {
    return this.salesOrdersService.sendExistingOrderToCustomerForApproval(
      await this.getOrderId(),
      await this.getBusinessId(),
      userId,
      expiry,
      orderIsSmbPayable,
    );
  }

  async convertToDraft(): Promise<unknown> {
    return this.salesOrdersService.convertToDraft(await this.getOrderId(), await this.getBusinessId()).toPromise();
  }

  async submitDraft({
    customFormData,
    extraFieldsData,
    commonFormData,
    userId,
    expiry,
  }: {
    customFormData: CustomField[];
    extraFieldsData: Field[];
    commonFormData: CommonField[];
    userId: string;
    expiry?: Date;
  }): Promise<unknown> {
    if (!userId) {
      return this.salesOrdersService
        .submitDraftSalesOrder(
          await this.getOrderId(),
          await this.getBusinessId(),
          customFormData,
          commonFormData,
          extraFieldsData,
        )
        .toPromise();
    }
    return this.salesOrdersService
      .submitDraftForCustomerApproval(
        await this.getOrderId(),
        await this.getBusinessId(),
        userId,
        customFormData,
        commonFormData,
        extraFieldsData,
        expiry,
      )
      .toPromise();
  }

  // Request cancellation of an order. If order is not provided, the orderId and
  // accountGroupId from the route will be used.
  async requestCancellation(
    notes: string,
    order?: {
      orderId: string;
      businessId: string;
    },
  ): Promise<unknown> {
    const orderId = order ? order.orderId : await this.getOrderId();
    const businessId = order ? order.businessId : await this.getBusinessId();
    return this.salesOrdersService.requestCancellation(orderId, businessId, notes).toPromise();
  }

  reloadOrder(): void {
    this.reloadOrder$$.next(undefined);
  }

  private getOrderId(): Promise<string> {
    return this.orderId$.pipe(filter<string>(Boolean), first()).toPromise();
  }

  private getBusinessId(): Promise<string> {
    return this.businessId$.pipe(filter<string>(Boolean), first()).toPromise();
  }

  private getPartnerId(): Promise<string> {
    return this.order$
      .pipe(
        map((order) => order.partnerId),
        filter<string>(Boolean),
        first(),
      )
      .toPromise();
  }

  private setSubscriptionLocation(location: SubscriptionLocation, subscribed: boolean): void {
    this.state$$.next({
      ...this.state$$.getValue(),
      subscriptionLocations: {
        ...this.state$$.getValue().subscriptionLocations,
        [location]: subscribed,
      },
    });
  }

  private setupProductIds$(): Observable<string[]> {
    const packages$ = this.order$.pipe(
      switchMap((order) => {
        const packageLineItems = order.lineItems?.filter((li) => !!li.packageId) || [];
        if (packageLineItems.length > 0) {
          return this.coreOrderConfig.getMultiPackages(packageLineItems.map((li) => li.packageId));
        }
        return of([]);
      }),
      shareReplay(1),
    );
    return combineLatest([this.order$, packages$]).pipe(
      map(([order, packages]) => {
        const unpackagedApps = order.lineItems?.filter((li) => !!li.appKey) || [];
        const appIds = unpackagedApps.map((np) => np.appKey.appId);
        packages.map((p) => (p.lineItems.lineItems || []).map((li) => appIds.push(li.id)));
        return appIds;
      }),
      publishReplay(1),
      refCount(),
    );
  }
}
