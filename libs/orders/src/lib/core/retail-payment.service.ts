import { Injectable } from '@angular/core';
import { combineLatest, forkJoin, Observable, of, Subject } from 'rxjs';
import {
  distinctUntilChanged,
  map,
  shareReplay,
  startWith,
  switchMap,
  take,
  tap,
  withLatestFrom,
} from 'rxjs/operators';
import {
  PaymentMethod,
  PaymentMethodService,
  PaymentMethodType,
  PaymentService,
  RetailCustomerConfiguration,
  RetailCustomerConfigurationService,
  StripeService,
} from '@galaxy/billing';
import { OrderStoreService } from './orders.service';
import { Order, SalesOrdersService } from '@vendasta/sales-orders';
import { OrderLineItemValidationService } from '../components/order-line-item-validation/order-line-item-validation.service';
import { OrdersEditOrderFormValidationService } from '../components/edit-order/edit-order-form-validation.service';
import { MatDialog } from '@angular/material/dialog';
import { SubmitForChargeDialogComponent } from '../components/retail-summary/submit-for-charge-dialog/submit-for-charge-dialog.component';

export interface RetailPaymentData {
  stripeConnectId?: string;
  stripeKey?: string;
  paymentMethodMap?: Map<PaymentMethodType, PaymentMethod[]>;
  defaultPaymentMethod?: PaymentMethod;
  defaultCustomerRecipientId?: string;
  loading: boolean;
}

export interface SelectPaymentMethodEvent {
  paymentMethodId: string;
  newMethod: boolean;
  userId: string;
}

@Injectable()
export class RetailPaymentService {
  public readonly retailPaymentData$: Observable<RetailPaymentData>;

  private readonly selectPaymentMethod$$ = new Subject<SelectPaymentMethodEvent>();

  constructor(
    private readonly dialog: MatDialog,
    private readonly orderService: OrderStoreService,
    private readonly paymentService: PaymentService,
    private readonly paymentMethodService: PaymentMethodService,
    private readonly stripeService: StripeService,
    private readonly customerConfigService: RetailCustomerConfigurationService,
    private readonly salesOrderService: SalesOrdersService,
    private readonly lineItemValidationService: OrderLineItemValidationService,
    private readonly editOrderFormValidationService: OrdersEditOrderFormValidationService,
  ) {
    const stripeConnectId$ = this.fetchStripeConnectId$();
    const paymentMethods$ = this.fetchPaymentMethods$();
    const retailConfiguration$ = this.fetchRetailConfiguration$();
    const selectPaymentMethodLoading$ = this.selectPaymentMethod$$.asObservable().pipe(
      withLatestFrom(this.orderService.order$),
      switchMap(([paymentMethod, order]) => this.setupSelectPaymentMethod$(paymentMethod, order)),
      tap(() => this.orderService.reloadOrder()),
      startWith(false),
    );

    this.retailPaymentData$ = combineLatest([
      stripeConnectId$,
      paymentMethods$,
      retailConfiguration$,
      selectPaymentMethodLoading$,
    ]).pipe(
      map(([stripeConnectId, paymentMethods, retailConfiguration, selectPaymentLoading]) => {
        return {
          loading: selectPaymentLoading,
          stripeConnectId: stripeConnectId,
          stripeKey: this.stripeService.getConnectPublicKey(),
          paymentMethodMap: this._filterOutNonCardPaymentMethods(paymentMethods.paymentMethods),
          defaultPaymentMethod: paymentMethods.defaultPaymentMethod,
          defaultCustomerRecipientId: retailConfiguration?.contactId,
        };
      }),
      startWith({ loading: true }),
    );
  }

  private fetchStripeConnectId$(): Observable<string> {
    return this.orderService.order$.pipe(
      switchMap((order) => {
        return this.paymentService
          .getRetailProvider(order.partnerId)
          .pipe(map((retailProvider) => retailProvider.stripeConnectId));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  private fetchPaymentMethods$(): Observable<{
    paymentMethods: PaymentMethod[];
    defaultPaymentMethod?: PaymentMethod;
  }> {
    return this.orderService.order$.pipe(
      switchMap((order) =>
        combineLatest([of(order), this.paymentMethodService.list(order.partnerId, order.businessId)]),
      ),
      map(([order, paymentMethods]) => {
        let defaultPaymentMethod: PaymentMethod;
        if (order.paymentMethodToken) {
          defaultPaymentMethod = (paymentMethods || []).find(
            (method) => method?.details?.id === order.paymentMethodToken,
          );
        } else {
          defaultPaymentMethod = (paymentMethods || []).find((method) => method?.default);
        }
        return {
          paymentMethods: paymentMethods,
          defaultPaymentMethod:
            defaultPaymentMethod?.type === PaymentMethodType.CARD ? defaultPaymentMethod : undefined,
        };
      }),
    );
  }

  private fetchRetailConfiguration$(): Observable<RetailCustomerConfiguration> {
    return this.orderService.order$.pipe(
      switchMap((order) => {
        return this.customerConfigService
          .get(order.partnerId, order.businessId)
          .pipe(distinctUntilChanged(), shareReplay({ bufferSize: 1, refCount: true }));
      }),
    );
  }

  private setupSelectPaymentMethod$(paymentMethod: SelectPaymentMethodEvent, order: Order): Observable<boolean> {
    const updateRecipientId$ = this.salesOrderService.updateCustomerRecipientUserId(
      order.orderId,
      order.businessId,
      paymentMethod.userId,
    );
    const updatePaymentMethodToken$ = this.salesOrderService.updatePaymentMethodToken(
      order.orderId,
      order.businessId,
      paymentMethod.paymentMethodId,
    );
    const calls = [updatePaymentMethodToken$];
    if (paymentMethod.userId?.length > 0) {
      calls.unshift(updateRecipientId$);
    }
    return forkJoin(calls).pipe(
      map(() => false),
      startWith(true),
    );
  }

  private _filterOutNonCardPaymentMethods(methods: PaymentMethod[]): Map<PaymentMethodType, PaymentMethod[]> {
    const cardPaymentMethods = methods.filter((m) => m.type === PaymentMethodType.CARD);
    const grouped = new Map<PaymentMethodType, PaymentMethod[]>();
    if (cardPaymentMethods.length > 0) {
      grouped.set(PaymentMethodType.CARD, cardPaymentMethods);
    }
    return grouped;
  }

  public selectPaymentMethod(paymentMethod: SelectPaymentMethodEvent) {
    this.selectPaymentMethod$$.next(paymentMethod);
  }

  // TODO: this is duplicated logic from the orders-payment-method component. can we share it?
  public openChargeConfirmationDialog(retailPaymentData: RetailPaymentData, order: Order): Observable<boolean> {
    return this.editOrderFormValidationService.validateFormsForCharging().pipe(
      switchMap((isFormValid) => {
        if (!isFormValid) {
          return of(null);
        }
        return this.lineItemValidationService.areBillingTermsValidOrOpenModal$();
      }),
      take(1),
      switchMap((areBillingTermsValid) => {
        if (!areBillingTermsValid) {
          return of(null);
        }
        return this.dialog
          .open(SubmitForChargeDialogComponent, {
            width: '600px',
            data: {
              accountId: retailPaymentData.stripeConnectId,
              partnerId: order.partnerId,
            },
            autoFocus: false,
          })
          .afterClosed();
      }),
      take(1),
      switchMap((res) => {
        const paymentMethodToken = order.paymentMethodToken || retailPaymentData.defaultPaymentMethod?.details?.id;
        if (res && paymentMethodToken) {
          return this.salesOrderService.updatePaymentMethodToken(order.orderId, order.businessId, paymentMethodToken);
        }
        return of(false);
      }),
      switchMap((shouldOrder) => {
        if (shouldOrder) {
          return this.salesOrderService.chargeOrder(order.orderId, order.businessId);
        }
        return of(false);
      }),
    );
  }
}
