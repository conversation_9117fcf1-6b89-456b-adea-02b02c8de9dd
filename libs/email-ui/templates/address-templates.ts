export const AddressTemplates = new Map<string, string>([
  [
    'default',
    `<p>
          {{ businessName }}, {% if businessStreetAddress %}{{ businessStreetAddress }}, {% endif %} {% if businessStreetAddress2 %}{{ businessStreetAddress2 }}, {% endif %}
          <br> {% if businessCity %}{{ businessCity }}, {% endif %} {% if businessState %}{{ businessState }}, {% endif %} {% if businessCountry %}{{ businessCountry }}, {% endif %} {{ businessPostalCode }}
          {% if businessPhoneNumber %} <br> <a href="tel:{{ businessPhoneNumber }}" style="color: inherit">{{ businessPhoneNumber }}</a>, {% endif %}
    </p>`,
  ],
  [
    'DE',
    `<p>
          {{ businessName }}, {% if businessStreetAddress %}{{ businessStreetAddress }}, {% endif %} {% if businessStreetAddress2 %}{{ businessStreetAddress2 }}, {% endif %}
          <br> {% if businessPostalCode %}{{ businessPostalCode }}, {% endif %} {% if businessCity %}{{ businessCity }}, {% endif %} {% if businessState %}{{ businessState }}, {% endif %} {% if businessCountry %}{{ businessCountry }}{% endif %}
          {% if businessPhoneNumber %} <br> <a href="tel:{{ businessPhoneNumber }}" style="color: inherit">{{ businessPhoneNumber }}</a>, {% endif %}
    </p>`,
  ],
]);
