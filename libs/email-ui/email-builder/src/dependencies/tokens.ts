import { VariableMenuItem } from '../components/dynamic-component-selector';
import { InjectionToken } from '@angular/core';
import { Footer, TopLogo } from '../components/email-advanced-settings/email-advanced-settings.component';
import { Observable } from 'rxjs';
import { EmailPreviewHydrationData } from '../services/preview-rendering.service';

export const DYNAMIC_COMPONENT_DATA_TOKEN: InjectionToken<VariableMenuItem[]> = new InjectionToken<VariableMenuItem[]>(
  'com.vendasta.email_builder.dynamic_component_data',
);
export const TEMPLATE_HYDRATION_DATA_TOKEN: InjectionToken<Observable<EmailPreviewHydrationData>> = new InjectionToken<
  Observable<EmailPreviewHydrationData>
>('com.vendasta.email_builder.hydration_data');
export const PLACEHOLDER_LOGO_URL_TOKEN: InjectionToken<Observable<string>> = new InjectionToken<Observable<string>>(
  'com.vendasta.email_builder.placeholder_logo_url',
);
export const EMAIL_FOOTER_TOKEN: InjectionToken<Footer> = new InjectionToken<Observable<Footer>>(
  'com.vendasta.email_builder.footer_text',
);

export const EMAIL_TOP_LOGO_TOKEN: InjectionToken<TopLogo> = new InjectionToken<TopLogo>(
  'com.vendasta.email_builder.top_logo',
);
