import { AfterViewInit, Component, HostBinding, Input, On<PERSON><PERSON>roy, ViewChild, OnInit } from '@angular/core';
import { NgForm } from '@angular/forms';
import { Subscription } from 'rxjs';
import { FileInfo, FileUploadStatus } from '@vendasta/galaxy/uploader';

import { EmailPreviewRefreshService } from '../../services/email-preview-refresh.service';
import { emptyColors } from './empty-settings';
import { Validator } from '@vendasta/shared';

export const ALL_ADVANCED_SECTIONS_ENABLED: AdvancedSections = {
  pageSetup: true,
  topLogo: true,
  footer: true,
  colors: true,
};

export interface EmailSetup {
  googleAnalytics?: unknown; // we have removed googleAnalytics from the email builder. This is here to prevent errors
  pageSetup?: PageSetup;
  topLogo?: TopLogo;
  footer?: Footer;
  colors?: Colors;
  isValid?: boolean;
}

export interface AdvancedSections {
  pageSetup?: boolean;
  topLogo?: boolean;
  footer?: boolean;
  colors?: boolean;
}

export interface PageSetup {
  outerWidth?: number;
  innerWidth?: number;
  innerHorizontalPadding?: number;
  topPadding?: number;
  bottomContentPadding?: number;
  collapsed?: boolean;
}

export interface TopLogo {
  showLogo?: 'show' | 'hide' | 'custom';
  customImageUrl?: string;
  width?: number;
  align?: 'center' | 'left' | 'right';
  topPadding?: number;
  bottomPadding?: number;
  collapsed?: boolean;
  link?: string;
  isValid?: boolean;
  defaultSourceUrl?: string;
}

export interface Footer {
  disclaimer?: string;
  text?: string; // This text will override the default partner contact and unsubscribe footer content
  showContactInfo?: 'hide' | 'show';
  showUnsubscribeLinks?: 'hide' | 'show';
  collapsed?: boolean;
}

export interface Colors {
  textColor?: string;
  titleColor?: string;
  subtitleColor?: string;
  linkColor?: string;
  footerTextColor?: string;
  textBackground?: string;
  pageBackground?: string;
  collapsed?: boolean;
  isValid?: boolean;
}

/**
 * A small function to add missing keys with default values to an object.
 * If the target object might not have the initial key, that can be checked an added using the `targetKey` input
 * @param target - The object you want to check and add keys to.
 * @param template - used as a base to add missing keys and their default value to the target object.
 * @param targetKey - Optional. If the target object might not have the initial key, that can be checked an added using the `targetKey` input
 *
 * @example
 * // Check object this.emailSetup.colors to make sure colors has all keys
 * addMissingKeysToObject(this.emailSetup.colors, emptyColors)
 * @example
 * // Check if object this.emailSetup has colors key/object, then make sure colors object has all keys
 * addMissingKeysToObject(this.emailSetup, emptyColors, 'colors')
 */
// eslint-disable-next-line @typescript-eslint/ban-types
export function addMissingKeysToObject(target: Object, template: Object, targetKey?: string): void {
  // Check to see if parent key exists, if not, add it and update target to point to it.
  if (targetKey) {
    target = target[targetKey] ? target[targetKey] : (target[targetKey] = {});
  }

  // Add any missing keys to the target with default values from the template
  const keys = Object.keys(template);
  keys.forEach((key) => {
    if (target[key] === undefined) {
      target[key] = template[key];
    }
  });
}

@Component({
  selector: 'email-advanced-settings',
  templateUrl: './email-advanced-settings.component.html',
  styleUrls: ['./email-advanced-settings.component.scss'],
  standalone: false,
})
export class EmailAdvancedSettingsComponent implements AfterViewInit, OnInit, OnDestroy {
  @HostBinding('class') class = 'email-advanced-settings';

  @Input() emailSetup: EmailSetup;
  @Input() sectionsEnabled: AdvancedSections;

  @ViewChild('sizingAndSpacingForm') sizingAndSpacingForm: NgForm;
  @ViewChild('topLogoForm') topLogoForm: NgForm;
  @ViewChild('footerForm') footerForm: NgForm;
  @ViewChild('colorForm') colorForm: NgForm;

  public urlMailtoOrDynamicContentRegex = Validator.URL_MAILTO_OR_DYNAMIC_CONTENT_REGEX;
  public colorRegex = Validator.HEX_COLOR_REGEX;

  FileUploadStatus = FileUploadStatus;

  private readonly subs: Subscription[] = [];

  constructor(public emailPreview: EmailPreviewRefreshService) {}

  ngOnInit(): void {
    // Add colors section and default values if they don't exist
    addMissingKeysToObject(this.emailSetup, emptyColors, 'colors');
  }

  ngAfterViewInit(): void {
    this.setupPreviewRefresh();
    this.setupFormValidation();
  }

  setupFormValidation(): void {
    this.subs.push(
      this.topLogoForm?.statusChanges.subscribe((topLogoValid) => {
        this.emailSetup.topLogo.isValid = topLogoValid === 'VALID';
      }),
    );
    this.subs.push(
      this.colorForm?.statusChanges.subscribe((colorsValid) => {
        this.emailSetup.colors.isValid = colorsValid === 'VALID';
      }),
    );
  }

  setupPreviewRefresh(): void {
    this.subs.push(
      this.sizingAndSpacingForm?.form?.valueChanges?.subscribe(() => {
        this.emailPreview.refresh();
      }),
      this.topLogoForm?.valueChanges?.subscribe(() => {
        this.emailPreview.refresh();
      }),
      this.footerForm?.form?.valueChanges?.subscribe(() => {
        this.emailPreview.refresh();
      }),
      this.colorForm?.valueChanges?.subscribe(() => {
        this.emailPreview.refresh();
      }),
    );
  }

  ngOnDestroy(): void {
    this.subs.forEach((s) => s?.unsubscribe());
  }

  setContentImage(file: FileInfo): void {
    this.emailSetup.topLogo.customImageUrl = file.url;
    this.emailPreview.refresh();
  }
}
