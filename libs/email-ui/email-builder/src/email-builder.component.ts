import { BreakpointObserver } from '@angular/cdk/layout';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  HostBinding,
  inject,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { NgForm } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { GalaxyNavControlService } from '@vendasta/galaxy/nav';
import { GALAXY_UPLOADER_SERVICE_TOKEN } from '@vendasta/galaxy/uploader';
import { combineLatest, identity, Subject, Subscription, throwError } from 'rxjs';
import { catchError, debounceTime, filter, map, mergeMap, share, skip, take, tap } from 'rxjs/operators';
import { DynamicComponentSelectorService, VariableMenuItem } from './components/dynamic-component-selector';
import {
  AdvancedSections,
  ALL_ADVANCED_SECTIONS_ENABLED,
} from './components/email-advanced-settings/email-advanced-settings.component';
import { InboxPreviewHtml } from './inbox-preview';
import { EmailPreviewRefreshService } from './services/email-preview-refresh.service';
import { HasUnsavedChangesGuard } from './services/has-unsaved-changes.guard';
import { ImageUploadService } from './services/image-upload.service';
import { EmailPreviewHydrationData, PreviewRenderingService } from './services/preview-rendering.service';
import { EmailContentData, type OptionalBlockIds } from './types/contentblocks';

export const MOBILE_MAX_WIDTH = 900;

@Component({
  selector: 'email-builder',
  templateUrl: './email-builder.component.html',
  styleUrls: ['./email-builder.component.scss'],
  providers: [{ provide: GALAXY_UPLOADER_SERVICE_TOKEN, useExisting: ImageUploadService }],
  standalone: false,
})
export class EmailBuilderComponent implements OnDestroy, OnInit, OnChanges, AfterViewInit {
  @HostBinding('class') class = 'email-builder';
  @HostBinding('class.email-builder-is-mobile') emailBuilderIsMobile = false;

  /** The email builder is considered mobile at this viewport width and below **/
  @Input() mobileMaxWidth = MOBILE_MAX_WIDTH;

  @Input() emailContent: EmailContentData;

  @Input() advancedSections: AdvancedSections = ALL_ADVANCED_SECTIONS_ENABLED;

  @Input() optionalBlockTypes: OptionalBlockIds[];

  @Input() set previewHydrationParams(params: EmailPreviewHydrationData) {
    if (params !== null) {
      this.previewRenderingService.setPreviewHydrationParams(params);
      this.renderPreviewAndTemplate();
    }
  }

  @Input() readonly locale: string = undefined;

  @Input() readonly placeholderLogoUrl: string = 'https://via.placeholder.com/220x72?text=%20';

  @Input() dynamicComponents: VariableMenuItem[] = [];

  @Input() previewUserName = 'Jane MacAvery';

  private readonly failedRenders$$ = new Subject<boolean>();

  @Output() emailContentChanged = new EventEmitter<EmailContentData>();
  @Output() emailRenderUpdated = new EventEmitter<string>();
  @Output() htmlRendering = new EventEmitter<boolean>(false);
  @Output() emailRenderFailed = new EventEmitter<null>();

  @ViewChild('emailSetupForm') emailSetupForm: NgForm;

  private readonly breakpointObserver = inject(BreakpointObserver);
  private readonly translateService = inject(TranslateService);
  private readonly dynamicComponentSelectorService = inject(DynamicComponentSelectorService);
  private emailPreview = inject(EmailPreviewRefreshService);
  private unsavedGuard = inject(HasUnsavedChangesGuard);
  private sideNavControl = inject(GalaxyNavControlService);
  private changeDetectorRef = inject(ChangeDetectorRef);
  private previewRenderingService = inject(PreviewRenderingService);

  public renderedTemplate: string;
  loadingIndicator = false;
  stagedChanges = false;
  inboxPreviewHtmlToInject = InboxPreviewHtml;

  private subscriptions: Subscription[] = [];

  ngOnInit(): void {
    if (this.dynamicComponents?.length > 0) {
      this.dynamicComponentSelectorService.overrideParentVariableCategories(this.dynamicComponents);
    }

    // This is a temporary fix to remove google analytics from the email content
    // because we have removed google analytics from the email template renderer
    if (this.emailContent?.emailSetup?.googleAnalytics) {
      delete this.emailContent?.emailSetup?.googleAnalytics;
    }

    this.sideNavControl.closeForThisPage();

    // Track if the viewport is desktop or mobile
    this.subscriptions.push(
      this.breakpointObserver.observe('(max-width: ' + this.mobileMaxWidth + 'px)').subscribe((resp) => {
        this.emailBuilderIsMobile = resp.matches;
      }),
    );
    this.subscriptions.push(
      this.failedRenders$$.pipe(debounceTime(3000), filter(identity)).subscribe(() => {
        this.emailRenderFailed.emit();
      }),
    );

    // Reload the email preview
    this.subscriptions.push(
      this.emailPreview.refreshEmitter$
        // skip the first event, as that is the form initializing
        .pipe(skip(1))
        .subscribe(() => {
          this.emailHasBeenModified();
        }),
    );
    // Listen for form field updates and throttle them, so they
    this.subscriptions.push(
      this.emailPreview.refreshStartedEmitter$.pipe(skip(1)).subscribe(() => {
        this.loadingIndicator = true;
        this.stagedChanges = true;
      }),
    );

    // Listen for attempts to navigate away
    this.subscriptions.push(
      this.unsavedGuard.attemptedNavWhileUnsaved$.subscribe(() => {
        this.confirmLeavingWithUnsavedChanges();
      }),
    );
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.locale) {
      this.previewRenderingService.setLocale(changes.locale.currentValue);
    }
  }

  ngAfterViewInit(): void {
    this.subscriptions.push(
      this.emailSetupForm.statusChanges.subscribe((status) => {
        this.emailContent.basicSetupValid = status === 'VALID';
      }),
    );
    this.emailSetupForm.valueChanges.subscribe(() => {
      this.emailPreview.refresh();
    });
    this.renderPreviewAndTemplate();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  renderPreviewAndTemplate(): void {
    const customizedInboxPreview = this.inboxPreviewHtmlToInject
      .replace('{{ username }}', this.previewUserName ?? '')
      .replace('{{ subjectLine }}', this.emailContent.subjectLine ?? '')
      .replace('{{ previewText }}', this.emailContent.previewText ?? '');
    this.stagedChanges = false;
    const preparedEmailContentData = JSON.parse(JSON.stringify(this.emailContent));
    const contentWithTags$ = this.previewRenderingService.renderContentHTML(preparedEmailContentData).pipe(
      catchError((err) => {
        console.error('Error loading content:', err);
        return throwError(err);
      }),
      share(),
    );

    const hydratedContent$ = contentWithTags$.pipe(
      mergeMap((contentWithTags) =>
        this.previewRenderingService.hydrateEmailContent(preparedEmailContentData, contentWithTags).pipe(
          tap(() => this.failedRenders$$.next(false)),
          catchError((err) => {
            console.error('Error loading hydrated content:', err);
            this.failedRenders$$.next(true);
            return throwError(err);
          }),
        ),
      ),
    );

    const hydratedOrFallback$ = combineLatest([
      hydratedContent$,
      contentWithTags$.pipe(
        catchError(() => contentWithTags$.pipe(map((v) => v.replace('{{ partnerLogo }}', this.placeholderLogoUrl)))),
      ),
    ]);
    this.subscriptions.push(
      hydratedOrFallback$.pipe(take(1)).subscribe(([value, rawHTML]) => {
        this.renderedTemplate = value.replace(/(<body[^>]*>)/, '$1' + customizedInboxPreview);
        this.emailRenderUpdated.emit(rawHTML);
        this.htmlRendering.emit(false);
        if (!this.stagedChanges) {
          this.loadingIndicator = false;
        }
        this.changeDetectorRef.detectChanges();
      }),
    );
  }

  saveEmail(): void {
    this.unsavedGuard.markAsSaved();
  }

  emailHasBeenModified(): void {
    this.htmlRendering.emit(true);
    this.emailContentChanged.emit(this.emailContent);
    this.renderPreviewAndTemplate();
    this.unsavedGuard.markAsUnsaved();
  }

  confirmLeavingWithUnsavedChanges(): void {
    if (window.confirm(this.translateService.instant('UNSAVED_CHANGES.CONFIRM'))) {
      this.unsavedGuard.markAsSaved();
    }
  }
}
