@if (emailContent) {
  <!--  -->
  <!-- Desktop with preview column -->
  @if (!emailBuilderIsMobile) {
    <div class="email-builder-desktop">
      <!-- Preview -->
      <email-editor-preview [html]="renderedTemplate" [loadingIndicator]="loadingIndicator">
        <ng-content select="[preview_header]" ngProjectAs="[preview_header]" preview_header />
      </email-editor-preview>
      <!-- Controls & Settings -->
      <glxy-resize class="email-controls" [width]="440" minWidth="360px" maxWidth="640px" appearance="standard">
        <div class="block-list-and-settings" cdkScrollable>
          <ng-container *ngTemplateOutlet="emailSetup" />
          <ng-container *ngTemplateOutlet="emailBlocksList" />
          <ng-container *ngTemplateOutlet="emailAdvancedSettings" />
        </div>
      </glxy-resize>
    </div>
  } @else {
    <mat-tab-group animationDuration="0ms">
      <mat-tab label="{{ 'EMAIL_BUILDER.MAT_LABELS.EDIT_EMAIL' | translate }}">
        <ng-template matTabContent>
          <ng-container *ngTemplateOutlet="emailSetup" />
          <ng-container *ngTemplateOutlet="emailBlocksList" />
        </ng-template>
      </mat-tab>
      <mat-tab label="{{ 'EMAIL_BUILDER.MAT_LABELS.PREVIEW' | translate }}">
        <email-editor-preview [html]="renderedTemplate" [previewIsMobile]="true">
          <ng-content select="[preview_header]" ngProjectAs="[preview_header]" preview_header />
        </email-editor-preview>
      </mat-tab>
      <mat-tab label="{{ 'EMAIL_BUILDER.MAT_LABELS.ADVANCED' | translate }}">
        <ng-container *ngTemplateOutlet="emailAdvancedSettings" />
      </mat-tab>
    </mat-tab-group>
  }
} @else {
  <glxy-loading-spinner [fullWidth]="true" [fullHeight]="true" />
}

<!-- ==================================== -->
<!--              Templates               -->
<!-- ==================================== -->

<!-- Email Controls Tab: Subject -->
<ng-template #emailSetup>
  <form #emailSetupForm="ngForm">
    <div class="email-settings-container">
      <glxy-form-field>
        <glxy-label>{{ 'EMAIL_BUILDER.SUBJECT_CONTROLS_TAB.TEMPLATE_NAME' | translate }}</glxy-label>
        <input matInput required [(ngModel)]="emailContent.name" name="Template Name" autocomplete="off" />
      </glxy-form-field>
    </div>

    <mat-divider />

    <div class="email-settings-container">
      <glxy-form-field>
        <glxy-label>{{ 'EMAIL_BUILDER.SUBJECT_CONTROLS_TAB.SUBJECT_LINE' | translate }}</glxy-label>
        <input
          #subjectLine
          #subjectLineModel="ngModel"
          matInput
          required
          [(ngModel)]="emailContent.subjectLine"
          name="Subject Line"
          autocomplete="off"
          sharedContainsValidDynamicContent
        />
        <email-dynamic-component-selector glxySuffix [target]="subjectLine" [(model)]="emailContent.subjectLine" />
        @if (subjectLineModel.hasError('invalidDynamicComponent') && subjectLineModel.touched) {
          <glxy-error>
            {{ 'EMAIL_BUILDER.VALIDATION_ERRORS.DYNAMIC_CONTENT' | translate }}
          </glxy-error>
        }
      </glxy-form-field>

      <glxy-form-field>
        <glxy-label>{{ 'EMAIL_BUILDER.SUBJECT_CONTROLS_TAB.INBOX_PREVIEW' | translate }}</glxy-label>
        <glxy-label-hint>{{ 'EMAIL_BUILDER.SUBJECT_CONTROLS_TAB.INBOX_PREVIEW_HINT' | translate }}</glxy-label-hint>
        <input
          #previewText
          #previewTextModel="ngModel"
          matInput
          [(ngModel)]="emailContent.previewText"
          name="Inbox Preview Text"
          autocomplete="off"
          sharedContainsValidDynamicContent
        />
        <email-dynamic-component-selector glxySuffix [target]="previewText" [(model)]="emailContent.previewText" />
        @if (previewTextModel.hasError('invalidDynamicComponent') && previewTextModel.touched) {
          <glxy-error>
            {{ 'EMAIL_BUILDER.VALIDATION_ERRORS.DYNAMIC_CONTENT' | translate }}
          </glxy-error>
        }
      </glxy-form-field>
    </div>
  </form>
</ng-template>

<!-- Email Controls Tab: Content List -->
<ng-template #emailBlocksList>
  <email-blocks-list [blocks]="emailContent.emailContentBlocks" [optionalBlockTypes]="optionalBlockTypes" />
</ng-template>

<!-- Email Controls Tab: Page Setup -->
<ng-template #emailAdvancedSettings>
  <email-advanced-settings [emailSetup]="emailContent.emailSetup" [sectionsEnabled]="advancedSections" />
</ng-template>
