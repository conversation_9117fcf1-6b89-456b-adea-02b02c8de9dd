<form #blockInputs="ngForm">
  <glxy-form-field>
    <glxy-label>{{ 'EMAIL_BUILDER.BUTTON_BLOCK.TEXT' | translate }}</glxy-label>
    <input
      matInput
      #buttonText
      sharedContainsValidDynamicContent
      #buttonTextModel="ngModel"
      [(ngModel)]="content.buttonText"
      placeholder="{{ 'PLACEHOLDER_OPTIONAL' | translate }}"
      autocomplete="off"
      name="Button Text"
    />
    <email-dynamic-component-selector glxySuffix [target]="buttonText" [(model)]="content.buttonText" />
    @if (buttonTextModel.hasError('invalidDynamicComponent') && buttonTextModel.touched) {
      <glxy-error>
        {{ 'EMAIL_BUILDER.VALIDATION_ERRORS.DYNAMIC_CONTENT' | translate }}
      </glxy-error>
    }
  </glxy-form-field>

  <glxy-form-field>
    <glxy-label>{{ 'EMAIL_BUILDER.BUTTON_BLOCK.STYLE' | translate }}</glxy-label>
    <mat-button-toggle-group [(ngModel)]="content.buttonStyle" name="Button Style">
      <mat-button-toggle value="solid">Solid</mat-button-toggle>
      <mat-button-toggle value="outline">Outline</mat-button-toggle>
    </mat-button-toggle-group>
  </glxy-form-field>

  <glxy-form-field>
    <glxy-label>{{ 'EMAIL_BUILDER.BUTTON_BLOCK.COLOR' | translate }}</glxy-label>
    <input matInput type="color" [(ngModel)]="content.buttonColor" name="Button Color" />
    <input
      matInput
      type="text"
      [(ngModel)]="content.buttonColor"
      #buttonColor="ngModel"
      [pattern]="hexColorRegex"
      name="Button Color Hex"
    />
    @if (buttonColor.hasError('pattern') && buttonColor.touched) {
      <glxy-error>
        {{ 'EMAIL_BUILDER.VALIDATION_ERRORS.COLOR' | translate }}
      </glxy-error>
    }
  </glxy-form-field>

  <glxy-form-field>
    <glxy-label>{{ 'EMAIL_BUILDER.BUTTON_BLOCK.LINK' | translate }}</glxy-label>
    <input
      matInput
      type="url"
      name="Image Link"
      autocomplete="off"
      placeholder="https://"
      [pattern]="urlMailtoOrDynamicContentRegex"
      [(ngModel)]="content.link"
      #linkModel="ngModel"
      #linkUrl
    />

    <email-dynamic-component-selector glxySuffix [target]="linkUrl" [(model)]="content.link" />

    @if (linkModel.hasError('pattern') && linkModel.touched) {
      <glxy-error>
        {{ 'EMAIL_BUILDER.VALIDATION_ERRORS.LINK' | translate }}
      </glxy-error>
    }
  </glxy-form-field>
</form>
