import {
  AfterViewInit,
  Component,
  EventEmitter,
  HostBinding,
  Input,
  OnDestroy,
  Output,
  ViewChild,
} from '@angular/core';
import { NgForm } from '@angular/forms';
import { Subscription } from 'rxjs';

import { EmailPreviewRefreshService } from '../../services/email-preview-refresh.service';
import { Validator } from '@vendasta/shared';

// Interface
export interface ButtonBlock {
  buttonText: string;
  buttonStyle: 'solid' | 'outline';
  link: string;
  buttonColor: string;
  showBackupLink: boolean;
}

// Default blank value
export const blankButtonBlock: ButtonBlock = {
  buttonText: null,
  buttonStyle: 'solid',
  link: null,
  buttonColor: '#47b75d',
  showBackupLink: false,
};

@Component({
  selector: 'email-button-block',
  templateUrl: './button-block.component.html',
  styleUrls: ['./button-block.component.scss'],
  standalone: false,
})
export class ButtonBlockComponent implements AfterViewInit, OnDestroy {
  @HostBinding('class') class = 'email-button-block';
  @Input() content: ButtonBlock;
  @Output() isBlockValid = new EventEmitter<boolean>();
  @ViewChild('blockInputs') blockInputs: NgForm;

  public urlMailtoOrDynamicContentRegex = Validator.URL_MAILTO_OR_DYNAMIC_CONTENT_REGEX;
  public hexColorRegex = Validator.HEX_COLOR_REGEX;

  private readonly subscriptions: Subscription[] = [];

  constructor(private emailPreview: EmailPreviewRefreshService) {}

  ngAfterViewInit(): void {
    this.subscriptions.push(
      this.blockInputs.statusChanges.subscribe((status) => {
        this.isBlockValid.emit(status === 'VALID');
      }),
    );

    this.subscriptions.push(
      this.blockInputs.valueChanges.subscribe(() => {
        this.emailPreview.refresh();
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }
}
