{"name": "social-marketing", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/social-marketing/src", "prefix": "social-marketing", "tags": ["scope:shared"], "projectType": "library", "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/social-marketing/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}