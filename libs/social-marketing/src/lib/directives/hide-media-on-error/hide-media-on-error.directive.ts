import { Directive, ElementRef, HostListener, Renderer2 } from '@angular/core';

@Directive({
  standalone: true,
  selector: '[socialMarketingHideMediaOnError]',
})
export class HideMediaOnErrorDirective {
  constructor(private el: ElementRef<HTMLImageElement>, private renderer: Renderer2) {}

  @HostListener('error')
  onError() {
    const el = this.el.nativeElement;
    if (el.tagName.toLowerCase() === 'img' || el.tagName.toLowerCase() === 'video') {
      this.renderer.setStyle(el, 'display', 'none');
    }
  }
}
