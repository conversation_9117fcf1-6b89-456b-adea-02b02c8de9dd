import { Observable } from 'rxjs';
import { MeetingNavigationLinkService, MeetingSchedulerCalendarService } from './data-providers/providers';
import { map } from 'rxjs/operators';
import { getObservableString } from './utils';
import { NavigationOption } from '@vendasta/uikit';
import { ApplicationContextPropertiesSet, DayOfWeek } from '@vendasta/meetings';

export interface MeetingSchedulerConfig {
  // The current hostId: https://vendasta.jira.com/wiki/spaces/BOOKME/pages/227836157/Terminology#Host
  // MeetingScheduler requires a hostId for its features to work, but will patiently wait for a truthy string value to
  // be emitted.
  hostId$: Observable<string | undefined | null>;

  // The current set of application context properties based on the application.
  // For example, Sales Center would specify the partner and market
  // For example, Business Center would specify the business and market
  applicationContext$: Observable<ApplicationContextPropertiesSet>;

  // The current userId. This is the IAM ID for a user regardless of their persona.
  // Meeting Scheduler requires a userId for attributing data to a user across
  // different platforms and apps.
  userId$: Observable<string | undefined | null>;

  // An optional calendarService used to retrieve calendars the current host has access to.
  // https://vendasta.jira.com/wiki/spaces/BOOKME/pages/227836157/Terminology#Calendar
  // For example, in S&SC this corresponds to Sales Teams.
  calendarService?: MeetingSchedulerCalendarService;

  // An optional service that allows you to return a list of links to associate with meetings.
  // These links will be shown as part of the meeting details.
  // One example could be returning a link to a Business if the Meeting is associated with a Business.
  meetingNavigationLinkService?: MeetingNavigationLinkService;

  // By default, Meeting Scheduler provides a reasonable default during setup (9:00 AM - 5:00 PM).
  // However, if your app has special requirements you may override the hours of operation with something that makes
  // more sense. For example, you could have it start with a Business' hours of operation.
  // Since this configuration is a nice-to-have - if this observable emits an error or takes longer than 2 seconds
  // Meeting Scheduler's default availability will be returned (to avoid the page not rendering).
  initialAvailability$?: Observable<DailyAvailability>;

  // An optional list of recommended slugs to try during initial personal calendar setup.
  // For example, provided ['rlaforge', 'rlaforge-1', 'rlaforge-2'] Meeting Scheduler will attempt to pre-populate the
  // personalized link (slug) field during setup with the first of those values not already taken by another calendar.
  recommendedPersonalCalendarSlugs$?: Observable<string[]>;

  // An optional map of external calendar id to recommended slugs to try during initial group calendar setups.
  // For example, provided ['booking-private-ryan', 'bookingprivateryan', 'bpr'] Meeting Scheduler will attempt to
  // pre-populate the customized link (slug) field during setup with the first of those values no already taken by
  // another calendar. A list can be provided per calendar.
  recommendedGroupCalendarSlugs$?: Observable<{ [externalCalendarId: string]: string[] }>;

  // links allow MeetingScheduler components to route to appropriate sections of the application correctly.
  // Some links will support placeholders (for example, deep-linking to Meetings).
  // For default routing, just use the function getDefaultMeetingSchedulerLinks() in your provider.
  links: MeetingSchedulerNavigationLinks;

  // service keys given to the MeetingIntegrationsService during initialization on app startup.
  // This may change later, but these are needed for connecting / disconnecting external tools like Google, Zoom, etc.
  meetingIntegrationsServiceKeys: MeetingIntegrationServiceKeys;

  // Whether to use Meeting Scheduler's built-in navigation breadcrumbs.
  // If, instead, you want to listen to the breadcrumbs / actions Meeting Scheduler would use you can use the
  // MeetingSchedulerNavigationService and listen to the actions / breadcrumbs
  // Defaults to true
  useBreadcrumbs$?: Observable<boolean> | boolean;

  // Whether or not to show the new team / service create meeting type workflow.
  featureServicesAndTeamLinksEnabled$?: Observable<boolean>;

  featureGroupsandServicesEnabled$: Observable<boolean>;

  featureGroupCalendarSlugEditingEnabled$: Observable<boolean>;

  featureEmailTemplateEnabled$: Observable<boolean>;

  featureMeetingExpansionEnabled$: Observable<boolean>;

  featureEmbedCodeEnabled$: Observable<boolean>;

  featureRoundRobinEnabled$: Observable<boolean>;

  featurePhysicalLocationEnabled$: Observable<boolean>;

  featureMeetingBeforeBufferEnabled$: Observable<boolean>;

  featureMicrosoftTeamsEnabled$: Observable<boolean>;

  featureMyMeetingMultiHostEnabled$: Observable<boolean>;

  featureDateRangeEnabled$: Observable<boolean>;

  // A map of Meeting Scheduler translation keys (see en_devel.json) to an already translated string for the purposes of
  // displaying customized text depending on where Meeting Scheduler is being rendered.
  // Not every translation key necessarily respects this config.
  alternateTranslations?: { [translationKey: string]: string };

  icons: MeetingSchedulerIcons;
}

export interface MeetingIntegrationServiceKeys {
  integrationsPage: string;
  eventsAndMeetingsPage: string;

  eventsAndMeetingsPageCalendarConnected: string;
  eventsAndMeetingsPageConferencingConnected: string;
}

// Either a regular string or Observable<string> can be provided depending on if your link includes a dynamic element.
export interface MeetingSchedulerNavigationLinks {
  // A link to the settings page. This can optionally include a hostId. I.e., 'my-link/{{hostId}}/settings'.
  settingsPage: string | Observable<string>;

  // The link to the meeting list in the application. This can use MeetingScheduler MeetingList component, or a different one.
  // This can optionally include the hostId using 'meeting-list/{{hostId}}'
  meetingListPage: string | Observable<string>;

  // The link to the meeting list in the application. This can use MeetingScheduler MeetingList component, or a different one.
  // This can optionally include the hostId using 'meeting-list/{{hostId}}'
  meetingTypeListViewPage: string | Observable<string>;

  // Optional link to the integration page within the application (outside of the MeetingScheduler).
  integrationsPage: string | Observable<string>;

  // The link to the page for modifying a calendar
  // This expects a calendar id at the end
  calendarPage: string | Observable<string>;

  // The link to the page for viewing booked meetings
  calendarViewPage: string | Observable<string>;

  // The link to the page for modifying the bookingLink.
  // This should be able to accept a booking id / slug at  the end. For example '/rlaforge' for a booking link of rlaforge
  // should be capable of being appended. (Don't include the trailing / in the given url)
  // This can optionally include the hostId using 'meeting-list/{{hostId}}'
  eventTypePage: string | Observable<string>;

  // Optional link to dee-link to specific meeting details. MeetingScheduler supports deep-linking to the MeetingList page by default.
  // This is used when rescheduling / canceling / editing a meeting and should follow the same format as the schedulerURL on the Host.
  // The page linked to must support an action at the end of the URL (such as 'reschedule', or 'cancel')
  meetingDetailsLink?: string | Observable<string>;

  // The link to the page for modifying the group.
  groupPage: string | Observable<string>;

  // The link to the page for modifying the service.
  servicePage: string | Observable<string>;
}

export enum MeetingSchedulerNavigationButtonType {
  ICON_BUTTON = 'mat-icon-button',
  MAT_BUTTON = 'mat-button',
  MAT_RAISED_BUTTON = 'mat-raised-button',
}

export interface MeetingSchedulerNavActionButton {
  buttonType: MeetingSchedulerNavigationButtonType;
  labelTranslationKey?: string;
  icon?: string;
  tooltipTranslationKey?: string;
  link?: string;
  handler?: () => void;
  navigationOption?: NavigationOption;
}

export interface TimeRange {
  start: Date;
  end: Date;
}

export interface DailyAvailability {
  days: Map<DayOfWeek, TimeRange[]>;
}

export interface MeetingSchedulerIcons {
  meetingListGroupsSection: string;
}

export function getDefaultMeetingSchedulerIcons(): MeetingSchedulerIcons {
  return {
    meetingListGroupsSection: 'groups',
  };
}

// roots can be used to add a root url in front of the urls
export const defaultMeetingSchedulerLinks = Object.freeze({
  calendarViewPageRelative: 'my-meetings',
  settingsRelative: 'settings',
  calendarPageRelative: 'calendar',
  eventTypePageRelative: 'event-type',
  groupPageRelative: 'group',
  servicePageRelative: 'service',
  meetingListPageRelative: 'schedule',
  integrationsPageRelative: 'settings/integrations',
  meetingTypeListViewPageRelative: 'meeting-type',
});

export function getDefaultMeetingSchedulerLinks(
  roots?: Partial<MeetingSchedulerNavigationLinks>,
): MeetingSchedulerNavigationLinks {
  const settingsPage$ = getObservableString(roots?.settingsPage);
  const calendarPage$ = getObservableString(roots?.calendarPage);
  const eventTypePage$ = getObservableString(roots?.eventTypePage);
  const meetingListPage$ = getObservableString(roots?.meetingListPage);
  const integrationsPage$ = getObservableString(roots?.integrationsPage);
  const groupPage$ = getObservableString(roots?.groupPage);
  const servicePage$ = getObservableString(roots?.servicePage);
  const calendarViewPage$ = getObservableString(roots?.calendarViewPage);
  const meetingTypeListViewPage$ = getObservableString(roots?.meetingTypeListViewPage);
  return {
    settingsPage: settingsPage$.pipe(
      map((settingsPage) => `${settingsPage || ''}${defaultMeetingSchedulerLinks.settingsRelative}`),
    ),
    calendarPage: calendarPage$.pipe(
      map((calendarPage) => `${calendarPage || ''}${defaultMeetingSchedulerLinks.calendarPageRelative}`),
    ),
    eventTypePage: eventTypePage$.pipe(
      map((eventTypePage) => `${eventTypePage || ''}${defaultMeetingSchedulerLinks.eventTypePageRelative}`),
    ),
    meetingListPage: meetingListPage$.pipe(
      map((meetingListPage) => `${meetingListPage || ''}${defaultMeetingSchedulerLinks.meetingListPageRelative}`),
    ),
    integrationsPage: integrationsPage$.pipe(
      map((integrationsPage) => `${integrationsPage || ''}${defaultMeetingSchedulerLinks.integrationsPageRelative}`),
    ),
    groupPage: groupPage$.pipe(
      map((groupPage) => `${groupPage || ''}${defaultMeetingSchedulerLinks.groupPageRelative}`),
    ),
    servicePage: servicePage$.pipe(
      map((servicePage) => `${servicePage || ''}${defaultMeetingSchedulerLinks.servicePageRelative}`),
    ),
    calendarViewPage: calendarViewPage$.pipe(
      map((calendarViewPage) => `${calendarViewPage || ''}${defaultMeetingSchedulerLinks.calendarViewPageRelative}`),
    ),
    meetingTypeListViewPage: meetingTypeListViewPage$.pipe(
      map(
        (meetingTypeListViewPage) =>
          `${meetingTypeListViewPage || ''}${defaultMeetingSchedulerLinks.meetingTypeListViewPageRelative}`,
      ),
    ),
  };
}
