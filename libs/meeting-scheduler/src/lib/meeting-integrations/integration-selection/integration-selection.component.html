<div class="integrations">
  <form [formGroup]="form">
    <mat-radio-group [formControl]="integrationFormControl" class="vertical-radio-group">
      <ng-container *ngIf="googleMeetContext$ | async as googleMeetContext">
        <ng-container [ngTemplateOutlet]="integration" [ngTemplateOutletContext]="googleMeetContext"></ng-container>
      </ng-container>
      <ng-container *ngIf="zoomContext$ | async as zoomContext">
        <ng-container [ngTemplateOutlet]="integration" [ngTemplateOutletContext]="zoomContext"></ng-container>
      </ng-container>
      <ng-container *ngIf="microsoftContext$ | async as microsoftContext">
        <ng-container [ngTemplateOutlet]="integration" [ngTemplateOutletContext]="microsoftContext"></ng-container>
      </ng-container>
    </mat-radio-group>
  </form>
</div>

<ng-template #integration let-service="service" let-svgIcon="svgIcon" let-label="label" let-connected="connected">
  <div class="vertical-radio-item" (click)="setSelected(service)">
    <integrations-service-connection-status
      *ngIf="connected"
      class="connection"
      [service]="service"
      [useAlternateStyling]="true"
    >
      <integrations-connect-element>
        <div class="container">
          <mat-radio-button [value]="service" [checked]="integrationFormControl.value === service"></mat-radio-button>
          <div class="svg-container">
            <mat-icon [svgIcon]="svgIcon"></mat-icon>
          </div>
          <span>{{ label }}</span>
        </div>
      </integrations-connect-element>
    </integrations-service-connection-status>
    <integrations-service-connection-status
      *ngIf="!connected"
      class="connection"
      [service]="service"
      [useAlternateStyling]="true"
    >
      <integrations-disconnect-element>
        <div class="container">
          <mat-radio-button [value]="service" [checked]="integrationFormControl.value === service"></mat-radio-button>
          <div class="svg-container">
            <mat-icon [svgIcon]="svgIcon"></mat-icon>
          </div>
          <div class="disconnected-container">
            <span>{{ label }}</span>
          </div>
        </div>
      </integrations-disconnect-element>
    </integrations-service-connection-status>
  </div>
</ng-template>
