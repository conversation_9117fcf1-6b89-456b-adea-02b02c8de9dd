@use 'design-tokens' as *;

.integrations {
  margin: 16px auto;

  .container {
    display: flex;
    justify-content: flex-start;
    border-radius: 4px;
    align-items: center;
    padding-left: $gutter-width-dense;
    cursor: pointer;
  }

  .disconnected-container {
    display: flex;
    flex-direction: column;
  }

  .not-connected {
    color: $secondary-font-color;
  }

  button {
    padding: 0;
  }

  .mat-icon {
    height: 24px;
    width: 24px;
    font-size: 23px;
  }

  .svg-container {
    margin-left: 8px;
    margin-right: 8px;
  }

  .zoom-coming-soon-container {
    display: flex;
    justify-content: flex-start;
    height: 60px;
    border: 1px solid $border-color;
    border-radius: 4px;
    align-items: center;
    padding-left: $gutter-width-dense;
  }

  .zoom-coming-soon-text-labels {
    display: flex;
    justify-content: space-between;
    flex-grow: 1;
    margin-right: $gutter-width;
  }

  .zoom-coming-soon {
    color: $secondary-font-color;
    font-size: $font-preset-5-size;
  }
}
span {
  display: inline-flex; // Make sure the span behaves as an inline block for vertical alignment
  align-items: center; // Center text vertically within the span
  line-height: 1.2; // Adjust line-height to vertically align text
  /* or use transform to move text up */
  transform: translateY(-2px); // Adjust the value as needed
}
