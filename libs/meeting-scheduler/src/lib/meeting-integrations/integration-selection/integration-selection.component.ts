import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Inject,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { AvailableServices, MeetingViewModelService } from '@vendasta/integrations';
import { distinctUntilChanged, map, switchMap } from 'rxjs/operators';
import { combineLatest, Observable, of, Subscription } from 'rxjs';
import { FEATURE_MEETING_MICROSOFT_TEAMS_TOKEN } from '../../data-providers/providers';

interface IntegrationContext {
  service: AvailableServices;
  svgIcon: 'meet-icon' | 'zoom-icon' | 'teams-icon';
  label: string;
  connected: boolean;
}

export interface IntegrationServiceSelection {
  service: AvailableServices;
  isConnected: boolean;
}

@Component({
  selector: 'meeting-scheduler-integration-selection',
  templateUrl: './integration-selection.component.html',
  styleUrls: ['./integration-selection.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class IntegrationSelectionComponent implements OnInit, OnDestroy {
  _initialSelection: AvailableServices;

  @Input() set initialSelection(val: AvailableServices | undefined) {
    if (val && this.integrationFormControl && this.integrationFormControl.pristine) {
      this.integrationFormControl.setValue(val);
    }
    this._initialSelection = val;
  }

  @Output() integrationSelected: EventEmitter<IntegrationServiceSelection> = new EventEmitter();
  form: UntypedFormGroup;
  integrationFormControl: UntypedFormControl;
  googleMeetContext$: Observable<IntegrationContext>;
  zoomContext$: Observable<IntegrationContext>;
  microsoftContext$: Observable<IntegrationContext>;

  private readonly subscriptions: Subscription[] = [];

  constructor(
    private readonly fb: UntypedFormBuilder,
    private readonly cd: ChangeDetectorRef,
    private readonly meetingViewModelService: MeetingViewModelService,
    @Inject(FEATURE_MEETING_MICROSOFT_TEAMS_TOKEN)
    readonly featureMicrosoftTeamsEnabled$: Observable<boolean>,
  ) {}

  ngOnInit(): void {
    this.integrationFormControl = this.fb.control(this._initialSelection, [Validators.required, Validators.min(1)]);
    this.form = this.fb.group({
      integration: this.integrationFormControl,
    });

    // These have to be observables because we only want to show the connected / disconnected portion depending on if
    // the service is connected. Otherwise, we have 2 radio buttons for each service and they don't show correctly.
    this.googleMeetContext$ = this.meetingViewModelService.isServiceConnected$(AvailableServices.GOOGLE_MEET).pipe(
      map((connected) => {
        return {
          label: 'Google Meet',
          service: AvailableServices.GOOGLE_MEET,
          svgIcon: 'meet-icon',
          connected,
        };
      }),
    );
    this.zoomContext$ = this.meetingViewModelService.isServiceConnected$(AvailableServices.ZOOM).pipe(
      map((connected) => {
        return {
          label: 'Zoom',
          service: AvailableServices.ZOOM,
          svgIcon: 'zoom-icon',
          connected,
        };
      }),
    );

    this.featureMicrosoftTeamsEnabled$.subscribe((isEnabled) => {
      if (isEnabled) {
        this.microsoftContext$ = this.meetingViewModelService.isServiceConnected$(AvailableServices.MICROSOFT).pipe(
          map((connected) => ({
            label: 'Microsoft Teams',
            service: AvailableServices.MICROSOFT,
            svgIcon: 'teams-icon',
            connected,
          })),
        );
      } else {
        this.microsoftContext$ = of(null);
      }
    });

    this.subscriptions.push(
      this.integrationFormControl.valueChanges
        .pipe(
          switchMap((service) => {
            return combineLatest([
              of(service),
              this.meetingViewModelService.isServiceConnected$(service).pipe(distinctUntilChanged()),
            ]);
          }),
          map(([service, isConnected]) => ({ service, isConnected })),
        )
        .subscribe((selection) => {
          this.cd.markForCheck();
          this.integrationSelected.emit(selection);
        }),
    );
    this.cd.markForCheck();
  }

  setSelected(service: AvailableServices): void {
    this.integrationFormControl.setValue(service);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }
}
