import { Inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { take, tap } from 'rxjs/operators';
import { MeetingSchedulerStoreService } from './data-providers/meeting-scheduler-store.service';
import { MeetingSchedulerServiceModule } from './data-providers/meeting-scheduler-service.module';
import { SETTINGS_LINK_TOKEN } from './data-providers/providers';

@Injectable({
  providedIn: MeetingSchedulerServiceModule,
})
export class ScheduledMeetingsGuard {
  constructor(
    @Inject(SETTINGS_LINK_TOKEN) private readonly settingsLink$: Observable<string>,
    private readonly meetingSchedulerStoreService: MeetingSchedulerStoreService,
    private readonly router: Router,
  ) {}

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> | boolean {
    if (this.meetingSchedulerStoreService.getHasHostEverBeenConfigured()) {
      return true;
    }
    return this.meetingSchedulerStoreService.loadHasHostEverBeenConfigured().pipe(
      tap(async (isConfigured) => {
        if (!isConfigured) {
          const settingsLink = await this.settingsLink$.pipe(take(1)).toPromise();
          await this.router.navigate([settingsLink]);
        }
      }),
    );
  }
}
