import { HttpErrorResponse } from '@angular/common/http';
import { Inject, Injectable, OnDestroy, signal, WritableSignal } from '@angular/core';
import {
  ApplicationContextPropertiesSet,
  Calendar,
  CalendarType,
  Contact,
  durationStringToMinutes,
  Group,
  GuestService,
  HostService,
  HostUser,
  ListBookedMeetingsRequestFilters,
  Meeting,
  MeetingType,
  Preferences,
  Service,
  TimeOfDay,
  TimeSpan,
  TimeZone,
  WeekdayAvailability,
  WellKnownMeetingMetadataKeys,
} from '@vendasta/meetings';
import { retryer } from '@vendasta/rx-utils';
import isEqual from 'lodash/isEqual';
import mergeWith from 'lodash/mergeWith';
import { BehaviorSubject, combineLatest, forkJoin, Observable, of, Subscription, throwError } from 'rxjs';
import {
  catchError,
  distinctUntilChanged,
  filter,
  first,
  map,
  shareReplay,
  startWith,
  switchMap,
  switchMapTo,
  tap,
  withLatestFrom,
} from 'rxjs/operators';
import { DailyAvailability, TimeRange } from '../interface';
import { MeetingSchedulerInitializerService } from './initializer.service';
import { MeetingSchedulerServiceModule } from './meeting-scheduler-service.module';
import { HOST_ID_TOKEN, USER_ID_TOKEN } from './providers';
import { getCallRetryConfig, meetingSchedulerInitializationRetryConfig } from './retry';
import moment from 'moment-timezone';
import { TimeRange as TimeSlot } from '@vendasta/meetings/lib';
import { BOOOKING_URL_DEMO, BOOOKING_URL_PROD, EVENTS, GRID_VIEW, LIST_VIEW } from '../constants';
import { GetGroupResponse, GetServiceResponse } from '@vendasta/meetings/lib/_internal';
import { EntityAssociations } from '@vendasta/meetings/lib/shared/groups-and-services';
import { Environment, EnvironmentService } from '@galaxy/core';
import { getDurationValue } from './duration-conversion-helper';
import { AvailabilityRuleList } from '@vendasta/meetings/lib/_internal/objects/meeting-host';

/**
 * This service is responsible for state management in Meeting Scheduler
 */

export interface State {
  // Determines whether the host has any availability
  isHostAvailable: boolean;

  // Determines whether we've ever asked the server to check if the host is available.
  hasLoadedIsHostConfigured: boolean;

  // Determined whether the host has ever configured Meeting Scheduler
  hasHostEverBeenConfigured: boolean;

  user: {
    // userId answers the question "who am I?" and applicationContext answers "what is my context?"
    // Context being where the user is accessing Meeting Scheduler from (sales center, business app, task manager, etc).
    id: string;
    applicationContext: ApplicationContextPropertiesSet;
    availability: DailyAvailability[];
    preferences: Preferences;

    // hostId is a deprecated identifier that's now mostly covered by userId and applicationContext, we still need it for some other RPCs though
    hostId: string;
  };

  calendars: {
    [calendarId: string]: {
      calendar: Calendar;
      meetingTypes: {
        [meetingTypeId: string]: MeetingType;
      };

      // Whether or not meeting types have been loaded. If this is false, it means we don't know if there are meetings or not (show loading state)
      hasLoadedMeetingTypes: boolean;

      meetings: Meeting[];

      // Whether or not meetings has been loaded. If this is false, it means we don't know if there are meetings or not (show loading state)
      hasLoadedMeetings: boolean;

      hostUsers: {
        [userId: string]: HostUser;
      };

      hostAvailability: {
        [userId: string]: AvailabilityRuleList;
      };
    };
  };

  //  Whether the groupCalendar already triggered GetCalendar api to get the groupCalendar data
  loadedGroupCalendarData: {
    [calendarId: string]: {
      calendar: Calendar;
    };
  };
}

function getStartingState(): State {
  return {
    calendars: {},
    loadedGroupCalendarData: {},
    hasHostEverBeenConfigured: false,
    hasLoadedIsHostConfigured: false,
    isHostAvailable: false,
    user: { applicationContext: undefined, availability: [], hostId: '', id: '', preferences: undefined },
  };
}

export type GroupMeetingType = MeetingType & { calendarName: string };

export interface HostsForCalendarResponse {
  hostUsers: HostUser[];
  hostAvailability: Map<string, AvailabilityRuleList>;
}

export interface HostsForCalendarApiResponse {
  hostUsers: HostUser[];
  hostUserAvailabilityRule?: { [userId: string]: AvailabilityRuleList };
}

export function isGroupMeetingType(mt: MeetingType | GroupMeetingType): mt is GroupMeetingType {
  return Boolean((mt as GroupMeetingType).calendarName);
}

@Injectable({
  providedIn: MeetingSchedulerServiceModule,
}) /* on destroy called on services: https://angular.io/api/core/OnDestroy */
export class MeetingSchedulerStoreService implements OnDestroy {
  public tableViewEnabled = false;
  public selectedToggle = EVENTS;
  public readonly displayOption: WritableSignal<typeof LIST_VIEW | typeof GRID_VIEW> = signal(LIST_VIEW);
  public pageSizeOptions = [5, 10, 15];
  public pageSize = 10;
  public selectedTabEventTypeListView = 0; // Available Tabs - [Event, Group, Service, Default]

  public readonly personalCalendarId$: Observable<string>;

  private state$$ = new BehaviorSubject<State>(getStartingState());
  private subscriptions: Subscription[] = [];
  private initialized = false;

  private readonly hostId$: Observable<string>;
  private readonly preferences$: Observable<Preferences>;
  private readonly personalCalendar$: Observable<Calendar>;
  private readonly groupCalendarIds$: Observable<string[]>;
  private readonly personalMeetings$: Observable<Meeting[]>;
  private readonly personalMeetingTypes$: Observable<MeetingType[]>;
  private personalCalendarId: string;

  dataSubject$$ = new BehaviorSubject<any>(null); // Initialize with a default value
  data$: Observable<any> = this.dataSubject$$.asObservable();

  get Schedule$(): Observable<any> {
    return this.dataSubject$$.asObservable();
  }

  updateSchedule(changes: any): void {
    this.dataSubject$$.next(changes);
  }

  constructor(
    private readonly hostService: HostService,
    private readonly guestService: GuestService,
    @Inject(HOST_ID_TOKEN) private readonly _hostId$: Observable<string>,
    @Inject(USER_ID_TOKEN) private readonly _userId$: Observable<string>,
    private readonly initializationService: MeetingSchedulerInitializerService,
    @Inject(EnvironmentService)
    private readonly environmentService,
  ) {
    this.preferences$ = this.state$$.pipe(
      map((s) => {
        // the slug preference was migrated to be on the personal cal
        const userPreferences = s?.user?.preferences || ({} as Preferences);
        const personalCalendarSlug = getPersonalCalendar(s)?.slug;
        return { ...userPreferences, calendarSlug: personalCalendarSlug };
      }),
      filter<Preferences>(Boolean),
    );
    this.hostId$ = this.state$$.pipe(
      map((s) => s?.user?.hostId),
      filter<string>(Boolean),
      distinctUntilChanged(),
    );
    this.personalCalendar$ = this.state$$.pipe(
      map((s) => getPersonalCalendar(s)),
      filter<Calendar>(Boolean),
      distinctUntilChanged(isEqual),
    );
    this.personalCalendarId$ = this.personalCalendar$.pipe(
      map((calendar) => calendar.id),
      distinctUntilChanged(),
    );
    this.personalMeetings$ = combineLatest([this.state$$, this.personalCalendarId$]).pipe(
      map(([state, personalCalendarId]) =>
        Object.values(state?.calendars?.[personalCalendarId]?.meetings || []).sort((a, b) => this.sortMeetings(a, b)),
      ),
    );
    this.personalMeetingTypes$ = combineLatest([this.state$$, this.personalCalendarId$]).pipe(
      map(([state, personalCalendarId]) =>
        Object.values(state?.calendars?.[personalCalendarId]?.meetingTypes || []).sort(
          (m1: MeetingType, m2: MeetingType) => m1.name.localeCompare(m2.name),
        ),
      ),
    );
    this.groupCalendarIds$ = this.state$$.pipe(
      map((state) => {
        const groupCalendarContainers = Object.values(state.calendars || {}).filter(
          (calendar) => calendar?.calendar?.calendarType === CalendarType.CALENDAR_TYPE_GROUP,
        );
        const groupCalendarIds = groupCalendarContainers.map((calendar) => calendar.calendar.id);
        return groupCalendarIds.sort();
      }),
      distinctUntilChanged(isEqual),
    );
    if (this.initialized) {
      return;
    }
    this.setup();
    this.initialized = true;
  }
  private dateToTimeOfDay(date: Date): TimeOfDay {
    return {
      hours: date.getHours(),
      minutes: date.getMinutes(),
      seconds: date.getSeconds(),
      nanos: date.getMilliseconds() * 1000000,
    } as TimeOfDay;
  }
  private timeRangeToTimeSlot(range: TimeRange): TimeSlot {
    return { from: this.dateToTimeOfDay(range.start), to: this.dateToTimeOfDay(range.end) };
  }
  sortMeetings(a: Meeting, b: Meeting): number {
    return a.start.getTime() - b.start.getTime();
  }

  loadPersonalCalendar(): Observable<Calendar> {
    return this.personalCalendar$;
  }

  loadGroupCalendars(): Observable<Calendar[]> {
    return this.state$$.pipe(
      map((s) => {
        const calendarList = Object.values(s.calendars || {}).map((calendar) => calendar.calendar);
        const groupCalendars = calendarList.filter(
          (calendar) => calendar.calendarType === CalendarType.CALENDAR_TYPE_GROUP,
        );
        return groupCalendars.sort((calendar1, calendar2) =>
          calendar1.displayName.localeCompare(calendar2.displayName),
        );
      }),
    );
  }

  private calendarLoadErrors = new BehaviorSubject<Set<string>>(new Set());

  getCalendarLoadErrors(): Observable<Set<string>> {
    return this.calendarLoadErrors.asObservable();
  }

  loadCalendar(calendarId: string): Observable<Calendar | null> {
    const calendar$ = this.state$$.pipe(
      map((s) => s?.calendars?.[calendarId]?.calendar),
      filter<Calendar>(Boolean),
    );

    const response$ = this.hostService.getCalendar({ calendarId }).pipe(
      tap((calendar) => {
        this.applyState({ calendars: { [calendar.id]: { calendar } } });
        this.applyState({ loadedGroupCalendarData: { [calendar.id]: { calendar } } });
      }),
      catchError((err) => {
        if (err?.status === 403 || err?.message?.includes('permission denied')) {
          const errorSet = new Set(this.calendarLoadErrors.getValue());
          errorSet.add(calendarId);
          this.calendarLoadErrors.next(errorSet);
        }
        return of(null); // Fallback so stream continues not affect any exsisting flow
      }),
      switchMapTo(calendar$),
      shareReplay(1),
    );

    const cached = this.state$$.getValue()?.calendars?.[calendarId]?.calendar;
    return cached ? response$.pipe(startWith(cached)) : response$;
  }

  getGroupCalendarDataFromState(calendarId: string): boolean {
    if (this.state$$.getValue()?.loadedGroupCalendarData?.[calendarId]?.calendar) {
      return true;
    }
    return false;
  }

  loadHostsForCalendar(calendarId: string): Observable<HostsForCalendarResponse> {
    const result$ = this.hostService.getHostsForCalendar({ calendarId }).pipe(
      tap((response: any) => {
        // Handle new response structure with hostUsers and availabilityRules
        const hostUsers = response?.hostUsers || response || [];
        const hostUserMap = (hostUsers || []).reduce((m, hostUser) => {
          return {
            ...m,
            [hostUser.userId]: hostUser,
          };
        }, {});

        // Cache host availability rules in state - support both property names
        const availabilityRules = response?.hostUserAvailabilityRule || response?.availabilityRules || {};
        const hostAvailabilityMap = { ...availabilityRules };

        this.applyState({
          calendars: {
            [calendarId]: {
              hostUsers: hostUserMap,
              hostAvailability: hostAvailabilityMap,
            },
          },
        });
      }),
      map((response: any) => {
        // Extract hostUsers and availability rules from response
        const hostUsers = response?.hostUsers || response || [];
        const availabilityRules = response?.hostUserAvailabilityRule || response?.availabilityRules || {};

        // Convert availabilityRules object to Map
        const hostAvailability = new Map<string, AvailabilityRuleList>();
        if (availabilityRules && typeof availabilityRules === 'object') {
          Object.keys(availabilityRules).forEach((key) => {
            hostAvailability.set(key, availabilityRules[key]);
          });
        }

        return {
          hostUsers: hostUsers,
          hostAvailability: hostAvailability,
        } as HostsForCalendarResponse;
      }),
      retryer(getCallRetryConfig),
      shareReplay(1),
    );

    // Check if we have cached data for both hostUsers and hostAvailability
    const calendarData = this.state$$.getValue()?.calendars?.[calendarId];
    if (calendarData?.hostUsers && calendarData?.hostAvailability) {
      const cachedHostUsers = this.getHostUserArray(calendarId, this.state$$.getValue());
      const cachedHostAvailability = this.getHostAvailability(calendarId, this.state$$.getValue());
      const cachedResponse: HostsForCalendarResponse = {
        hostUsers: cachedHostUsers,
        hostAvailability: cachedHostAvailability,
      };
      return result$.pipe(startWith(cachedResponse));
    }
    return result$;
  }

  loadPersonalGeneralBookingLink(): Observable<string> {
    return this.personalCalendar$.pipe(
      switchMap((calendar) => this.hostService.getGeneralBookingUrl({ calendarId: calendar.slug, metadata: {} })),
      retryer(getCallRetryConfig),
      distinctUntilChanged(),
      shareReplay(1),
    );
  }

  loadBookingDomain(): Observable<string> {
    return this.personalCalendar$.pipe(
      map((calendar) => {
        const parts = calendar.bookingUrl.replace('https://', '').replace('http://', '').split('/');
        return parts.length > 0 ? parts[0] : '';
      }),
      distinctUntilChanged(),
      shareReplay(1),
    );
  }

  loadGroupGeneralBookingLinks(): Observable<{ [calendarId: string]: string }> {
    return this.groupCalendarIds$.pipe(
      switchMap((groupCalendarIds) => {
        if (!groupCalendarIds || groupCalendarIds.length === 0) {
          return of({});
        }
        return forkJoin(
          (groupCalendarIds || []).map((calendarId) => {
            return this.hostService.getGeneralBookingUrl({ calendarId }).pipe(
              map((bookingUrl) => {
                return {
                  calendarId,
                  bookingUrl: bookingUrl,
                };
              }),
              retryer(getCallRetryConfig),
            );
          }),
        );
      }),
      map((calendarUrlContainers: { calendarId: string; bookingUrl: string }[]) =>
        calendarUrlContainers.reduce((acc, curr) => {
          return {
            ...acc,
            [curr.calendarId]: curr.bookingUrl,
          };
        }, {}),
      ),
      shareReplay(1),
    );
  }

  isCalendarSlugTaken(slug: string): Observable<boolean> {
    // If the current calendar slug is equal return false. The host took the slug.
    return this.personalCalendar$.pipe(
      switchMap((calendar) => {
        if (slug === calendar.slug) {
          return of(false);
        }
        return this.hostService.doesCalendarExist(slug);
      }),
      first(),
    );
  }

  async setPersonalCalendarSlug(slug: string): Promise<void> {
    const calendarId = await this.personalCalendarId$.pipe(first()).toPromise();
    return this.setCalendarSlug(calendarId, slug);
  }

  async setCalendarSlug(calendarId: string, slug: string): Promise<void> {
    await this.hostService.updateCalendarSlug(calendarId, slug).pipe(first()).toPromise();
    const existingCalendar = this.state$$.getValue().calendars[calendarId]?.calendar;

    if (!existingCalendar) {
      console.error(`Calendar with ID ${calendarId} not found.`);
      return;
    }

    const updatedBookingUrl = existingCalendar.bookingUrl?.includes(existingCalendar.slug)
      ? existingCalendar.bookingUrl.replace(existingCalendar.slug, slug)
      : existingCalendar.bookingUrl;

    this.applyState({
      calendars: {
        [calendarId]: {
          calendar: {
            slug: slug,
            bookingUrl: updatedBookingUrl,
          },
        },
      },
    });
  }

  getUserId(): string {
    return this.state$$.getValue()?.user?.id;
  }

  loadUserPreferences(): Observable<Preferences> {
    const hostPreferences$ = this.hostId$.pipe(
      switchMap((hostId) => this.hostService.getPreferences({ hostId })),
      retryer(getCallRetryConfig),
      tap((preferences) => this.applyState({ user: { preferences } })),
      switchMapTo(this.preferences$),
      shareReplay(1),
    );

    // If we have already loaded hostPreferences, return from cache
    const cachedPreferences = this.state$$.getValue()?.user?.preferences;
    if (cachedPreferences) {
      return hostPreferences$.pipe(startWith(cachedPreferences));
    }
    return hostPreferences$;
  }

  async updateUserPreferences(preferences: Partial<Preferences>): Promise<void> {
    const hostId = await this.getHostId();
    await this.hostService.updatePreferences({ hostId, preferences }).pipe(first()).toPromise();
    this.applyState({ user: { preferences } });
  }

  loadPersonalMeetings(req: {
    filters: Pick<ListBookedMeetingsRequestFilters, 'timeSpan' | 'meetingTypeIds'>;
    timeZone: TimeZone;
    pageSize: number;
  }): Observable<Meeting[]> {
    let result$ = this.hostId$.pipe(
      switchMap((hostId) => {
        return this.hostService.listBookedMeetings({
          filters: { ...req.filters, hostId },
          timeZone: req.timeZone,
          pageSize: req.pageSize,
          cursor: '',
        });
      }),
      retryer(getCallRetryConfig),
      withLatestFrom(this.personalCalendarId$),
      tap(([resp, personalCalendarId]) => {
        this.applyState({ calendars: { [personalCalendarId]: { meetings: resp?.results, hasLoadedMeetings: true } } });
      }),
      switchMapTo(this.personalMeetings$),
      shareReplay(1),
    );
    const personalCalendar = this.state$$?.getValue()?.calendars?.[this.personalCalendarId];
    if (personalCalendar?.hasLoadedMeetings && personalCalendar?.meetings) {
      result$ = result$.pipe(
        startWith(Object.values(personalCalendar.meetings).sort((a, b) => this.sortMeetings(a, b))),
      );
    }
    return result$;
  }

  // returns whether the host can be scheduled
  loadIsHostAvailable(req?: { skipCache: boolean }): Observable<boolean> {
    const isHostAvail$ = this.hostId$.pipe(
      switchMap((hostId: string) =>
        this.hostService.isHostConfigured({ hostId: hostId }).pipe(
          catchError((err: HttpErrorResponse) => {
            if (err?.status === 404) {
              return of(false);
            }
            return throwError(err);
          }),
        ),
      ),
      retryer(getCallRetryConfig),
      tap((isHostAvailable) => this.applyState({ isHostAvailable, hasLoadedIsHostConfigured: true })),
      switchMapTo(this.state$$.pipe(map((state) => state.isHostAvailable))),
      shareReplay(1),
    );

    // Only expose cache when it has already been set
    const skipCache = req && req.skipCache;
    if (this.state$$.getValue().hasLoadedIsHostConfigured && !skipCache) {
      return isHostAvail$.pipe(startWith(this.state$$.getValue().isHostAvailable));
    }

    return isHostAvail$;
  }

  getHasHostEverBeenConfigured(): boolean {
    return this.state$$.getValue().hasHostEverBeenConfigured;
  }

  // TODO: Need to refactor - need a better way of determining if setup was complete
  // Returns if the host has EVER setup Meeting Scheduler, even if they cannot be scheduled right now
  loadHasHostEverBeenConfigured(req?: { skipCache: boolean }): Observable<boolean> {
    if (!req?.skipCache && this.state$$.getValue().hasLoadedIsHostConfigured) {
      return this.state$$.pipe(map((s) => s.hasHostEverBeenConfigured));
    }
    return combineLatest([this.loadIsHostAvailable(req), this.personalCalendar$]).pipe(
      map(([isHostAvailable, personalCalendar]) => {
        return isHostAvailable || personalCalendar.slug !== personalCalendar.id;
      }),
      tap((hasHostEverBeenConfigured: boolean) => {
        // If block protects from infinite loop (because we change state)
        if (
          this.state$$.getValue().hasHostEverBeenConfigured !== hasHostEverBeenConfigured ||
          !this.state$$.getValue().hasLoadedIsHostConfigured
        ) {
          this.applyState({ hasHostEverBeenConfigured, hasLoadedIsHostConfigured: true });
        }
      }),
      switchMapTo(this.state$$.pipe(map((s) => s.hasHostEverBeenConfigured))),
      distinctUntilChanged(),
      shareReplay(1),
    );
  }

  async cancelMeeting(req: { meetingId: string; cancellationReason: string }): Promise<void> {
    await this.hostService
      .cancelMeeting({
        meetingId: req.meetingId,
        cancellationReason: req.cancellationReason,
      })
      .pipe(first())
      .toPromise();
    const calendar = Object.values(this.state$$.getValue().calendars || {}).find((cal) =>
      Boolean(cal.meetings?.find((meeting) => meeting.id === req.meetingId)),
    );
    this.applyMeetingUpdate(calendar?.calendar?.id, req.meetingId, undefined);
  }

  async rescheduleMeeting(req: { meetingId: string; start: Date }): Promise<void> {
    await this.hostService
      .rescheduleMeeting({
        meetingId: req.meetingId,
        start: req.start,
      })
      .pipe(first())
      .toPromise();
    const calendar = Object.values(this.state$$.getValue().calendars || {}).find((cal) =>
      Boolean(cal.meetings?.find((meeting) => meeting.id === req.meetingId)),
    );
    const meetingTypeId = calendar.meetings.find((meeting) => meeting.id === req.meetingId)?.eventTypeId;
    const meetingType = calendar.meetingTypes[meetingTypeId];
    let duration: number;
    if (meetingType) {
      duration = durationStringToMinutes({ duration: meetingType.duration });
    } else if (meetingTypeId.indexOf('-minutes') >= 0) {
      // Special cases where meetingTypeId is just a duration in minutes postfixed by -minutes (15-minutes)
      duration = parseInt(meetingTypeId, 10);
    } else {
      console.error(`could not determine duration for meeting type ${meetingTypeId}`);
      return;
    }
    const endDate = new Date(req.start);
    endDate.setMinutes(endDate.getMinutes() + duration);
    this.applyMeetingUpdate(calendar.calendar.id, req.meetingId, { end: endDate, start: req.start });
  }

  linkBusinessToMeeting(req: { meetingId: string; businessId: string }): Observable<void> {
    return this.hostService
      .updateMeetingMetadata({
        meetingId: req.meetingId,
        metadata: { [WellKnownMeetingMetadataKeys.BUSINESS]: req.businessId },
      })
      .pipe(
        tap(() => {
          const calendars = Object.values(this.state$$.getValue().calendars || {});
          // Find a calendar that has a meeting with the given meeting id
          const calendar = calendars.find((cal) => cal?.meetings?.find((meeting) => meeting.id === req.meetingId));
          this.applyMeetingUpdate(calendar.calendar.id, req.meetingId, {
            metadata: { [WellKnownMeetingMetadataKeys.BUSINESS]: req.businessId },
          });
        }),
      );
  }

  async unlinkBusinessFromMeeting(req: { meetingId: string }): Promise<void> {
    await this.hostService
      .updateMeetingMetadata({
        meetingId: req.meetingId,
        metadata: { [WellKnownMeetingMetadataKeys.BUSINESS]: '' },
      })
      .pipe(first())
      .toPromise();
    const calendars = Object.values(this.state$$.getValue().calendars || {});
    // Find a calendar that has a meeting with the given meeting id
    const calendar = calendars.find((cal) => cal?.meetings?.find((meeting) => meeting.id === req.meetingId));
    this.applyMeetingUpdate(calendar.calendar.id, req.meetingId, {
      metadata: { [WellKnownMeetingMetadataKeys.BUSINESS]: '' },
    });
  }

  loadMeetingTypes(req: {
    calendarId: string;
    calendarSlug?: string;
    returnCache?: boolean;
  }): Observable<MeetingType[]> {
    const response$ = this.hostService
      .listMeetingTypes({
        hostId: req.calendarId,
        calendarSlug: req.calendarSlug || req.calendarId,
      })
      .pipe(
        retryer(getCallRetryConfig),
        tap((meetingTypes) => {
          this.applyState({
            calendars: {
              [req.calendarId]: {
                hasLoadedMeetingTypes: true,
                meetingTypes: meetingTypes.reduce((meetingTypeMap, meetingType) => {
                  return { ...meetingTypeMap, [meetingType.id]: meetingType };
                }, {}),
              },
            },
          });
        }),
        switchMapTo(
          this.state$$.pipe(map((state) => Object.values(state?.calendars?.[req.calendarId]?.meetingTypes || {}))),
        ),
        shareReplay(1),
      );

    if (req.returnCache && this.state$$.getValue()?.calendars?.[req.calendarId]?.hasLoadedMeetingTypes) {
      const cached = Object.values(this.state$$.getValue()?.calendars?.[req.calendarId]?.meetingTypes || {});
      return response$.pipe(startWith(cached));
    }

    return response$.pipe(map((mt) => mt || []));
  }

  loadPersonalMeetingTypes(req?: { returnCache?: boolean }): Observable<MeetingType[]> {
    const idWithSlug$ = this.personalCalendar$.pipe(
      map((calendar) => ({ slug: calendar.slug, id: calendar.id })),
      distinctUntilChanged(isEqual),
    );
    return idWithSlug$.pipe(
      switchMap((idWithSlug) => {
        return this.loadMeetingTypes({
          calendarId: idWithSlug.id,
          calendarSlug: idWithSlug.slug,
          returnCache: req?.returnCache || false,
        });
      }),
      shareReplay(1),
    );
  }

  loadGroupMeetingTypes(req?: { returnCache?: boolean }): Observable<{ [calendarId: string]: GroupMeetingType[] }> {
    const response$ = this.groupCalendarIds$.pipe(
      switchMap((groupCalendarIds) => {
        if (!groupCalendarIds || groupCalendarIds.length === 0) {
          return of([]);
        }
        return this.hostService.getMeetingTypesForCalendars({ calendarIds: groupCalendarIds, metadata: {} });
      }),
      retryer(meetingSchedulerInitializationRetryConfig),
      tap((calendarIdToMeetingTypeMap: { [calendarId: string]: MeetingType[] }) => {
        const stateUpdate: {
          calendars: { [calendarId: string]: { meetingTypes: { [meetingTypeId: string]: MeetingType } } };
        } = { calendars: {} };
        Object.keys(calendarIdToMeetingTypeMap).forEach((calendarId) => {
          const meetingTypeMap = (calendarIdToMeetingTypeMap[calendarId] || []).reduce((m, meetingType) => {
            return {
              ...m,
              [meetingType.id]: meetingType,
            };
          }, {});
          stateUpdate.calendars[calendarId] = { meetingTypes: meetingTypeMap };
        });
        this.applyState(stateUpdate);
      }),
      switchMapTo(this.state$$.pipe(map((state) => selectGroupMetingTypes(state)))),
      shareReplay(1),
    );
    if (req?.returnCache) {
      return response$.pipe(startWith(selectGroupMetingTypes(this.state$$.getValue())));
    }
    return response$;
  }

  markHostAsConfigured(): void {
    this.state$$.next({ ...this.state$$.getValue(), hasHostEverBeenConfigured: true });
  }

  async createMeetingType(meetingType: MeetingType): Promise<string> {
    const id = await this.hostService
      .createMeetingType({
        meetingType: meetingType,
      })
      .pipe(first())
      .toPromise();
    this.applyMeetingTypeUpdate(meetingType.calendarId, id, meetingType);
    return id;
  }

  async updateMeetingType(id: string, meetingType: MeetingType, updateFields?: (keyof MeetingType)[]): Promise<void> {
    await this.hostService
      .updateMeetingType({
        id: id,
        meetingType: meetingType,
        updateFields: updateFields,
      })
      .pipe(first())
      .toPromise();
    this.applyMeetingTypeUpdate(meetingType.calendarId, id, meetingType);
  }

  async deleteMeetingType(id: string): Promise<any> {
    await this.hostService.deleteMeetingType({ id: id }).pipe(first()).toPromise();
    const calendar = Object.values(this.state$$.getValue().calendars || {}).find((cal) =>
      Boolean(cal?.meetingTypes?.[id]),
    );
    this.applyMeetingTypeUpdate(calendar?.calendar?.id, id, undefined);
  }

  async deleteGroup(id: string): Promise<any> {
    await this.hostId$
      .pipe(
        switchMap((hostId) => {
          return this.hostService.deleteGroup({
            id: id,
            hostId: hostId,
          });
        }),
        catchError((err) => {
          console.error('Error in delete group:', err);
          throw err;
        }),
      )
      .pipe(first())
      .toPromise();
  }

  async deleteService(id: string): Promise<any> {
    await this.hostId$
      .pipe(
        switchMap((hostId) => {
          return this.hostService.deleteService({
            id: id,
            hostId: hostId,
          });
        }),
        catchError((err) => {
          console.error('Error in delete service:', err);
          throw err;
        }),
      )
      .pipe(first())
      .toPromise();
  }

  setAvailability(
    availability: DailyAvailability,
    timezone?: string,
    meetingId?: string,
    hostId?: string,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const weekdaysAvailability: WeekdayAvailability[] = [];
      availability.days.forEach((ranges, day) => {
        weekdaysAvailability.push(
          new WeekdayAvailability({
            day,
            timeSlots: ranges.map((range) => this.timeRangeToTimeSlot(range)),
          }),
        );
      });

      const request = {
        hostId,
        meetingTypeId: meetingId,
        weekdaysAvailability: weekdaysAvailability,
        timeZone: { id: timezone || moment.tz.guess() },
      };

      this.hostService.setGeneralAvailability(request).subscribe(
        () => resolve(),
        () => reject(),
      );
    });
  }

  async createGroup(group: Group, hostId: string): Promise<string> {
    const id = await this.hostService
      .createGroup({
        group: group,
        HostId: hostId,
      })
      .pipe(first())
      .toPromise();
    return id;
  }

  async createService(service: Service, hostId: string): Promise<string> {
    const id = await this.hostService
      .createService({
        service,
        HostId: hostId,
      })
      .pipe(first())
      .toPromise();
    return id;
  }

  async updateService(id: string, serviceData: Service, updateFields?: (keyof Service)[]): Promise<void> {
    await this.hostId$
      .pipe(
        switchMap((hostId) => {
          return this.hostService.updateService({
            id: id,
            service: serviceData,
            updateFields: updateFields,
            HostId: hostId,
          });
        }),
        catchError((err) => {
          console.error('Error in update Service:', err);
          throw err;
        }),
      )
      .pipe(first())
      .toPromise();
  }

  loadServices() {
    return this.hostId$.pipe(
      switchMap((hostId) => {
        return this.hostService.listServices({ hostId });
      }),
    );
  }

  getGroup(groupId: string, slug: string): Observable<Group | GetGroupResponse> {
    return this.hostId$
      .pipe(
        switchMap((hostId) => {
          return this.hostService.getGroup({ groupId, slug, hostId });
        }),
      )
      .pipe(first());
  }

  getService(serviceId: string, slug: string): Observable<Service | GetServiceResponse> {
    return this.hostId$
      .pipe(
        switchMap((hostId) => {
          return this.hostService.getService({ serviceId, slug, hostId });
        }),
      )
      .pipe(first());
  }

  checkGroupOrServiceSlugExist(slugIdentifier: string, category: string): Observable<boolean> {
    return this.hostId$.pipe(
      switchMap((hostId) => this.hostService.checkGroupOrServiceSlugExist({ hostId, slugIdentifier, category })),
    );
  }

  private setup(): void {
    this.subscriptions.push(
      // TODO: Add an error message indicating what about initialization failed.
      this._hostId$.pipe(distinctUntilChanged()).subscribe((hostId) => this.applyState({ user: { hostId } })),
      this._userId$.pipe(distinctUntilChanged()).subscribe((id) => {
        this.applyState({ user: { id } });
      }),
      this.initializationService.personalCalendar$.pipe(distinctUntilChanged(isEqual)).subscribe((calendar) =>
        this.applyState({
          calendars: {
            [calendar.id]: {
              calendar: { ...calendar, calendarType: CalendarType.CALENDAR_TYPE_PERSONAL },
            },
          },
        }),
      ),
      this.initializationService.groupCalendars$.pipe(distinctUntilChanged(isEqual)).subscribe((groupCalendars) => {
        const calendars = groupCalendars.reduce((calendarMap, calendar) => {
          return {
            ...calendarMap,
            [calendar.id]: {
              calendar: {
                id: calendar.id,
                externalId: calendar.externalId,
                displayName: calendar.displayName,
                calendarType: CalendarType.CALENDAR_TYPE_GROUP,
                ...calendar,
              },
            },
          };
        }, {});
        this.applyState({ calendars });
      }),
      this.initializationService.meetingTypeMap$.pipe(distinctUntilChanged(isEqual)).subscribe((meetingTypeMap) => {
        Object.entries(meetingTypeMap || {}).forEach(([calendarId, meetingTypes]) => {
          const newMeetingTypeMap = (meetingTypes as MeetingType[]).reduce((m, meetingType) => {
            return {
              ...m,
              [meetingType.id]: meetingType,
            };
          }, {});
          this.applyState({
            calendars: {
              [calendarId]: {
                meetingTypes: newMeetingTypeMap,
                hasLoadedMeetingTypes: true,
              },
            },
          });
        });
      }),
    );
  }

  private getHostUserArray(calendarId: string, s: State): HostUser[] {
    const hostUserList = Object.values(s?.calendars?.[calendarId]?.hostUsers || {});
    return hostUserList.sort((h1, h2) => h1.displayName.localeCompare(h2.displayName));
  }

  private getHostAvailability(calendarId: string, s: State): Map<string, AvailabilityRuleList> {
    const hostAvailabilityObj = s?.calendars?.[calendarId]?.hostAvailability || {};
    const hostAvailabilityMap = new Map<string, AvailabilityRuleList>();

    // Convert object to Map
    Object.keys(hostAvailabilityObj).forEach((key) => {
      hostAvailabilityMap.set(key, hostAvailabilityObj[key]);
    });

    return hostAvailabilityMap;
  }

  // Passing undefined will delete the meeting
  private applyMeetingUpdate(calendarId: string, id: string, meeting: Partial<Meeting> | undefined): void {
    const meetings = this.state$$.getValue()?.calendars?.[calendarId]?.meetings || [];
    const index = meetings.findIndex((m) => m.id === id);
    if (!meeting) {
      meetings.splice(index, 1);
    } else {
      meetings[index] = { ...meetings[index], ...meeting, id } as Meeting;
    }
    this.applyState({ calendars: { [calendarId]: { meetings } } });
  }

  // Passing undefined will delete the meetingType
  private applyMeetingTypeUpdate(calendarId: string, id: string, meetingType: Partial<MeetingType> | undefined): void {
    let meetingTypes = this.state$$.getValue()?.calendars?.[calendarId]?.meetingTypes || {};
    if (!meetingType) {
      delete meetingTypes[id];
    } else {
      const calendarSlug = this.state$$.getValue()?.calendars?.[calendarId].calendar.slug;
      const bookingUrl = `${this.getHostUrl()}/book/${calendarSlug}/${meetingType.meetingTypeSlug || ''}`;
      const durationValue = getDurationValue(meetingType.duration);
      const duration = durationValue !== undefined ? `${durationValue}s` : '0s';
      let teamName = '';
      if (this.state$$.getValue()?.calendars?.[calendarId].calendar.calendarType === CalendarType.CALENDAR_TYPE_GROUP) {
        teamName = this.state$$.getValue()?.calendars?.[calendarId].calendar.displayName;
      }

      meetingTypes = {
        ...meetingTypes,
        [id]: { ...meetingTypes[id], ...meetingType, id, bookingUrl, duration, teamName },
      };
    }
    this.applyState({ calendars: { [calendarId]: { meetingTypes } } }, true);
  }

  //to get hostUrl to generate bookingLink
  getHostUrl(): string {
    const env = this.environmentService.getEnvironment();
    const isProd = env === Environment.PROD;
    return isProd ? BOOOKING_URL_PROD : BOOOKING_URL_DEMO;
  }

  // apply state only overriding the properties given in newState, old values remain unchanged.
  private applyState(newState: DeepPartial<State>, overrideEmptyValue?: boolean): void {
    // Merge the states, but override the default behaviour for array: override the entire array.
    // Default behaviour treats the array like an object and does not merge the way we want.
    // https://lodash.com/docs/4.17.15#mergeWith
    this.state$$.next(
      mergeWith({}, this.state$$.getValue(), newState, (objValue: any, srcValue: any) => {
        if (Array.isArray(objValue) || Array.isArray(srcValue)) {
          return srcValue;
        }
        if (typeof objValue === 'string' && typeof srcValue === 'string') {
          if (overrideEmptyValue) {
            return srcValue !== undefined ? srcValue : objValue; // Ensure empty strings are updated
          } else {
            return srcValue || objValue; // prefer non-empty strings
          }
        }
        // Handle primitive values (like booleans or numbers)
        if (typeof srcValue !== 'object' || srcValue === null) {
          return srcValue || objValue;
        }
      }),
    );
  }

  private async getHostId(): Promise<string> {
    return this.hostId$.pipe(first()).toPromise();
  }

  loadGroups() {
    return this.hostId$.pipe(
      switchMap((hostId) => {
        return this.hostService.listGroups({ hostId });
      }),
    );
  }

  async updateGroup(id: string, groupData: Group, updateFields?: (keyof Group)[]): Promise<void> {
    await this.hostId$
      .pipe(
        switchMap((hostId) => {
          return this.hostService.updateGroup({
            id: id,
            group: groupData,
            updateFields: updateFields,
            HostId: hostId,
          });
        }),
        catchError((err) => {
          console.error('Error in updateGroup:', err);
          throw err;
        }),
      )
      .pipe(first())
      .toPromise();
  }

  loadAvailableTimeSlots(request?: {
    calendarId?: string;
    meetingTypeId?: string;
    timeSpan?: TimeSpan;
    timeZone?: TimeZone;
    userID?: string;
  }): Observable<TimeSpan[]> {
    const req = request || {};

    return this.guestService.listAvailableTimeSlots({
      hostId: req.calendarId,
      meetingTypeId: req.meetingTypeId,
      timeZone: req.timeZone,
      timeSpan: req.timeSpan,
      userId: req.userID,
    });
  }

  getEntityAssociation(id: string): Observable<EntityAssociations> {
    return this.hostService.getEntityAssociations({ entityId: id });
  }

  hostBookMeeting(req: {
    calendarId: string;
    meetingTypeId: string;
    start: Date;
    comment: string;
    attendees: Contact[];
    location: string;
    userId: string;
  }): Observable<string> {
    return this.hostService.hostBookMeeting({ ...req, hostId: req.calendarId }).pipe(
      map((resp) => {
        return resp.meetingId;
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
    this.subscriptions = [];
    this.initialized = false;
  }
}

function selectGroupMetingTypes(state: State): { [calendarId: string]: GroupMeetingType[] } {
  const groupCalendarContainers = Object.values(state.calendars).filter(
    (calendar) => calendar?.calendar?.calendarType === CalendarType.CALENDAR_TYPE_GROUP,
  );
  const calendarIdToGroupMeetingTypeMap: { [calendarId: string]: GroupMeetingType[] } = {};
  groupCalendarContainers.forEach((groupCalendarContainer) => {
    const meetingTypes = Object.values(groupCalendarContainer.meetingTypes || {});
    calendarIdToGroupMeetingTypeMap[groupCalendarContainer.calendar.id] = meetingTypes.map((meetingType) => ({
      ...meetingType,
      calendarName: groupCalendarContainer.calendar.displayName,
    }));
  });
  return calendarIdToGroupMeetingTypeMap;
}

function getPersonalCalendar(s: State): Calendar | undefined {
  const calendars = Object.values(s.calendars || []);
  return calendars.find(
    (calendar) =>
      calendar?.calendar?.calendarType === CalendarType.CALENDAR_TYPE_PERSONAL &&
      calendar?.calendar?.creatorUserId === s?.user?.id,
  )?.calendar;
}

// Partial object with partial nested properties as a type
type DeepPartial<T> = {
  [P in keyof T]?: DeepPartial<T[P]>;
};
