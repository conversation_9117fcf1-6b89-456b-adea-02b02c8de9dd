import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnDestroy, OnInit } from '@angular/core';
import {
  AbstractControl,
  AsyncValidatorFn,
  UntypedFormBuilder,
  UntypedFormControl,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Calendar, HostUser, MeetingType, Group, EventGroupAndServiceAssociations } from '@vendasta/meetings';
import { Observable, Subscription, from, combineLatest, BehaviorSubject, firstValueFrom, of, Subject } from 'rxjs';
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  first,
  map,
  shareReplay,
  switchMap,
  take,
  tap,
} from 'rxjs/operators';
import { v4 as uuidv4 } from 'uuid';
import { GroupMeetingType, MeetingSchedulerStoreService } from '../data-providers/meeting-scheduler-store.service';
import {
  FEATURE_MEETING_EXPANSION_TOKEN,
  FEATURE_MEETING_EMAIL_TEMPLATE_TOKEN,
  HOST_ID_TOKEN,
  FEATURE_MEETING_EMBED_CODE_TOKEN,
  FEATURE_MEETING_ROUNDROBIN_TOKEN,
  MEETING_TYPE_LIST_VIEW_PAGE_TOKEN,
} from '../data-providers/providers';
import { UpdateConfirmationDialogComponent } from '../shared/components/update-confirmation-dialog/update-confirmation-dialog.component';
import {
  GROUP,
  PERSONAL_EVENT_TYPE,
  TEAM_EVENT_TYPE,
  MS_CONTEXT,
  MEETING_SCHEDULER_CONTEXT_INJECTION_TOKEN$,
  POSTHOG_KEYS,
  POSTHOG_CATEGORIES,
  POSTHOG_ACTIONS,
} from '../constants';
import { ProductAnalyticsService } from '@vendasta/product-analytics';

interface GroupType {
  id: string;
  calendarId: string;
  name: string;
  type: string;
  creator?: string;
}

@Component({
  selector: 'meeting-scheduler-group',
  templateUrl: './group.component.html',
  styleUrl: './group.component.scss',
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GroupComponent implements OnInit, OnDestroy {
  buttonText: string;
  linkPrefix$: Observable<string>;

  readonly colors = [
    '#EE5353',
    '#F778B4',
    '#E27EFF',
    '#8988FC',
    '#4A91E9',
    '#0BC0D7',
    '#34C66F',
    '#67C820',
    '#DFC12C',
    '#F49A31',
  ];
  public originalSlug: string;
  private readonly slugRandomness = uuidv4().split('-')[0];

  form = this.formBuilder.group({
    id: this.formBuilder.control(''),
    name: this.formBuilder.control('', [Validators.required]),
    description: this.formBuilder.control(''),
    color: this.formBuilder.control(this.colors[0]),
    slug: this.formBuilder.control(
      '',
      [Validators.required, Validators.pattern('[a-zA-Z0-9_-]+')],
      [this.uniqueSlug()],
    ),
  });

  events = this.formBuilder.group({
    eventList: new UntypedFormControl(null),
  });

  isUpdate = false;
  isLoading$ = new BehaviorSubject<boolean>(true);
  isButtonLoading$ = new Subject<boolean>();
  timezone;
  meetingIdNotNull = false;

  calendar: Calendar | null = null;

  hostUserIds: string[] = [];
  groupId: string;
  hostid: string;
  businessId: string;
  partnerid: string;
  groupName: string;
  private group: Group;
  unmatchedAssociations: GroupType[] = [];
  selectedOptions: GroupType[] = [];
  listMeetingTypes: GroupType[] = [];

  personalMeetingTypes$: Observable<MeetingType[]>;
  groupMeetingTypes$: Observable<{ [calendarId: string]: GroupMeetingType[] }>;
  selectedGroupCalendarId$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  selectedGroupMeetingTypes$: Observable<MeetingType[]>;
  groupCalendars$: Observable<Calendar[]>;
  allGroupCalendarIds$: Observable<string[]>;
  allGroupMeetingTypes$: Observable<MeetingType[]>;
  isEventTypeNotSelected = false;
  private readonly subscriptions: Subscription[] = [];
  showAlert$: Observable<boolean>;

  constructor(
    @Inject(MEETING_TYPE_LIST_VIEW_PAGE_TOKEN) public readonly meetingTypeListViewPage$: Observable<string>,
    @Inject(HOST_ID_TOKEN) private readonly _hostId$: Observable<string>,
    @Inject('PARTNER_ID') readonly partnerId$: Observable<string>,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly translate: TranslateService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly storeService: MeetingSchedulerStoreService,
    private readonly alerts: SnackbarService,
    private readonly dialog: MatDialog,
    @Inject(FEATURE_MEETING_EMAIL_TEMPLATE_TOKEN)
    readonly featureEmailTemplateEnabled$: Observable<boolean>,
    @Inject(FEATURE_MEETING_EXPANSION_TOKEN)
    readonly featureMeetingExpansionEnabled$: Observable<boolean>,
    @Inject(FEATURE_MEETING_EMBED_CODE_TOKEN)
    readonly featureMeetingEmbedEnabled$: Observable<boolean>,
    @Inject(FEATURE_MEETING_ROUNDROBIN_TOKEN)
    readonly featureMeetingRoundRobinEnabled$: Observable<boolean>,
    private readonly meetingSchedulerStoreService: MeetingSchedulerStoreService,
    private cdr: ChangeDetectorRef,
    @Inject(MEETING_SCHEDULER_CONTEXT_INJECTION_TOKEN$) protected context: MS_CONTEXT,
    private readonly analyticsService: ProductAnalyticsService,
  ) {}

  async ngOnInit() {
    try {
      this.subscriptions.push(
        this.meetingSchedulerStoreService.loadPersonalCalendar().subscribe((calendar) => (this.calendar = calendar)),
      );
      this.initializeGroupDetails();
      await this.loadInitialData();
      this.setupFormListeners();
      if (this.isUpdate) this.loadGroupDetails();
      this.loadData();
    } catch (error) {
      console.error('Error during initialization:', error);
      this.isLoading$.next(false);
    }
  }

  private initializeGroupDetails() {
    this.meetingSchedulerStoreService.tableViewEnabled = true;
    this.meetingSchedulerStoreService.selectedToggle = GROUP;
    this.groupId = this.route.snapshot.params.groupId || '';
    this.isUpdate = this.groupId !== '';
    this.buttonText = this.translate.instant(
      this.isUpdate ? 'MEETING_SCHEDULER.BOOKING_GROUP.UPDATE_BUTTON' : 'MEETING_SCHEDULER.BOOKING_GROUP.CREATE_BUTTON',
    );
  }

  private async loadInitialData() {
    this.hostid = await firstValueFrom(this._hostId$);
    this.groupCalendars$ = this.storeService.loadGroupCalendars();
    this.linkPrefix$ = this.getFormattedLinkPrefix();
  }

  private getFormattedLinkPrefix(): Observable<string> {
    return this.storeService.loadPersonalGeneralBookingLink().pipe(
      map((url) => {
        const parts = url.replace(/https?:\/\//, '').split('/');
        return (parts.length > 0 ? parts[0] : '') + '/you/';
      }),
    );
  }

  private setupFormListeners() {
    this.subscriptions.push(
      this.form.controls.name.valueChanges
        .pipe(debounceTime(300), distinctUntilChanged())
        .subscribe(() => this.updateSlugIfNeeded()),
    );
  }

  private updateSlugIfNeeded() {
    const { name, slug } = this.form.controls;
    if (this.isUpdate || slug.dirty) return;

    let generatedSlug = name.value
      .toLowerCase()
      .trim()
      .replace(/\s+/g, '-')
      .replace(/[^0-9a-zA-Z_-]/g, '');

    this.subscriptions.push(
      this.isSlugExist(generatedSlug)
        .pipe(first())
        .subscribe((result) => {
          if (result?.unique) {
            generatedSlug += `-${this.slugRandomness}`;
          }
          this.form.controls.slug.patchValue(generatedSlug, { emitEvent: false });
        }),
    );
  }

  private loadGroupDetails() {
    this.subscriptions.push(
      this.storeService.getGroup(this.groupId, '').subscribe((group) => {
        if ('group' in group) {
          // If group is of type GetGroupResponse, extract the actual Group object
          this.group = group.group;
        } else {
          // Otherwise, it is already of type Group
          this.group = group;
        }
        this.populateFormWithGroupData(this.group);
      }),
    );
  }

  private populateFormWithGroupData(group: Group) {
    if (!this.group) return;
    this.originalSlug = group.slug;
    this.groupName = group.name;
    this.form.patchValue({
      id: group.id,
      name: (group.name || '').trim(),
      description: (group.description || '').trim(),
      color: group.hexColor,
      slug: group.slug,
    });
  }

  private getDefaultOptions(options: any[]): GroupType[] {
    return options.map(({ id, calendarId, name, CalendarType: type, calendarName }) => ({
      id,
      calendarId,
      name,
      type,
      creator:
        this.context === MS_CONTEXT.MS_CONTEXT_PARTNER && type === TEAM_EVENT_TYPE
          ? calendarName
          : type === PERSONAL_EVENT_TYPE && calendarId === this.calendar?.id
            ? this.calendar.displayName
            : null,
    }));
  }

  private mapAssociations(associations: EventGroupAndServiceAssociations[]): GroupType[] {
    return associations.map(({ id, calendarId, name, eventType: type, creatorUserName: creator }) => ({
      id,
      calendarId,
      name,
      type,
      creator,
    }));
  }

  private mapOptionsWithAssociations(options: any[], associations: GroupType[]): GroupType[] {
    const associationMap = new Map(associations.map((assoc) => [assoc.id, assoc]));
    return options.map((option) => {
      const matchingAssociation = associationMap.get(option.id);
      return {
        ...option,
        creator:
          option.type === PERSONAL_EVENT_TYPE
            ? (matchingAssociation?.creator ??
              option.creator ??
              this.translate.instant('MEETING_SCHEDULER.MEETING_LIST.UNKNOWN_USER'))
            : option.creator,
      };
    });
  }

  private getSelectedAssociations(options: GroupType[], associations: GroupType[]): GroupType[] {
    const associationIds = new Set(associations.map(({ id }) => id));
    return options.filter(({ id }) => associationIds.has(id));
  }

  private loadData(): void {
    try {
      this.personalMeetingTypes$ = this.loadSortedPersonalMeetingTypes();
      this.groupMeetingTypes$ = this.meetingSchedulerStoreService.loadGroupMeetingTypes({ returnCache: true });

      this.allGroupCalendarIds$ = this.getAllGroupCalendarIds();
      this.allGroupMeetingTypes$ = this.getAllGroupMeetingTypes();

      this.subscriptions.push(
        this.combineMeetingTypes().subscribe({
          next: (combinedMeetingTypes) => {
            const associations = this.mapAssociations(this.group?.associations || []);
            this.listMeetingTypes = this.mapOptionsWithAssociations(
              this.getDefaultOptions(combinedMeetingTypes),
              associations,
            );
            const selectedAssociations = this.getSelectedAssociations(this.listMeetingTypes, associations);
            this.events.patchValue({
              eventList: selectedAssociations || [],
            });
            this.unmatchedAssociations = associations.filter(
              ({ id }) => !this.listMeetingTypes.some((option) => option.id === id),
            );
            const orderMap = new Map<string, number>();
            associations.forEach((item, index) => orderMap.set(item.id, index));
            const mergedMap = new Map(
              [...selectedAssociations, ...this.unmatchedAssociations].map((item) => [item.id, item]),
            );
            this.selectedOptions = [...mergedMap.values()].sort(
              (a, b) =>
                (orderMap.get(a.id) ?? Number.MAX_SAFE_INTEGER) - (orderMap.get(b.id) ?? Number.MAX_SAFE_INTEGER),
            );

            this.isLoading$.next(false);
          },
          error: (err) => {
            console.error(err);
          },
        }),
      );
    } catch (error) {
      console.error('Error in loadData:', error);
      this.isLoading$.next(false);
    }
  }

  private loadSortedPersonalMeetingTypes(): Observable<MeetingType[]> {
    return this.meetingSchedulerStoreService
      .loadPersonalMeetingTypes({ returnCache: false })
      .pipe(
        map((meetingTypes) =>
          meetingTypes
            ? meetingTypes
                .map((mt) => ({ ...mt, CalendarType: PERSONAL_EVENT_TYPE }))
                .sort((a, b) => a.name.localeCompare(b.name, undefined, { numeric: true }))
            : [],
        ),
      );
  }

  private getAllGroupCalendarIds(): Observable<string[]> {
    return this.groupCalendars$.pipe(
      map((calendars) => calendars?.map((calendar) => calendar.id)),
      filter((ids) => ids?.length > 0),
    );
  }

  private getAllGroupMeetingTypes(): Observable<MeetingType[]> {
    return this.groupMeetingTypes$.pipe(
      switchMap((groupMeetingTypes) => {
        const allMeetings = Object.keys(groupMeetingTypes).reduce(
          (acc, calendarId) => acc.concat(groupMeetingTypes[calendarId] || []),
          [] as MeetingType[],
        );
        return of(allMeetings);
      }),
      shareReplay(1),
    );
  }

  private combineMeetingTypes(): Observable<MeetingType[]> {
    return combineLatest([this.personalMeetingTypes$, this.allGroupMeetingTypes$]).pipe(
      map(([personalMeetingTypes, groupMeetingTypes]) => [
        ...(personalMeetingTypes || []),
        ...(groupMeetingTypes || []),
      ]),
    );
  }

  get slugFormControl() {
    return this.form.get('slug');
  }

  get eventList() {
    return this.events.get('eventList');
  }

  get personalEventTypes() {
    return this.listMeetingTypes?.filter((mt) => mt.type === PERSONAL_EVENT_TYPE) || [];
  }

  get teamEventTypes() {
    return this.listMeetingTypes?.filter((mt) => mt.type === TEAM_EVENT_TYPE) || [];
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }
  onSelectionChange(_event: any): void {
    this.selectedOptions = [
      ...new Map(
        [...this.unmatchedAssociations, ...(this.eventList?.value || [])].map((item) => [item.id, item]),
      ).values(),
    ];
  }
  onDelete(index: number): void {
    const [deletedOption] = this.selectedOptions.splice(index, 1);
    if (!deletedOption) return;
    this.unmatchedAssociations = this.unmatchedAssociations.filter((assoc) => assoc.id === deletedOption.id);
    this.eventList.setValue(this.selectedOptions);
  }

  onDrop(event: CdkDragDrop<string[]>): void {
    const droppedItem = event.item.data;
    const previousIndex = this.selectedOptions.findIndex((item) => item === droppedItem);
    const currentIndex = event.currentIndex;

    moveItemInArray(this.selectedOptions, previousIndex, currentIndex);
    this.cdr.detectChanges();
  }

  getBadgeColor(calendarType: string): 'light-grey' | 'blue' | 'green' | 'yellow' {
    switch (calendarType) {
      case PERSONAL_EVENT_TYPE:
        return 'green';
      case TEAM_EVENT_TYPE:
        return 'blue';
      case GROUP:
        return 'yellow';
      default:
        return 'light-grey';
    }
  }

  get slugChanged(): boolean {
    return this.isUpdate && this.originalSlug !== (this.form.controls.slug.value as string);
  }

  uniqueSlug(): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      if (!control.value || control.value === this.originalSlug || !control.dirty) {
        return of(null);
      }
      return this.isSlugExist(control.value);
    };
  }

  isSlugExist(value: string) {
    return this.meetingSchedulerStoreService.checkGroupOrServiceSlugExist(value, GROUP).pipe(
      debounceTime(500),
      distinctUntilChanged(),
      take(1),
      map((taken: boolean) => (taken ? { unique: true } : null)),
      catchError(() => {
        console.error('API Error in slug validation');
        return of(null);
      }),
      tap(() => this.cdr.detectChanges()),
    );
  }

  select(event: boolean, hostUser: HostUser): void {
    if (this.hostUserIds.indexOf(hostUser.userId) < 0) {
      this.hostUserIds.push(hostUser.userId);
    } else {
      this.hostUserIds = this.hostUserIds.filter((userId) => userId !== hostUser.userId);
    }
  }

  revertSlug(): void {
    this.form.controls.slug.setValue(this.originalSlug);
  }

  setColor(color: string): void {
    this.form.controls.color.setValue(color);
  }

  private buildGroupData(): Group {
    const { id, name, slug, description, color } = this.form.controls;

    return {
      id: id.value as string,
      name: name.value as string,
      slug: slug.value as string,
      description: description.value as string,
      hexColor: color.value as string,
      bookingUrl: slug.value as string,
      associations: this.selectedOptions.map((option) => ({
        id: option.id,
        identifier: 'event',
      })),
    } as Group;
  }

  private async openSlugUpdateDialog(group: Group): Promise<void> {
    const dialogRef = this.dialog.open(UpdateConfirmationDialogComponent, {
      maxWidth: '500px',
      data: {
        type: GROUP,
        originalSlug: this.originalSlug,
        newSlug: group.slug,
        confirmationCallback: () => from(this.updateGroup(group.id, group)),
      },
    });

    await dialogRef.afterClosed().toPromise();
  }

  private async processUpdate(group: Group): Promise<void> {
    if (this.originalSlug === group.slug) {
      await this.updateGroup(group.id, group);
      return;
    }

    await this.openSlugUpdateDialog(group);
  }

  async createOrUpdateGroup(): Promise<void> {
    if (this.form.invalid || this.selectedOptions.length < 2) {
      this.form.markAllAsTouched();
      this.events.markAllAsTouched();
      if (this.selectedOptions.length < 2) {
        this.isEventTypeNotSelected = true;
      }
      return;
    }
    this.isButtonLoading$.next(true);
    const group = this.buildGroupData();

    try {
      this.isUpdate ? await this.processUpdate(group) : await this.createGroup(group);
      this.navigateToMeetingList();
    } catch (error) {
      this.handleError(error);
    } finally {
      this.isButtonLoading$.next(false);
    }
  }

  private async createGroup(group: Group): Promise<void> {
    await this.storeService.createGroup(group, this.hostid);
    this.analyticsService.trackEvent(POSTHOG_KEYS.GROUP_CREATED, POSTHOG_CATEGORIES.USER, POSTHOG_ACTIONS.CLICK);
    this.alerts.openSuccessSnack('MEETING_SCHEDULER.MEETING_LIST.GROUP.CREATED_SUCCESS');
  }

  private async updateGroup(id: string, group: Group): Promise<void> {
    await this.storeService.updateGroup(id, group);
    this.analyticsService.trackEvent(POSTHOG_KEYS.GROUP_UPDATED, POSTHOG_CATEGORIES.USER, POSTHOG_ACTIONS.CLICK);
    this.alerts.openSuccessSnack('MEETING_SCHEDULER.MEETING_LIST.GROUP.UPDATED_SUCCESS');
  }

  private handleError(error: any): void {
    const errorMsg = this.isUpdate
      ? 'MEETING_SCHEDULER.MEETING_LIST.GROUP.UPDATED_ERROR'
      : 'MEETING_SCHEDULER.MEETING_LIST.GROUP.CREATED_ERROR';

    this.alerts.openErrorSnack(errorMsg);
    console.error(error);
  }

  private async navigateToMeetingList(): Promise<unknown> {
    const meetingTypeListLink = await this.meetingTypeListViewPage$.pipe(take(1)).toPromise();
    return this.router.navigate([meetingTypeListLink]);
  }

  trackById(index: number, item: { id: number }) {
    return item.id;
  }
}
