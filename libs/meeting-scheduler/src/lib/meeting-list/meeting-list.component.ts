import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import moment from 'moment-timezone';
import { BehaviorSubject, combineLatest, from, Observable, of, Subject, Subscription } from 'rxjs';
import { openSecureNewTab } from '../utils';
import { DeleteBookingLinkConfirmationDialogComponent } from './delete-booking-link-confirmation-dialog/delete-booking-link-confirmation-dialog.component';
import { MeetingSchedulerIcons } from '../interface';
import { MatTabGroup } from '@angular/material/tabs';
import { Router } from '@angular/router';
import { MEETING_SCHEDULER_CONTEXT_INJECTION_TOKEN$, MS_CONTEXT } from '../constants';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Inject,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  AttachmentInterface,
  Calendar,
  durationStringToMinutes,
  Meeting,
  MeetingFormAnswer,
  MeetingType,
} from '@vendasta/meetings';
import {
  catchError,
  distinctUntilChanged,
  filter,
  first,
  map,
  shareReplay,
  startWith,
  switchMap,
  tap,
} from 'rxjs/operators';
import {
  GroupMeetingType,
  isGroupMeetingType,
  MeetingSchedulerStoreService,
} from '../data-providers/meeting-scheduler-store.service';
import {
  ALTERNATE_TRANSLATIONS_TOKEN,
  CALENDAR_PAGE_LINK_TOKEN,
  EVENT_TYPE_PAGE_TOKEN,
  GROUP_PAGE_TOKEN,
  SERVICE_PAGE_TOKEN,
  FEATURE_GROUP_CALENDAR_SLUG_EDITING_ENABLED_TOKEN,
  FEATURE_GROUPS_AND_SERVICES_TOKEN,
  FEATURE_SERVICES_TEAM_LINKS_ENABLED_TOKEN,
  ICONS_TOKEN,
  MEETING_SCHEDULER_MEETING_NAVIGATION_SERVICE_TOKEN,
  MeetingDetailsAction,
  MeetingDetailsActionOrLink,
  MeetingNavigationLinkService,
  CALENDAR_VIEW_PAGE_LINK_TOKEN,
} from '../data-providers/providers';

export interface TableDataSource {
  meetingId: string;
  title: string;
  joinMeeting: string;
  date: { start: Date; end: Date };
  guest: string;
  contact: { email: string; phoneNumber: string };
  additionalGuestEmails: string[];
  comments: string;
  location: string;
  created: Date;
  formAnswers: MeetingFormAnswer[];
  attachments: AttachmentInterface[];
  calendarId: string;
}

interface TimeSpan {
  start: Date;
  end: Date;
}

interface LoadMeetingFilters {
  meetingTypeIds: string[];
  timeSpan: TimeSpan;
}

@Component({
  selector: 'meeting-scheduler-meeting-list',
  templateUrl: './meeting-list.component.html',
  styleUrls: ['./meeting-list.component.scss', '../schedule-settings/calendar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class MeetingListComponent implements OnInit, OnDestroy {
  meetings$: Observable<Meeting[]>;
  isHostConfigured$: Observable<boolean>;
  dataSource$: Observable<TableDataSource[]>;
  noMeetingTypesColor = '#81C784';
  personalGeneralLinkDescription = '';
  groupGeneralLinkDescription = '';
  groupsSectionHeader = '';
  NoTeamIcon = 'https://storage.googleapis.com/meeting-scheduler-prod-public-images/Icon-for-no-team.svg';
  @ViewChild('tabGroup') tabGroup: MatTabGroup;
  @ViewChild('calendarTemplate', { static: true }) calendarTemplate!: TemplateRef<any>;
  @ViewChild('settingsTemplate', { static: true }) settingsTemplate!: TemplateRef<any>;
  @ViewChild('generalEventLinkCard') generalEventLinkCard!: TemplateRef<any>;
  @ViewChild('eventTypeCard') eventTypeCard!: TemplateRef<any>;

  allMeetingTypes$: Observable<MeetingType[]>;
  personalCalendar$: Observable<Calendar>;
  personalMeetingTypes$: Observable<MeetingType[]>;
  hasPersonalMeetingTypes$: Observable<boolean>;
  groupCalendars$: Observable<Calendar[]>;
  hasGroupCalendars$: Observable<boolean>;
  groupMeetingTypes$: Observable<{ [calendarId: string]: GroupMeetingType[] }>;
  groupGeneralLinks$: Observable<{ [calendarId: string]: string }>;
  allGroupMeetingTypes$: Observable<MeetingType[]>;
  hasGroupMeetingTypes$: Observable<boolean>;
  hasSelectedGroupMeetingTypes$: Observable<boolean>;
  selectedGroupCalendarId$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  selectedGroupCalendarName$: Observable<string>;
  selectedCalendar$: Observable<Calendar>;
  selectedGroupGeneralLink$: Observable<string>;
  selectedGroupMeetingTypes$: Observable<MeetingType[]>;

  primaryMeetingDetailsActionsAndLinks$: Observable<{ [meetingId: string]: MeetingDetailsActionOrLink }>;
  secondaryMeetingDetailsActionsAndLinks$: Observable<{ [meetingId: string]: MeetingDetailsActionOrLink[] }>;

  // to identify new Date meeting
  previousDate = '';

  isMobileScreen$: Observable<boolean>;
  isLoading$ = new Subject<boolean>();

  meetingTypeFilter$$: BehaviorSubject<string[]> = new BehaviorSubject([]);
  meetingTimeSpanFilter$$: BehaviorSubject<TimeSpan> = new BehaviorSubject({
    start: new Date(),
    end: this.getTenYearsLater(),
  });
  selectedTab: 'upcoming' | 'past' = 'upcoming';
  now = new Date();
  selectedEventTypeFilter: string;
  timeRangeKeySelected = '90days';
  timeRangeOptions = [
    {
      key: '7days',
      name: this.translate.instant('MEETING_SCHEDULER.MEETING_LIST.TIME_FILTER.7_DAYS'),
      dateRange: [moment(this.now).subtract(7, 'days').toDate(), this.now],
    },
    {
      key: '30days',
      name: this.translate.instant('MEETING_SCHEDULER.MEETING_LIST.TIME_FILTER.30_DAYS'),
      dateRange: [moment(this.now).subtract(30, 'days').toDate(), this.now],
    },
    {
      key: '90days',
      name: this.translate.instant('MEETING_SCHEDULER.MEETING_LIST.TIME_FILTER.90_DAYS'),
      dateRange: [moment(this.now).subtract(90, 'days').toDate(), this.now],
    },
    {
      key: '6months',
      name: this.translate.instant('MEETING_SCHEDULER.MEETING_LIST.TIME_FILTER.6_MONTHS'),
      dateRange: [moment(this.now).subtract(6, 'months').toDate(), this.now],
    },
    {
      key: '12months',
      name: this.translate.instant('MEETING_SCHEDULER.MEETING_LIST.TIME_FILTER.12_MONTHS'),
      dateRange: [moment(this.now).subtract(12, 'months').toDate(), this.now],
    },
    {
      key: 'custom',
      name: this.translate.instant('MEETING_SCHEDULER.MEETING_LIST.TIME_FILTER.CUSTOM'),
      dateRange: [moment(this.now).subtract(90, 'days').toDate(), this.now],
    },
  ];

  private readonly subscriptions: Subscription[] = [];
  isTableView = false;
  category: string;
  protected MSContextType = MS_CONTEXT;
  selectedToggle = 'events';

  constructor(
    @Inject(CALENDAR_PAGE_LINK_TOKEN) public readonly calendarPageLink$: Observable<string>,
    @Inject(EVENT_TYPE_PAGE_TOKEN) public readonly eventTypePageLink$: Observable<string>,
    @Inject(GROUP_PAGE_TOKEN) public readonly groupPageLink$: Observable<string>,
    @Inject(SERVICE_PAGE_TOKEN) public readonly servicePageLink$: Observable<string>,
    @Inject(CALENDAR_VIEW_PAGE_LINK_TOKEN) public readonly myMeeting$: Observable<string>,
    @Inject(MEETING_SCHEDULER_MEETING_NAVIGATION_SERVICE_TOKEN)
    private readonly meetingNavigationLinkService: MeetingNavigationLinkService,
    @Inject(FEATURE_GROUP_CALENDAR_SLUG_EDITING_ENABLED_TOKEN)
    readonly featureGroupCalendarSlugEditingEnabled$: Observable<boolean>,
    @Inject(FEATURE_SERVICES_TEAM_LINKS_ENABLED_TOKEN)
    readonly featureServicesAndTeamLinksEnabled$: Observable<boolean>,
    @Inject(FEATURE_GROUPS_AND_SERVICES_TOKEN)
    readonly featureGroupsandServicesEnabled$: Observable<boolean>,
    @Inject(ALTERNATE_TRANSLATIONS_TOKEN) readonly alternateTranslations: { [translationKey: string]: string },
    @Inject(ICONS_TOKEN) readonly icons: MeetingSchedulerIcons,
    private readonly meetingSchedulerStoreService: MeetingSchedulerStoreService,
    private readonly alertService: SnackbarService,
    private readonly translate: TranslateService,
    private readonly dialog: MatDialog,
    private readonly breakpointObserver: BreakpointObserver,
    private cdr: ChangeDetectorRef,
    private router: Router,
    @Inject(MEETING_SCHEDULER_CONTEXT_INJECTION_TOKEN$) protected context: MS_CONTEXT,
  ) {
    this.isMobileScreen$ = this.breakpointObserver.observe([Breakpoints.XSmall]).pipe(map((result) => result.matches));
    this.personalGeneralLinkDescription = this.translate.instant(
      'MEETING_SCHEDULER.MEETING_LIST.PERSONAL_GENERAL_LINK_DESCRIPTION',
    );
    const groupGeneralLinkDescriptionKey = 'MEETING_SCHEDULER.MEETING_LIST.GROUP_GENERAL_LINK_DESCRIPTION';
    this.groupGeneralLinkDescription =
      alternateTranslations[groupGeneralLinkDescriptionKey] || this.translate.instant(groupGeneralLinkDescriptionKey);
    const groupsSectionHeaderKey = 'MEETING_SCHEDULER.MEETING_LIST.TEAMS_SECTION_HEADER';
    this.groupsSectionHeader =
      alternateTranslations[groupsSectionHeaderKey] || this.translate.instant(groupsSectionHeaderKey);
  }

  ngOnInit(): void {
    this.isLoading$.next(true);
    this.isTableView = this.meetingSchedulerStoreService.tableViewEnabled;
    this.selectedToggle = this.meetingSchedulerStoreService.selectedToggle;
    this.isHostConfigured$ = this.meetingSchedulerStoreService.loadHasHostEverBeenConfigured();
    this.personalMeetingTypes$ = this.meetingSchedulerStoreService
      .loadPersonalMeetingTypes({ returnCache: false })
      .pipe(
        map((meetingTypes) => {
          if (!meetingTypes) {
            return null;
          }
          return meetingTypes.sort((mt1, mt2) => {
            return mt1.name.localeCompare(mt2.name, undefined, { numeric: true });
          });
        }),
      );
    this.featureGroupsandServicesEnabled$.subscribe((isEnabled) => {
      if (isEnabled) {
        this.myMeeting$.subscribe((meetingPageURL) => {
          this.navigateToGroups(meetingPageURL);
        });
      }
    });
    this.personalCalendar$ = this.meetingSchedulerStoreService.loadPersonalCalendar();
    this.hasPersonalMeetingTypes$ = this.personalMeetingTypes$.pipe(map((types) => types.length > 0));
    this.groupMeetingTypes$ = this.meetingSchedulerStoreService.loadGroupMeetingTypes({ returnCache: true });
    this.groupGeneralLinks$ = this.meetingSchedulerStoreService.loadGroupGeneralBookingLinks();
    this.groupCalendars$ = this.meetingSchedulerStoreService.loadGroupCalendars();
    this.hasGroupCalendars$ = this.groupCalendars$.pipe(map((calendars) => calendars?.length > 0));
    const firstGroupCalendarId$ = this.groupCalendars$.pipe(
      map((calendars) => calendars?.[0]?.id),
      filter<string>(Boolean),
      first(),
    );
    this.subscriptions.push(
      firstGroupCalendarId$.subscribe((id) => {
        if (!this.selectedGroupCalendarId$$.getValue()) {
          this.selectedGroupCalendarId$$.next(id);
        }
      }),
    );
    this.allGroupMeetingTypes$ = this.groupMeetingTypes$.pipe(
      map((groupMeetingTypes) => meetingTypeMapToArray(groupMeetingTypes)),
    );
    this.hasGroupMeetingTypes$ = this.allGroupMeetingTypes$.pipe(
      map((allGroupMeetingTypes) => allGroupMeetingTypes.length > 0),
    );

    this.allMeetingTypes$ = combineLatest([this.personalMeetingTypes$, this.allGroupMeetingTypes$]).pipe(
      tap(() => this.isLoading$.next(false)),
      map(([personalMeetingTypes, allGroupMeetingTypes]) => {
        return sortAndJoinMeetingsTypes(personalMeetingTypes, allGroupMeetingTypes);
      }),
      shareReplay(1),
    );
    this.selectedCalendar$ = this.selectedGroupCalendarId$$.pipe(
      filter<string>(Boolean),
      switchMap((selectedCalendarId) => this.meetingSchedulerStoreService.loadCalendar(selectedCalendarId)),
      shareReplay(1),
    );
    this.selectedGroupMeetingTypes$ = combineLatest([
      this.groupMeetingTypes$,
      this.selectedGroupCalendarId$$.asObservable(),
    ]).pipe(map(([groupMeetingTypes, selectedGroupCalendarId]) => groupMeetingTypes[selectedGroupCalendarId] || []));
    this.selectedGroupGeneralLink$ = combineLatest([
      this.groupGeneralLinks$,
      this.selectedGroupCalendarId$$.asObservable(),
    ]).pipe(map(([groupGeneralLinks, selectedGroupCalendarId]) => groupGeneralLinks[selectedGroupCalendarId] || ''));

    this.hasSelectedGroupMeetingTypes$ = this.selectedGroupMeetingTypes$.pipe(
      map((meetingTypes) => meetingTypes.length > 0),
    );

    const loadMeetingFilters$ = this.setupLoadMeetingFilters();
    this.meetings$ = loadMeetingFilters$.pipe(
      switchMap((meetingTypeFilter) => this.loadMeetings(meetingTypeFilter)),
      shareReplay(1),
    );
    this.selectedGroupCalendarName$ = combineLatest([
      this.groupCalendars$,
      this.selectedGroupCalendarId$$.asObservable(),
    ]).pipe(
      map(([groupCalendars, selectedGroupCalendarId]) => {
        const selectedCalendar = groupCalendars.find((calendar) => calendar.id === selectedGroupCalendarId);
        return selectedCalendar ? selectedCalendar.displayName : 'No team selected';
      }),
    );

    this.dataSource$ = combineLatest([this.meetings$, this.allMeetingTypes$]).pipe(
      map(([meetings, meetingTypes]) => {
        return meetings
          .filter((m) => m.id !== undefined)
          .map((m) => {
            const primaryAttendee = (m.attendees || []).find((a) => a.isPrimary) || { email: '', phoneNumber: '' };
            const additionalGuestEmails = (m.attendees || []).filter((a) => !a.isPrimary).map((a) => a.email);
            const meetingType = meetingTypes.find((type) => type.id === m.eventTypeId);
            return {
              meetingId: m.id,
              title: meetingType?.name || '',
              joinMeeting: m.joinMeetingUrl.replace('https', 'http') || '',
              date: { start: m.start, end: m.end },
              guest: m.attendees.length > 0 ? m.attendees[0].firstName + ' ' + m.attendees[0].lastName : '',
              contact: primaryAttendee,
              additionalGuestEmails,
              comments: m.description || '',
              location: m.location,
              created: m.created,
              formAnswers: m.formAnswers.formAnswers,
              attachments: m.attachments,
              calendarId: m.calendarId,
              eventTypeId: m.eventTypeId,
            };
          });
      }),
      map((meetings) => {
        return this.selectedTab === 'past' ? meetings.reverse() : meetings;
      }),
    );
    this.primaryMeetingDetailsActionsAndLinks$ = this.meetings$.pipe(
      switchMap((meetings) => this.meetingNavigationLinkService.getPrimaryMeetingDetailsAction(meetings)),
      catchError(() => {
        // If we error here, we fail to load the entire page.
        // Instead, we opt to simply not load the additional links / actions (a small part of the overall page)
        this.alertService.openErrorSnack('MEETING_SCHEDULER.MEETING_LIST.ALERTS.LOAD_MEETING_BUSINESS_LINK');
        return of({});
      }),
      shareReplay(1),
      startWith({}),
    );

    this.secondaryMeetingDetailsActionsAndLinks$ = this.meetings$.pipe(
      switchMap((meetings) => this.meetingNavigationLinkService.getSecondaryMeetingDetailsAction(meetings)),
      catchError(() => {
        // If we error here, we fail to load the entire page.
        // Instead, we opt to simply not load the additional links / actions (a small part of the overall page)
        this.alertService.openErrorSnack('MEETING_SCHEDULER.MEETING_LIST.ALERTS.LOAD_MEETING_ADDITIONAL_ACTIONS');
        return of({});
      }),
      shareReplay(1),
      startWith({}),
    );
    this.breakpointObserver.observe([Breakpoints.Handset, Breakpoints.Tablet]).subscribe((result) => {
      this.category = result.matches
        ? this.translate.instant('MEETING_SCHEDULER.MEETING_LIST.CREATE_EVENT_GROUP_SERVICE_MOBILE')
        : this.translate.instant('MEETING_SCHEDULER.MEETING_LIST.CREATE_EVENT_DESKTOP');
    });
  }

  onChildDeleteCompleted(): void {
    this.isTableView = false;
    this.cdr.detectChanges();

    setTimeout(() => {
      this.isTableView = true;
      this.cdr.detectChanges();
    }, 0);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  showTableView() {
    this.isTableView = true;
    this.meetingSchedulerStoreService.tableViewEnabled = true;
  }
  showCardView() {
    this.isTableView = false;
    this.meetingSchedulerStoreService.tableViewEnabled = false;
  }

  onToggleChange(value: string): void {
    this.selectedToggle = value;
    this.meetingSchedulerStoreService.selectedToggle = value;
    this.breakpointObserver.observe([Breakpoints.Handset, Breakpoints.Tablet]).subscribe((result) => {
      let translate = 'MEETING_SCHEDULER.MEETING_LIST.CREATE_EVENT_DESKTOP';
      if (this.selectedToggle === 'service') {
        translate = 'MEETING_SCHEDULER.MEETING_LIST.CREATE_SERVICE_DESKTOP';
      } else if (this.selectedToggle === 'group') {
        translate = 'MEETING_SCHEDULER.MEETING_LIST.CREATE_GROUP_DESKTOP';
      }
      this.category = result.matches
        ? this.translate.instant('MEETING_SCHEDULER.MEETING_LIST.CREATE_EVENT_GROUP_SERVICE_MOBILE')
        : this.translate.instant(translate);
    });
  }

  goToSettingsTab(): void {
    this.tabGroup.selectedIndex = 1; // Index of the settings tab
  }

  getIconUrl(iconName: string) {
    const iconMap: { [key: string]: string } = {
      'meet-icon': 'https://storage.googleapis.com/meeting-scheduler-prod-public-images/google-hangouts-meet.svg',
      'zoom-icon': 'https://storage.googleapis.com/meeting-scheduler-prod-public-images/zoom-icon.svg',
      'teams-icon': 'https://storage.googleapis.com/meeting-scheduler-prod-public-images/teams-icon.svg',
    };
    return iconMap[iconName];
  }

  changeSelectedGroupCalendarId(event: any): void {
    this.selectedGroupCalendarId$$.next(event.value);
  }

  handleCopyMeetingLinkClicked(event: MouseEvent, url: string): void {
    if (event.ctrlKey || event.metaKey) {
      this.openMeetingLink(url);
      this.alertService.openSuccessSnack('MEETING_SCHEDULER.MEETINGS_SHARED.ALERTS.LINK_OPENED');
    } else {
      this.alertService.openSuccessSnack('MEETING_SCHEDULER.MEETINGS_SHARED.ALERTS.LINK_COPIED');
    }
  }

  openMeetingLink(url: string): void {
    const w = openSecureNewTab(url);
    if (w && w.focus) {
      w.focus();
    }
  }

  openGoogleMaps(address: string) {
    const url = 'https://www.google.com/maps?q=' + address;
    this.openMeetingLink(url);
  }

  async deleteMeetingType(id: string): Promise<void> {
    try {
      await this.meetingSchedulerStoreService.deleteMeetingType(id);
      this.alertService.openSuccessSnack('MEETING_SCHEDULER.MEETING_LIST.ALERTS.DELETE_BOOKING_LINK_SUCCESS');
    } catch (err) {
      this.alertService.openErrorSnack('MEETING_SCHEDULER.MEETING_LIST.ALERTS.DELETE_BOOKING_LINK_ERROR');
      console.error(err);
    }
  }

  handleEventTableCreate(eventTypePageLink: string, calendarId: string): void {
    const url = `${eventTypePageLink}/${calendarId}`;
    this.router.navigateByUrl(url);
  }

  handleView(eventTypePageLink: string, calendarId: string, meetingTypeSlug: string): void {
    const url = `${eventTypePageLink}/${calendarId}/${meetingTypeSlug}`;
    this.router.navigateByUrl(url);
  }

  navigateToGroups(groupPageLink: string): void {
    const url = `${groupPageLink}`;
    this.router.navigateByUrl(url);
  }

  handleDeleteMeetingLinkClicked(id: string): void {
    this.dialog.open(DeleteBookingLinkConfirmationDialogComponent, {
      maxWidth: '500px',
      data: {
        category: this.selectedToggle,
        confirmationCallback: () => from(this.deleteMeetingType(id)),
      },
    });
  }

  isSameDate(newDate: string, index: number): boolean {
    // Avioding multiple calls that cause previousDate have same value as newDate
    if (index === 0) {
      this.previousDate = newDate;
      return false;
    }

    if (this.previousDate === newDate) {
      return true;
    } else {
      this.previousDate = newDate;
      return false;
    }
  }

  toMinutes(duration: string): number {
    if (duration.endsWith('m')) return +duration.slice(0, -1);
    return durationStringToMinutes({ duration });
  }

  filterByMeetingTypeIds(meetingTypeIds: string[]): void {
    this.selectedEventTypeFilter = meetingTypeIds[0];
    this.meetingTypeFilter$$.next(meetingTypeIds);
  }

  actionClicked(e: Event, action: MeetingDetailsAction, meetingId: string, meetings: Meeting[]): void {
    e.preventDefault();
    e.stopPropagation();
    action.onClick(meetings.find((m) => m.id === meetingId));
  }

  stopPropagation(e: Event): void {
    // used to prevent event propagation for anchor tag
    e.stopPropagation();
  }

  changedMeetingListTab(index: number): void {
    this.selectedTab = index === 1 ? 'past' : 'upcoming';
    const timeSpan = { start: this.now, end: this.getTenYearsLater() };
    this.meetingTimeSpanFilter$$.next(timeSpan);
  }

  setDateRange(range?: [Date, Date]): void {
    if (this.selectedTab === 'past' && range) {
      // va-time-range-selector emits Dates or Moments
      // (Date|Moment)->Moment->Date handles both
      // va-time-range-selector also doesn't care about times, just dates
      const start = moment(range[0]);
      let end = moment(range[1]);
      const now = moment();
      const endsToday = end.isSame(now, 'day');
      if (endsToday) {
        end.add(now.hour(), 'hour');
        end.add(now.minute(), 'minute');
        end.add(now.second(), 'second');
      } else {
        end = end.endOf('day');
      }
      this.meetingTimeSpanFilter$$.next({
        start: start.toDate(),
        end: end.toDate(),
      });
    }
    if (this.timeRangeKeySelected === 'custom' && range) {
      this.timeRangeOptions.forEach((option) => {
        if (option.key === 'custom') {
          option.dateRange = range;
        }
      });
    }
  }

  setTimeRangeKey(key: string): void {
    this.timeRangeKeySelected = key;
  }

  private getTenYearsLater(): Date {
    const tenYearsLater = new Date();
    tenYearsLater.setFullYear(tenYearsLater.getFullYear() + 10);
    return tenYearsLater;
  }

  private setupLoadMeetingFilters(): Observable<LoadMeetingFilters> {
    const meetingTypeFilter$ = this.meetingTypeFilter$$.pipe(startWith([]), distinctUntilChanged());
    const timeSpanFilter$ = this.meetingTimeSpanFilter$$.pipe(
      distinctUntilChanged((a, b) => {
        const aStr = a.start.toISOString() + '-' + a.end.toISOString();
        const bStr = b.start.toISOString() + '-' + b.end.toISOString();
        return aStr === bStr;
      }),
    );
    return combineLatest([meetingTypeFilter$, timeSpanFilter$]).pipe(
      map(([meetingTypeIds, timeSpan]) => {
        return {
          meetingTypeIds,
          timeSpan,
        };
      }),
    );
  }

  private loadMeetings(filters: LoadMeetingFilters): Observable<Meeting[]> {
    const { meetingTypeIds, timeSpan } = filters;
    return this.meetingSchedulerStoreService.loadPersonalMeetings({
      filters: {
        timeSpan: timeSpan,
        meetingTypeIds: meetingTypeIds && meetingTypeIds.length > 0 ? meetingTypeIds : [],
      },
      timeZone: { id: moment.tz.guess() },
      pageSize: 1000,
    });
  }
}

function meetingTypeMapToArray(meetingTypeMap: { [key: string]: MeetingType[] }): MeetingType[] {
  return Object.values(meetingTypeMap || {}).reduce((allTeamMeetingTypes, meetingTypes) => {
    return [...allTeamMeetingTypes, ...meetingTypes];
  }, []);
}

function sortAndJoinMeetingsTypes(mts1: MeetingType[], mts2: MeetingType[]): MeetingType[] {
  return [...sortMeetingTypes(mts1), ...sortMeetingTypes(mts2)].map((mt) => ({
    ...mt,
    displayName: isGroupMeetingType(mt) ? `${mt.calendarName}: ${mt.name}` : mt.name,
  }));
}

function sortMeetingTypes(meetingTypes: MeetingType[]): MeetingType[] {
  if (!meetingTypes) {
    return [];
  }
  return meetingTypes.sort((mt1, mt2) => {
    const mt1Name = isGroupMeetingType(mt1) ? `${mt1.calendarName} ${mt1.name}` : mt1.name;
    const mt2Name = isGroupMeetingType(mt2) ? `${mt2.calendarName} ${mt2.name}` : mt2.name;
    return mt1Name.localeCompare(mt2Name, undefined, { numeric: true, sensitivity: 'base' });
  });
}
