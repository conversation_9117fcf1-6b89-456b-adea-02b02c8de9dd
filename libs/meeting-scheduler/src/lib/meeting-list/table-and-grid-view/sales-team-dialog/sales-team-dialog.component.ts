import { Component, Inject, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';

import {
  MatDialogActions,
  MatDialogClose,
  MatDialogContent,
  MatDialogRef,
  MatDialogTitle,
} from '@angular/material/dialog';
import { Calendar } from '@vendasta/meetings';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatAutocomplete, MatAutocompleteTrigger, MatOption } from '@angular/material/autocomplete';
import { AsyncPipe, NgForOf } from '@angular/common';
import { TranslationModule } from '@galaxy/crm/static';
import { MatButton } from '@angular/material/button';
import { EVENT_TYPE_PAGE_TOKEN } from './../../../data-providers/providers';
import { forkJoin, Observable, Subject, takeUntil } from 'rxjs';
import { map, startWith, take } from 'rxjs/operators';
import { Form<PERSON><PERSON>er, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MeetingSchedulerStoreService } from './../../../data-providers/meeting-scheduler-store.service';
import { MatInput } from '@angular/material/input';
import { MatMenuItem } from '@angular/material/menu';

@Component({
  selector: 'meeting-scheduler-sales-team-dialog',
  standalone: true,
  imports: [
    GalaxyButtonLoadingIndicatorModule,
    GalaxyFormFieldModule,
    MatDialogContent,
    MatDialogActions,
    MatDialogTitle,
    MatAutocompleteTrigger,
    MatAutocomplete,
    MatOption,
    AsyncPipe,
    TranslationModule,
    NgForOf,
    MatButton,
    MatDialogClose,
    ReactiveFormsModule,
    MatInput,
    MatMenuItem,
  ],
  templateUrl: './sales-team-dialog.component.html',
  styleUrl: './sales-team-dialog.component.scss',
})
export class SalesTeamDialogComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  form: FormGroup;
  groupCalendars: Calendar[] = [];
  eventTypePageLink = '';
  isLoading = false;
  matAutocompleteDisabled = true;
  filteredTeams$: Observable<Calendar[]>;
  @ViewChild(MatAutocompleteTrigger) autocompleteTrigger!: MatAutocompleteTrigger;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<SalesTeamDialogComponent>,
    private meetingSchedulerStoreService: MeetingSchedulerStoreService,
    @Inject(EVENT_TYPE_PAGE_TOKEN) public readonly eventTypePageLink$: Observable<string>,
  ) {
    this.form = this.fb.group({
      team: [null],
    });
  }

  onInputClick(): void {
    if (!this.autocompleteTrigger.panelOpen) {
      this.autocompleteTrigger.openPanel();
    }
  }

  ngOnInit(): void {
    forkJoin({
      link: this.eventTypePageLink$.pipe(take(1)),
      calendars: this.meetingSchedulerStoreService.loadGroupCalendars().pipe(take(1)),
    })
      .pipe(takeUntil(this.destroy$))
      .subscribe(({ link, calendars }) => {
        this.eventTypePageLink = link;
        this.groupCalendars = calendars;

        this.filteredTeams$ = this.form.get('team')!.valueChanges.pipe(
          startWith(''),
          map((value) => {
            const salesTeam = typeof value === 'string' ? value.toLowerCase() : value?.displayName?.toLowerCase() || '';
            return this.groupCalendars.filter((calendar) => calendar.displayName.toLowerCase().includes(salesTeam));
          }),
        );
      });
  }

  displayTeamName = (calendar: Calendar): string => {
    return calendar?.displayName || '';
  };

  createEvent(): void {
    const selectedCalendar = this.form.get('team')?.value;
    if (!selectedCalendar) return;

    this.isLoading = true;

    this.dialogRef.close({
      calendarId: selectedCalendar.id,
      eventTypePageLink: this.eventTypePageLink,
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
