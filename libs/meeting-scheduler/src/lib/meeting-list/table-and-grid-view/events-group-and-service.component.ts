import { Component, Inject, Input, OnDestroy, OnInit, signal, WritableSignal } from '@angular/core';
import { MatSort, Sort } from '@angular/material/sort';
import { GalaxyFilterChipInjectionToken, GalaxyFilterInterface } from '@vendasta/galaxy/filter/chips';
import { GalaxyDataSource, Row } from '@vendasta/galaxy/table';
import { GalaxyColumnDef } from '@vendasta/galaxy/table/src/table.interface';
import { BehaviorSubject, from, Observable, of, Subject, Subscription } from 'rxjs';
import { CategoryRow, GroupAndServiceService } from './events-group-and-service.service';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Calendar, Group, MeetingType, Service } from '@vendasta/meetings';
import { GroupInterface, ServiceInterface } from '@vendasta/meetings/lib/_internal/interfaces';
import { DeleteBookingLinkConfirmationDialogComponent } from '../delete-booking-link-confirmation-dialog/delete-booking-link-confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { PageEvent } from '@angular/material/paginator';
import { map, startWith } from 'rxjs/operators';
import {
  CALENDAR_PAGE_LINK_TOKEN,
  EVENT_TYPE_PAGE_TOKEN,
  GROUP_PAGE_TOKEN,
  SERVICE_PAGE_TOKEN,
} from '../../data-providers/providers';
import { MeetingSchedulerStoreService } from '../../data-providers/meeting-scheduler-store.service';
import { TranslateService } from '@ngx-translate/core';
import {
  GRID_VIEW,
  LIST_VIEW,
  MEETING_SCHEDULER_CONTEXT_INJECTION_TOKEN$,
  MS_CONTEXT,
  POSTHOG_ACTIONS,
  POSTHOG_CATEGORIES,
  POSTHOG_KEYS,
} from '../../constants';

import { SalesTeamDialogComponent } from './sales-team-dialog/sales-team-dialog.component';
import { ProductAnalyticsService } from '@vendasta/product-analytics';

@Component({
  selector: 'meeting-scheduler-group-and-service',
  templateUrl: './events-group-and-service.component.html',
  styleUrls: ['./events-group-and-service.component.scss'],
  providers: [GroupAndServiceService, { provide: GalaxyFilterChipInjectionToken, useExisting: GroupAndServiceService }],
  standalone: false,
})
export class GroupAndServiceComponent implements OnInit, OnDestroy {
  private subscriptions: Subscription[] = [];
  @Input() selectedToggle: string;
  groupPageLink: string;
  name: string;
  memberCount: string;
  emptyStateTitle: string;
  emptyStateSubtitle: string;
  calendarPageLink: string;
  servicePageLink: string;
  eventTypePageLink: string;
  dataSource: GalaxyDataSource<Row, void, MatSort> = {} as GalaxyDataSource<Row, void, MatSort>;
  calendarLoadError$: Observable<boolean>;
  columns$?: Observable<GalaxyColumnDef[]>;
  isMobileScreen$: Observable<boolean>;
  pageSizeOptions: number[];
  isLoading$ = new BehaviorSubject<boolean>(true);
  pageSize: number;
  data: CategoryRow[] = [];
  noDataIcon = 'https://storage.googleapis.com/meeting-scheduler-prod-public-images/Icon-for-no-team.svg';
  personalCalendar$: Observable<Calendar>;
  groupCalendars$$: Subject<Calendar[]> = new Subject<Calendar[]>();
  selectedGroupCalendarId$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  protected MSContextType = MS_CONTEXT;
  constructor(
    private readonly categoryService: GroupAndServiceService,
    @Inject(CALENDAR_PAGE_LINK_TOKEN) public readonly calendarPageLink$: Observable<string>,
    @Inject(GROUP_PAGE_TOKEN) public readonly groupPageLink$: Observable<string>,
    @Inject(SERVICE_PAGE_TOKEN) public readonly servicePageLink$: Observable<string>,
    @Inject(EVENT_TYPE_PAGE_TOKEN) public readonly eventTypePageLink$: Observable<string>,
    @Inject(MEETING_SCHEDULER_CONTEXT_INJECTION_TOKEN$) protected context: MS_CONTEXT,
    private readonly meetingSchedulerStoreService: MeetingSchedulerStoreService,
    private readonly alertService: SnackbarService,
    private readonly dialog: MatDialog,
    private router: Router,
    private readonly breakpointObserver: BreakpointObserver,
    private readonly translate: TranslateService,
    private readonly analyticsService: ProductAnalyticsService,
  ) {
    this.subscriptions.push(
      calendarPageLink$.subscribe((value) => (this.calendarPageLink = value)),
      groupPageLink$.subscribe((value) => (this.groupPageLink = value)),
      servicePageLink$.subscribe((value) => (this.servicePageLink = value)),
      eventTypePageLink$.subscribe((value) => (this.eventTypePageLink = value)),
    );
    this.isMobileScreen$ = this.breakpointObserver.observe([Breakpoints.XSmall]).pipe(map((result) => result.matches));
  }
  displayOption: WritableSignal<typeof LIST_VIEW | typeof GRID_VIEW> = signal(LIST_VIEW);

  ngOnInit(): void {
    this.displayOption.set(this.meetingSchedulerStoreService.displayOption());
    this.pageSizeOptions = this.meetingSchedulerStoreService.pageSizeOptions;
    this.pageSize = this.meetingSchedulerStoreService.pageSize;
    this.meetingSchedulerStoreService.tableViewEnabled = true;
    this.meetingSchedulerStoreService.selectedToggle = this.selectedToggle;
    this.getTranslations(this.selectedToggle);
    this.loadData(this.selectedToggle);
    this.personalCalendar$ = this.meetingSchedulerStoreService.loadPersonalCalendar();
    const groupCalendars$ = this.meetingSchedulerStoreService.loadGroupCalendars();
    this.subscriptions.push(
      groupCalendars$.subscribe((calendars) => {
        this.groupCalendars$$.next(calendars);
      }),
    );
    this.calendarLoadError$ = this.meetingSchedulerStoreService.getCalendarLoadErrors().pipe(
      map((errorSet) => errorSet.size > 0),
      startWith(false),
    );
  }

  navigateToGroups(groupPageLink: string): void {
    const url = `${groupPageLink}`;
    this.router.navigateByUrl(url);
  }

  handleEventTableCreate(eventTypePageLink: string, calendarId: string): void {
    const url = `${eventTypePageLink}/${calendarId}`;
    this.router.navigateByUrl(url);
  }

  getFilterHeader(selectedToggle?: string): string {
    return selectedToggle
      ? this.translate.instant(`MEETING_SCHEDULER.MEETING_LIST.${selectedToggle.toUpperCase()}`)
      : '';
  }

  getTranslations(selectedToggle?: string): void {
    if (!selectedToggle) return;

    const prefix = `MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.${selectedToggle.toUpperCase()}.`;
    [this.name, this.memberCount, this.emptyStateTitle, this.emptyStateSubtitle] = [
      'NAME',
      'MEMBER_COUNT',
      'EMPTY_STATE_TITLE',
      'EMPTY_STATE_SUBTITLE',
    ].map((key) => `${prefix}${key}`);
  }

  loadData(selectedToggle) {
    this.columns$ = this.categoryService.getColumn$(selectedToggle).pipe(map((cols) => [...cols]));
    this.dataSource = this.categoryService.getDataSource();
    this.dataSource.loading$ = of(true);
    this.categoryService.getDataFromApi(selectedToggle, this.dataSource);
    this.subscriptions.push(
      this.dataSource.state$.subscribe((val) => {
        if (val.initialLoadCompleted) {
          this.data = this.categoryService.ROW_DATA;
        }
      }),
    );
  }

  onFilterChanged(filters: GalaxyFilterInterface[]): void {
    this.categoryService.updateFilers(filters);
  }

  sortData(sort: Sort): void {
    this.dataSource?.setSorting([
      {
        active: sort.active,
        direction: sort.direction,
      } as MatSort,
    ]);
  }

  handleBookingClick(link: string) {
    this.analyticsService.trackEvent(POSTHOG_KEYS.VIEW_EVENT_LINK, POSTHOG_CATEGORIES.USER, POSTHOG_ACTIONS.CLICK);
    window.open(link, '_blank', 'noopener,noreferrer');
  }

  copyToClipboard(link: string): void {
    navigator.clipboard
      .writeText(link)
      .then(() => {
        this.analyticsService.trackEvent(POSTHOG_KEYS.VIEW_EVENT_LINK, POSTHOG_CATEGORIES.USER, POSTHOG_ACTIONS.CLICK);
        this.alertService.openSuccessSnack('MEETING_SCHEDULER.MEETINGS_SHARED.ALERTS.LINK_COPIED');
      })
      .catch((err) => {
        console.error(err);
      });
  }
  openSalesTeamDialog(eventTypePageLink: string, groupCalendars: Calendar[]): void {
    if (this.context === MS_CONTEXT.MS_CONTEXT_SMB) {
      const groupCalendar = groupCalendars[0];
      this.handleEventTableCreate(eventTypePageLink, groupCalendar.id);
      return;
    }

    const dialogRef = this.dialog.open(SalesTeamDialogComponent, {
      width: '400px',
    });

    const dialogSub = dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.handleEventTableCreate(result.eventTypePageLink, result.calendarId);
      }
    });

    this.subscriptions.push(dialogSub);
  }

  onMenuClosed() {
    (document.activeElement as HTMLElement)?.blur();
  }

  onMenuItemHover(event: MouseEvent) {
    (event.target as HTMLElement).blur();
  }

  async togglePin(row) {
    if (row.isDefaultGroup) {
      this.alertService.openWithOptions('MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.PIN.UPDATE.RESTRICTED.ERROR');
      return;
    }
    const isPinned = !row.isPinned;
    if (this.isService(row.mid)) {
      this.updateServiceData(row);
    } else if (this.isGroup(row.mid)) {
      this.updateGroupData(row);
    } else {
      this.updateMeetingData(row);
    }
    this.updateDataSource(false, row.mid, isPinned);
  }

  updateDataSource(isDeleteAction: boolean, id: string, updatedAction?: boolean) {
    let data: CategoryRow[] = this.categoryService.ROW_DATA;
    if (isDeleteAction) {
      data = data.filter((rowData) => rowData.id !== id);
    } else {
      data = data.map((rowData) => (rowData.id === id ? { ...rowData, isPinned: updatedAction } : rowData));
    }
    const sortedData = data.sort((a, b) => Number(b.isPinned) - Number(a.isPinned));

    this.categoryService.ROW_DATA = sortedData; // assigned the modified data to the local variable for pagination
    const pageSize = this.dataSource.state.pageSize;
    const cursor = this.dataSource.state.pageIndex * pageSize;

    const rowData = this.categoryService.getPageDataBasedOnCursor(sortedData, pageSize, cursor);

    this.dataSource.state = {
      ...this.dataSource.state,
      dataMembers: rowData,
    };
  }

  async updateMeetingData(row) {
    row.isPinned = !row.isPinned;

    const updatedMeetingType = {
      id: row.mid,
      calendarId: row.calendarId,
      isPinned: row.isPinned,
    } as MeetingType;
    try {
      const currentDataSource = this.dataSource;
      await this.updateMeetingType(row.mid, updatedMeetingType, ['isPinned']);
      this.dataSource = currentDataSource;
    } catch (err) {
      console.error('Error in updating group:', err);
    }
  }

  async updateMeetingType(id: string, meetingType: MeetingType, updateFields?: (keyof MeetingType)[]): Promise<void> {
    try {
      await this.meetingSchedulerStoreService.updateMeetingType(id, meetingType, updateFields);
    } catch (err) {
      console.error(err);
      this.alertService.openWithOptions('MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.PIN.UPDATE.API.ERROR');
    }
  }

  async updateGroupData(row) {
    const isPinned = !row.isPinned;
    const mt = {
      id: row.mid,
      isPinned: isPinned,
    } as Group;
    try {
      await this.updateGroup(row.mid, mt, ['isPinned']);
    } catch (err) {
      console.error('Error in updating group:', err);
    }
  }

  async updateGroup(id: string, groupData: Group, updateFields?: (keyof GroupInterface)[]): Promise<void> {
    try {
      await this.meetingSchedulerStoreService.updateGroup(id, groupData, updateFields);
    } catch (err) {
      console.error(err);
      this.alertService.openWithOptions('MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.PIN.UPDATE.API.ERROR');
    }
  }

  async updateServiceData(row) {
    const isPinned = !row.isPinned;
    const mt = {
      id: row.mid,
      isPinned: isPinned,
    } as Service;
    try {
      await this.updateService(row.mid, mt, ['isPinned']);
    } catch (err) {
      console.error('Error in updating service:', err);
    }
  }

  async updateService(id: string, data: Service, updateFields?: (keyof ServiceInterface)[]): Promise<void> {
    try {
      await this.meetingSchedulerStoreService.updateService(id, data, updateFields);
    } catch (err) {
      console.error(err);
      this.alertService.openWithOptions('MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.PIN.UPDATE.API.ERROR');
    }
  }

  isGroup(id: string) {
    return id.startsWith('GRP-');
  }

  isService(id: string) {
    return id.startsWith('SVS-');
  }

  handleDeleteClicked(id: string): void {
    this.dialog.open(DeleteBookingLinkConfirmationDialogComponent, {
      maxWidth: '500px',
      data: {
        category: this.selectedToggle,
        entityID: id,
        validateAssociation: !this.isService(id),
        confirmationCallback: () => from(this.deletedata(id)),
      },
    });
  }

  async deletedata(id) {
    if (this.isService(id)) {
      this.deleteService(id);
    } else if (this.isGroup(id)) {
      this.deleteGroup(id);
    } else {
      this.deleteMeetingType(id);
    }
    this.updateDataSource(true, id);
  }

  async deleteMeetingType(id: string): Promise<void> {
    const currentDataSource = this.dataSource;
    try {
      await this.meetingSchedulerStoreService.deleteMeetingType(id);
      this.dataSource = currentDataSource;
      this.analyticsService.trackEvent(POSTHOG_KEYS.EVENT_TYPE_DELETED, POSTHOG_CATEGORIES.USER, POSTHOG_ACTIONS.CLICK);
      this.alertService.openSuccessSnack('MEETING_SCHEDULER.MEETING_LIST.ALERTS.DELETE_BOOKING_LINK_SUCCESS');
    } catch (err) {
      this.alertService.openErrorSnack('MEETING_SCHEDULER.MEETING_LIST.ALERTS.DELETE_BOOKING_LINK_ERROR');
      console.error(err);
    }
  }

  async deleteGroup(id: string): Promise<void> {
    try {
      await this.meetingSchedulerStoreService.deleteGroup(id);
      this.analyticsService.trackEvent(POSTHOG_KEYS.GROUP_DELETED, POSTHOG_CATEGORIES.USER, POSTHOG_ACTIONS.CLICK);
      this.alertService.openSuccessSnack('MEETING_SCHEDULER.MEETING_LIST.ALERTS.DELETE_GROUP_SUCCESS');
    } catch (err) {
      this.alertService.openErrorSnack('MEETING_SCHEDULER.MEETING_LIST.ALERTS.DELETE_GROUP_ERROR');
      console.error(err);
    }
  }

  async deleteService(id: string): Promise<void> {
    try {
      await this.meetingSchedulerStoreService.deleteService(id);
      this.analyticsService.trackEvent(POSTHOG_KEYS.SERVICE_DELETED, POSTHOG_CATEGORIES.USER, POSTHOG_ACTIONS.CLICK);
      this.alertService.openSuccessSnack('MEETING_SCHEDULER.MEETING_LIST.ALERTS.DELETE_GROUP_SUCCESS');
    } catch (err) {
      this.alertService.openErrorSnack('MEETING_SCHEDULER.MEETING_LIST.ALERTS.DELETE_GROUP_ERROR');
      console.error(err);
    }
  }

  navigateToEditURL(mid: string): void {
    this.router.navigate([`${this.calendarPageLink}/${mid}`]);
  }

  handleView(row): void {
    let url;
    if (this.isService(row.mid)) {
      url = `${this.servicePageLink}/${row.mid}`;
    } else if (this.isGroup(row.mid)) {
      url = `${this.groupPageLink}/${row.mid}`;
    } else {
      url = `${this.eventTypePageLink}/${row.calendarId}/${row.meetingTypeSlug}`;
    }
    this.router.navigateByUrl(url);
  }

  displayChanges(event) {
    this.meetingSchedulerStoreService.displayOption.set(event === GRID_VIEW ? GRID_VIEW : LIST_VIEW);
    if (event === LIST_VIEW) {
      this.pageSizeOptions = [5, 10, 15];
      this.pageSize = 10;
    } else {
      this.pageSizeOptions = [6, 9, 15];
      this.pageSize = 9;
    }
    this.meetingSchedulerStoreService.pageSizeOptions = this.pageSizeOptions;
    this.meetingSchedulerStoreService.pageSize = this.pageSize;
    const pageChanged: PageEvent = {
      pageIndex: 0,
      pageSize: this.pageSize,
      length: this.categoryService.ROW_DATA.length,
    };
    this.dataSource.pageChanged(pageChanged);
    this.displayOption.set(event);
  }

  getTooltip(name: string, limit: number): string | null {
    return name?.length > limit ? name : null;
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
    this.subscriptions = [];
  }
}
