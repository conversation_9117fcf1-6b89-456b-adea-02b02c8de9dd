import { Inject, Injectable } from '@angular/core';
import { MatSort, Sort } from '@angular/material/sort';
import { GalaxyFilterOperator, GalaxyFilterType } from '@vendasta/galaxy/filter/chips';
import {
  GalaxyFilterChipDependencies,
  GalaxyFilterDefinitionInterface,
  GalaxyFilterInterface,
} from '@vendasta/galaxy/filter/chips/src/interface';
import {
  CustomData,
  CustomDataValue,
  GalaxyColumnDef,
  GalaxyDataSource,
  PagedListRequestInterface,
  PagedResponseInterface,
  Row,
  RowData,
} from '@vendasta/galaxy/table';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { MeetingListLoadDataService } from './load-data-service.service';
import { EVENTS, GROUP, MEETING_SCHEDULER_CONTEXT_INJECTION_TOKEN$, MS_CONTEXT, SERVICE } from '../../constants';
import { COLUMNS, FILTERS_EVENT_TYPE, FILTERS_GRP_AND_SVS } from './filters-and-columns-contants';
import { MeetingSchedulerStoreService } from '../../data-providers/meeting-scheduler-store.service';

export interface CategoryRow {
  id?: string;
  name?: string;
  bookingUrl?: string;
  eventCount?: number;
  isPinned?: boolean;
  associations?: any[];
  description?: string;
  isDefaultGroup?: boolean;
  hexColor?: string;
  duration?: string;
  eventType?: string;
  teamName?: string;
  calendarId?: string;
  meetingTypeSlug?: string;
}

@Injectable()
export class GroupAndServiceService implements GalaxyFilterChipDependencies {
  data: Record<string, unknown> = {};
  cursors: string[] = [];
  pageSize = 0;

  private filters$$ = new BehaviorSubject<GalaxyFilterInterface[]>([]);
  columns: GalaxyColumnDef[] = COLUMNS;
  ROW_DATA: CategoryRow[];
  selectedToggle: string = this.meetingSchedulerStoreService.selectedToggle;

  constructor(
    private dataService: MeetingListLoadDataService,
    private readonly meetingSchedulerStoreService: MeetingSchedulerStoreService,
    @Inject(MEETING_SCHEDULER_CONTEXT_INJECTION_TOKEN$) protected context: MS_CONTEXT,
  ) {}

  getColumn$(selectedToggle): Observable<GalaxyColumnDef[]> {
    this.selectedToggle = selectedToggle;
    this.columns = COLUMNS.filter((column) => {
      if (selectedToggle === EVENTS) {
        if (this.context === MS_CONTEXT.MS_CONTEXT_SMB) {
          return column.id !== 'eventCount' && column.id !== 'description' && column.id !== 'teamName';
        }
        return column.id !== 'eventCount' && column.id !== 'description';
      } else {
        if (column.id === 'eventCount' && selectedToggle === GROUP) {
          column.title = 'Event type count';
        } else if (column.id === 'eventCount' && selectedToggle === SERVICE) {
          column.title = 'Member count';
        }
        return column.id !== 'duration' && column.id !== 'eventType' && column.id !== 'teamName';
      }
    });
    return of(this.columns);
  }

  getDataSource(): GalaxyDataSource<Row, void, MatSort> {
    const paginatedAPI = {
      get: (req: PagedListRequestInterface<void, MatSort>): Observable<PagedResponseInterface<Row>> => {
        const { searchOptions } = req;
        const { text } = searchOptions || { text: '' };
        return this.filters$$.pipe(
          map((filters) => {
            const filteredRows = this.frontendFilter(filters);
            let sortedRows: CategoryRow[] = [];
            if (req.sorting) {
              sortedRows = this.sortData(req.sorting[0], filteredRows);
            }
            const searchFilteredRows = this.getSearchFilteredRows(sortedRows, text);
            const cursorAsNumber: number = req.pagingOptions?.cursor ? Number(req.pagingOptions.cursor) : 0;
            const pageSize = req.pagingOptions?.pageSize || 0;
            const hasMore: boolean = cursorAsNumber + pageSize < searchFilteredRows.length;
            const nextCursor: number = cursorAsNumber + pageSize;

            return {
              data: this.getPageDataBasedOnCursor(searchFilteredRows, pageSize, cursorAsNumber),
              pagingMetadata: {
                hasMore: hasMore,
                nextCursor: nextCursor.toString(),
              },
            };
          }),
        );
      },
    };
    return new GalaxyDataSource<Row, void, MatSort>(paginatedAPI);
  }

  getDataFromApi(selectedToggle: string, dataSource: GalaxyDataSource<Row, void, MatSort>) {
    this.dataService.loadGroupOrServiceData(selectedToggle).subscribe((rowData) => {
      this.ROW_DATA = rowData;
      dataSource.loading$ = of(false);
      dataSource.state = {
        ...dataSource.state,
        shouldRefetch: true,
        loading: false,
      };
    });
  }

  getPageDataBasedOnCursor(searchFilteredRows: CategoryRow[], pageSize, cursorAsNumber: number): Row[] {
    const pageRows: Row[] = [];
    for (
      let rowIndex = cursorAsNumber;
      rowIndex < Math.min(cursorAsNumber + pageSize, searchFilteredRows.length);
      rowIndex++
    ) {
      const rowData: RowData = {};
      for (let columnIndex = 0; columnIndex < this.columns.length; columnIndex++) {
        if (['name', 'bookingUrl', 'description'].includes(this.columns[columnIndex].id)) {
          continue;
        }
        rowData[this.columns[columnIndex].id] = (searchFilteredRows as Record<string, any>)[rowIndex][
          this.columns[columnIndex].id
        ];
      }
      const row = {
        id: `ROW-${rowIndex}`,
        data: rowData,
        mid: searchFilteredRows[rowIndex].id,
        calendarId: searchFilteredRows[rowIndex].calendarId,
        meetingTypeSlug: searchFilteredRows[rowIndex].meetingTypeSlug,
        name: searchFilteredRows[rowIndex].name,

        bookingUrl: searchFilteredRows[rowIndex].bookingUrl,
        isPinned: searchFilteredRows[rowIndex].isPinned,
        eventCount: searchFilteredRows[rowIndex].associations ? searchFilteredRows[rowIndex].associations.length : 0,
        description: searchFilteredRows[rowIndex].description ? searchFilteredRows[rowIndex].description : '-',
        isDefaultGroup: searchFilteredRows[rowIndex].isDefaultGroup
          ? searchFilteredRows[rowIndex].isDefaultGroup
          : false,
        hexColor: searchFilteredRows[rowIndex].hexColor ? searchFilteredRows[rowIndex].hexColor : '#EE5353',
        duration: searchFilteredRows[rowIndex].duration,
        eventType: searchFilteredRows[rowIndex].eventType,
        teamName: searchFilteredRows[rowIndex].teamName ? searchFilteredRows[rowIndex].teamName : '-',
      };
      pageRows.push(row);
    }
    return pageRows;
  }

  getSearchFilteredRows(rows: CategoryRow[], searchText: string): CategoryRow[] {
    return rows.filter((row) => {
      if (!searchText) {
        return true;
      }

      searchText = searchText.toLowerCase();

      return Object.keys(row).some((key: string) => {
        const prop = (row as Record<string, any>)[key];
        // check if model-driven cell (discriminate for CellData interface)
        if (typeof prop === 'object' && 'value' in prop && 'cellType' in prop) {
          return prop['value'].toString().toLowerCase().includes(searchText);
        }
        return prop.toString().toLowerCase().includes(searchText);
      });
    });
  }

  updateFilers(filters: GalaxyFilterInterface[]): void {
    this.filters$$.next(filters);
  }

  // getFieldOptions allows you to return values for autocompleting a filter.
  // Just return of([]) if you have no autocomplete.
  // This could come from an API or be filtered on the frontend for small, known, datasets.
  getFieldOptions(fieldId: string, search: string): Observable<string[]> {
    const options = this.ROW_DATA.reduce((acc, curr) => {
      const val = curr[fieldId as keyof CategoryRow];
      if (val && (val instanceof String || val instanceof Number || val instanceof Boolean)) {
        return [val, ...acc];
      }
      return acc;
    }, new Array<any>());
    const uniqueOptions = options.filter((val, index, arr) => arr.indexOf(val) === index);
    return of(uniqueOptions.filter((option) => (option as string).toLowerCase().includes(search.toLowerCase())));
  }

  // getInitialAppliedFilters lets you specify what filters should be shown when they are initially loaded.
  // To provide a 'preset' filter that does not have a default value, specify preset:true in the filter definition.
  // The filters must be given unique ids.
  async getInitialAppliedFilters(): Promise<GalaxyFilterInterface[]> {
    return [];
  }

  // listObjectFilters is used to return the filter options. A searchTerm may be given to limit the filters to show.
  // This can be filtered via API or simply in the frontend.
  listObjectFilters(searchTerm: string): Observable<GalaxyFilterDefinitionInterface[]> {
    if (this.selectedToggle === EVENTS) {
      return of(
        FILTERS_EVENT_TYPE.filter((filter) => {
          return (
            !(this.context === MS_CONTEXT.MS_CONTEXT_SMB && filter.fieldId === 'teamName') &&
            filter.fieldName?.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }),
      );
    } else {
      return of(
        FILTERS_GRP_AND_SVS.map((filter) => ({
          ...filter,
          fieldName:
            filter.fieldId === 'eventCount' && this.selectedToggle === SERVICE ? 'Member Count' : filter.fieldName,
        })).filter((filter) => filter.fieldName?.toLowerCase().includes(searchTerm.toLowerCase())),
      );
    }
  }

  getFlatValue(val: string | CustomData): CustomDataValue {
    if (typeof val === 'string') {
      return val.toLowerCase();
    }
    if ('value' in val) {
      const flat = val.value;
      if (typeof flat === 'string') {
        return flat.toLowerCase();
      }
      return flat;
    }
    return '';
  }

  private frontendFilter(filters: GalaxyFilterInterface[]): CategoryRow[] {
    if (!this.ROW_DATA) {
      return [];
    }
    return this.ROW_DATA.filter((row) => {
      return filters.every((filter) => {
        let val: any = row[filter.fieldId as keyof CategoryRow];
        if (filter.fieldId === 'eventCount' && Array.isArray(row['associations'])) {
          val = row['associations' as keyof CategoryRow];
        }
        const filterValues = filter.values;
        let definition;
        if (this.selectedToggle === EVENTS) {
          definition = FILTERS_EVENT_TYPE.find((f) => f.fieldId === filter.fieldId);
        } else {
          definition = FILTERS_GRP_AND_SVS.find((f) => f.fieldId === filter.fieldId);
        }
        if (!definition) {
          return true;
        }
        let values;
        switch (definition.type) {
          case GalaxyFilterType.FILTER_TYPE_STRING:
            values = filterValues?.map((value) => value.string?.toLowerCase());
            break;
          case GalaxyFilterType.FILTER_TYPE_INTEGER:
            values = filterValues?.map((value) => value.integer);
            break;
        }
        if (val === undefined) {
          return true;
        }
        let flatValue = this.getFlatValue(val);
        if (definition.fieldId === 'duration') {
          if (typeof flatValue === 'string') {
            const extractedInt = flatValue.match(/\d+/); // Extract first integer occurrence
            flatValue = extractedInt ? parseInt(extractedInt[0], 10) : flatValue.toLowerCase();
          }
        }
        if (definition.fieldId === 'eventCount') {
          flatValue = val.length;
        }
        const anyValues = values as any[]; // cheat and make the below comparison easier
        switch (filter.operator) {
          case GalaxyFilterOperator.FILTER_OPERATOR_IS:
            return anyValues?.includes(flatValue);
          case GalaxyFilterOperator.FILTER_OPERATOR_IS_NOT:
            return !anyValues?.includes(flatValue);
          case GalaxyFilterOperator.FILTER_OPERATOR_IS_EMPTY:
            return !flatValue;
          case GalaxyFilterOperator.FILTER_OPERATOR_IS_NOT_EMPTY:
            return Boolean(flatValue);
          case GalaxyFilterOperator.FILTER_OPERATOR_CONTAINS:
            return typeof flatValue === 'string' && anyValues?.some((value) => flatValue.includes(value));
          case GalaxyFilterOperator.FILTER_OPERATOR_DOES_NOT_CONTAIN:
            return !(typeof flatValue === 'string' && anyValues?.some((value) => flatValue.includes(value)));
          case GalaxyFilterOperator.FILTER_OPERATOR_IS_GREATER_THAN:
            return anyValues?.some((value) => flatValue > value);
          case GalaxyFilterOperator.FILTER_OPERATOR_IS_GREATER_THAN_OR_EQUAL_TO:
            return anyValues?.some((value) => flatValue >= value);
          case GalaxyFilterOperator.FILTER_OPERATOR_IS_LESS_THAN:
            return anyValues?.some((value) => flatValue < value);
          case GalaxyFilterOperator.FILTER_OPERATOR_IS_LESS_THAN_OR_EQUAL_TO:
            return anyValues?.some((value) => flatValue <= value);
          case GalaxyFilterOperator.FILTER_OPERATOR_IS_EQUAL_TO:
            return anyValues?.some((value) => flatValue == value);
          case GalaxyFilterOperator.FILTER_OPERATOR_IS_BETWEEN:
            return anyValues?.some((value) => flatValue >= value && flatValue <= value);
          case GalaxyFilterOperator.FILTER_OPERATOR_IS_NOT_BETWEEN:
            return !anyValues?.some((value) => flatValue >= value && flatValue <= value);
          case GalaxyFilterOperator.FILTER_OPERATOR_IS_AFTER_OR_ON:
            return flatValue instanceof Date && anyValues?.some((value) => flatValue.getTime() >= value?.getTime());
          case GalaxyFilterOperator.FILTER_OPERATOR_IS_AFTER:
            return flatValue instanceof Date && anyValues?.some((value) => flatValue.getTime() > value?.getTime());
          case GalaxyFilterOperator.FILTER_OPERATOR_IS_BEFORE_OR_ON:
            return flatValue instanceof Date && anyValues?.some((value) => flatValue.getTime() <= value?.getTime());
          case GalaxyFilterOperator.FILTER_OPERATOR_IS_BEFORE:
            return flatValue instanceof Date && anyValues?.some((value) => flatValue.getTime() < value?.getTime());
        }
      });
    });
  }

  sortData(sort: Sort, rows: CategoryRow[]): CategoryRow[] {
    if (!sort) {
      return rows;
    }
    if (!sort.active || sort.direction === '') {
      return rows;
    }
    const isAsc = sort.direction === 'asc';
    return customSort(rows, sort.active, isAsc);
  }
}

function customSort(data: any[], sortColumn: string, asc: boolean): any[] {
  return data.slice().sort((a, b) => {
    let aValue = a[sortColumn];
    let bValue = b[sortColumn];

    if (['name', 'description', 'eventType', 'teamName'].includes(sortColumn)) {
      return asc ? (aValue || '').localeCompare(bValue || '') : (bValue || '').localeCompare(aValue || '');
    }

    if (sortColumn === 'eventCount') {
      aValue = a['associations']?.length || 0;
      bValue = b['associations']?.length || 0;
    }

    if (sortColumn === 'duration') {
      const extractMinutes = (val: string): number => {
        const match = val?.match(/\d+/); // Extract numeric value
        return match ? parseInt(match[0], 10) : 0;
      };
      aValue = extractMinutes(aValue);
      bValue = extractMinutes(bValue);
    }

    return asc ? aValue - bValue : bValue - aValue;
  });
}
