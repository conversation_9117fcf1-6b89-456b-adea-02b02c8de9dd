import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Inject,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  AbstractControl,
  AsyncValidatorFn,
  UntypedFormBuilder,
  UntypedFormControl,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Calendar, HostUser, MeetingType, Group, EventGroupAndServiceAssociations, Service } from '@vendasta/meetings';

import { Observable, Subscription, combineLatest, BehaviorSubject, firstValueFrom, from, of, Subject } from 'rxjs';
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  first,
  map,
  shareReplay,
  switchMap,
  take,
  tap,
} from 'rxjs/operators';
import { v4 as uuidv4 } from 'uuid';
import { GroupMeetingType, MeetingSchedulerStoreService } from '../data-providers/meeting-scheduler-store.service';
import { HOST_ID_TOKEN, MEETING_TYPE_LIST_VIEW_PAGE_TOKEN } from '../data-providers/providers';
import { UpdateConfirmationDialogComponent } from '../shared/components/update-confirmation-dialog/update-confirmation-dialog.component';
import { MatSelect } from '@angular/material/select';
import {
  GROUP,
  SERVICE,
  PERSONAL_EVENT_TYPE,
  TEAM_EVENT_TYPE,
  MS_CONTEXT,
  MEETING_SCHEDULER_CONTEXT_INJECTION_TOKEN$,
  POSTHOG_KEYS,
  POSTHOG_CATEGORIES,
  POSTHOG_ACTIONS,
} from '../constants';
import { ProductAnalyticsService } from '@vendasta/product-analytics';

interface ServiceType {
  id: string;
  calendarId: string;
  name: string;
  type: string;
  creator?: string;
}

@Component({
  selector: 'meeting-scheduler-service',
  templateUrl: './service.component.html',
  styleUrl: './service.component.scss',
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ServiceComponent implements OnInit, OnDestroy {
  buttonText: string;
  linkPrefix$: Observable<string>;

  readonly colors = [
    '#EE5353',
    '#F778B4',
    '#E27EFF',
    '#8988FC',
    '#4A91E9',
    '#0BC0D7',
    '#34C66F',
    '#67C820',
    '#DFC12C',
    '#F49A31',
  ];
  public originalSlug: string;
  private usedSlugs: string[] = [];
  private readonly slugRandomness = uuidv4().split('-')[0];

  form = this.formBuilder.group({
    id: this.formBuilder.control(''),
    name: this.formBuilder.control('', [Validators.required]),
    description: this.formBuilder.control(''),
    color: this.formBuilder.control(this.colors[0]),
    slug: this.formBuilder.control(
      '',
      [Validators.required, Validators.pattern('[a-zA-Z0-9_-]+')],
      [this.uniqueSlug()],
    ),
  });

  events = this.formBuilder.group({
    eventList: new UntypedFormControl(null),
    groupList: new UntypedFormControl(null),
  });

  isUpdate = false;
  isLoading$ = new BehaviorSubject<boolean>(true);
  private service: Service;
  isButtonLoading$ = new Subject<boolean>();
  meetingId;
  timezone;

  calendar: Calendar | null = null;

  hostUserIds: string[] = [];
  serviceId: string;
  serviceName: string;
  hostid: string;
  businessId: string;
  partnerid: string;
  unmatchedAssociations: ServiceType[] = [];
  selectedOptions: ServiceType[] = [];
  listMeetingTypes: ServiceType[] = [];
  listGroups: ServiceType[] = [];

  personalMeetingTypes$: Observable<MeetingType[]>;
  groupMeetingTypes$: Observable<{ [calendarId: string]: GroupMeetingType[] }>;
  allGroupCalendarIds$: Observable<string[]>;
  allGroupMeetingTypes$: Observable<MeetingType[]>;
  groupCalendars$: Observable<Calendar[]>;
  isAssociationsNotSelected = false;

  private readonly subscriptions: Subscription[] = [];
  showAlert$: Observable<boolean>;

  @ViewChild('groupSelect') groupSelect!: MatSelect;
  @ViewChild('eventSelect') eventSelect!: MatSelect;

  constructor(
    @Inject(MEETING_TYPE_LIST_VIEW_PAGE_TOKEN) public readonly meetingTypeListViewPage$: Observable<string>,
    @Inject(HOST_ID_TOKEN) private readonly _hostId$: Observable<string>,
    @Inject('PARTNER_ID') readonly partnerId$: Observable<string>,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly translate: TranslateService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly storeService: MeetingSchedulerStoreService,
    private readonly alerts: SnackbarService,
    private readonly dialog: MatDialog,
    private readonly meetingSchedulerStoreService: MeetingSchedulerStoreService,
    private cdr: ChangeDetectorRef,
    @Inject(MEETING_SCHEDULER_CONTEXT_INJECTION_TOKEN$) protected context: MS_CONTEXT,
    private readonly analyticsService: ProductAnalyticsService,
  ) {}

  async ngOnInit() {
    try {
      this.subscriptions.push(
        this.meetingSchedulerStoreService.loadPersonalCalendar().subscribe((calendar) => (this.calendar = calendar)),
      );
      this.initializeServiceDetails();
      await this.loadInitialData();
      this.setupFormListeners();
      if (this.isUpdate) this.loadServiceDetails();
      else this.loadGroups();
      this.loadData();
    } catch (error) {
      console.error('Error during initialization:', error);
      this.isLoading$.next(false);
    }
  }

  private initializeServiceDetails() {
    this.meetingSchedulerStoreService.tableViewEnabled = true;
    this.meetingSchedulerStoreService.selectedToggle = SERVICE;
    this.serviceId = this.route.snapshot.params.serviceId || '';
    this.isUpdate = this.serviceId !== '';
    this.buttonText = this.translate.instant(
      this.isUpdate
        ? 'MEETING_SCHEDULER.BOOKING_SERVICE.UPDATE_BUTTON'
        : 'MEETING_SCHEDULER.BOOKING_SERVICE.CREATE_BUTTON',
    );
  }

  private async loadInitialData() {
    this.hostid = await firstValueFrom(this._hostId$);
    this.groupCalendars$ = this.storeService.loadGroupCalendars();
    this.linkPrefix$ = this.getFormattedLinkPrefix();
  }

  private getFormattedLinkPrefix(): Observable<string> {
    return this.storeService.loadPersonalGeneralBookingLink().pipe(
      map((url) => {
        const parts = url.replace(/https?:\/\//, '').split('/');
        return (parts.length > 0 ? parts[0] : '') + '/you/';
      }),
    );
  }

  private setupFormListeners() {
    this.subscriptions.push(
      this.form.controls.name.valueChanges
        .pipe(debounceTime(300), distinctUntilChanged())
        .subscribe(() => this.updateSlugIfNeeded()),
    );
  }

  private updateSlugIfNeeded() {
    const { name, slug } = this.form.controls;
    if (this.isUpdate || slug.dirty) return;

    let generatedSlug = name.value
      .toLowerCase()
      .trim()
      .replace(/\s+/g, '-')
      .replace(/[^0-9a-zA-Z_-]/g, '');

    this.subscriptions.push(
      this.isSlugExist(generatedSlug)
        .pipe(first())
        .subscribe((result) => {
          if (result?.unique) {
            generatedSlug += `-${this.slugRandomness}`;
          }
          this.form.controls.slug.patchValue(generatedSlug, { emitEvent: false });
        }),
    );
  }

  private loadServiceDetails() {
    this.subscriptions.push(
      this.storeService
        .getService(this.serviceId, '')
        .pipe(first())
        .subscribe((service) => {
          if ('service' in service) {
            // If service is of type GetServiceResponse, extract the actual Service object
            this.service = service.service;
          } else {
            // Otherwise, it is already of type Service
            this.service = service;
          }
          this.loadGroups();
        }),
    );
  }

  private loadGroups() {
    this.subscriptions.push(
      this.storeService
        .loadGroups()
        .pipe(first())
        .subscribe((groups) => {
          this.populateFormWithServiceData(groups);
        }),
    );
  }

  private populateFormWithServiceData(groups: Group[]) {
    this.listGroups = this.getDefaultOptions(groups, GROUP);
    if (!this.service) return;
    this.meetingId = this.service.id;
    this.originalSlug = this.service.slug;
    this.serviceName = this.service.name;
    this.form.patchValue({
      id: this.service.id,
      name: (this.service.name || '').trim(),
      description: (this.service.description || '').trim(),
      color: this.service.hexColor,
      slug: this.service.slug,
    });

    const associations = this.mapAssociations(this.service.associations);
    this.listGroups = this.mapOptionsWithAssociations(this.listGroups, associations);

    const selectedAssociations = this.getSelectedAssociations(this.listGroups, associations);
    this.events.patchValue({
      groupList: selectedAssociations || [],
    });
    this.selectedOptions = [
      ...new Map([...this.selectedOptions, ...selectedAssociations].map((item) => [item.id, item])).values(),
    ];
  }

  private getDefaultOptions(options: any[], eventType?: string): ServiceType[] {
    return options.map(({ id, calendarId, name, CalendarType, calendarName }) => ({
      id,
      calendarId,
      name,
      type: eventType ?? CalendarType,
      creator:
        this.context === MS_CONTEXT.MS_CONTEXT_PARTNER && CalendarType === TEAM_EVENT_TYPE
          ? calendarName
          : CalendarType === PERSONAL_EVENT_TYPE && eventType !== GROUP && calendarId === this.calendar.id
            ? this.calendar.displayName
            : null,
    }));
  }

  private mapAssociations(associations: EventGroupAndServiceAssociations[]): ServiceType[] {
    return associations.map(({ id, calendarId, name, identifier, eventType, creatorUserName: creator }) => ({
      id,
      calendarId,
      name,
      type: identifier === GROUP ? GROUP : eventType,
      creator,
    }));
  }

  private mapOptionsWithAssociations(options: any[], associations: ServiceType[]): ServiceType[] {
    const associationMap = new Map(associations.map((assoc) => [assoc.id, assoc]));
    return options.map((option) => {
      const matchingAssociation = associationMap.get(option.id);
      return {
        ...option,
        creator:
          option.type === PERSONAL_EVENT_TYPE
            ? (matchingAssociation?.creator ??
              option.creator ??
              this.translate.instant('MEETING_SCHEDULER.MEETING_LIST.UNKNOWN_USER'))
            : option.creator,
      };
    });
  }

  private getSelectedAssociations(options: any[], associations: ServiceType[]): ServiceType[] {
    const associationIds = new Set(associations.map(({ id }) => id));
    return options.filter(({ id }) => associationIds.has(id));
  }

  loadData(): void {
    try {
      this.personalMeetingTypes$ = this.loadSortedPersonalMeetingTypes();
      this.groupMeetingTypes$ = this.meetingSchedulerStoreService.loadGroupMeetingTypes({ returnCache: true });

      this.allGroupCalendarIds$ = this.getAllGroupCalendarIds();
      this.allGroupMeetingTypes$ = this.getAllGroupMeetingTypes();

      this.subscriptions.push(
        this.combineMeetingTypes()
          .pipe(shareReplay(1))
          .subscribe({
            next: (combinedMeetingTypes) => {
              const associations = this.mapAssociations(this.service?.associations || []);
              this.listMeetingTypes = this.mapOptionsWithAssociations(
                this.getDefaultOptions(combinedMeetingTypes),
                associations,
              );
              const selectedAssociations = this.getSelectedAssociations(this.listMeetingTypes, associations);
              this.events.patchValue({
                eventList: selectedAssociations || [],
              });
              this.unmatchedAssociations = associations.filter(
                ({ id, type }) =>
                  !this.listMeetingTypes.some(({ id: eventTypeId }) => eventTypeId === id) && type !== GROUP,
              );

              const orderMap = new Map<string, number>();
              associations.forEach((item, index) => orderMap.set(item.id, index));
              const mergedMap = new Map(
                [...this.selectedOptions, ...selectedAssociations, ...this.unmatchedAssociations].map((item) => [
                  item.id,
                  item,
                ]),
              );
              this.selectedOptions = [...mergedMap.values()].sort(
                (a, b) =>
                  (orderMap.get(a.id) ?? Number.MAX_SAFE_INTEGER) - (orderMap.get(b.id) ?? Number.MAX_SAFE_INTEGER),
              );
              this.isLoading$.next(false);
            },
            error: (err) => {
              console.error(err);
            },
          }),
      );
    } catch (error) {
      console.error('Error in loadData:', error);
      this.isLoading$.next(false);
    }
  }

  private loadSortedPersonalMeetingTypes(): Observable<MeetingType[]> {
    return this.meetingSchedulerStoreService
      .loadPersonalMeetingTypes({ returnCache: false })
      .pipe(
        map((meetingTypes) =>
          meetingTypes
            ? meetingTypes
                .map((mt) => ({ ...mt, CalendarType: PERSONAL_EVENT_TYPE }) as MeetingType)
                .sort((a, b) => a.name.localeCompare(b.name, undefined, { numeric: true }))
            : null,
        ),
      );
  }

  private getAllGroupCalendarIds(): Observable<string[]> {
    return this.groupCalendars$.pipe(
      map((calendars) => calendars?.map((calendar) => calendar.id)),
      filter((ids) => ids?.length > 0),
    );
  }

  private getAllGroupMeetingTypes(): Observable<MeetingType[]> {
    return this.groupMeetingTypes$.pipe(
      switchMap((groupMeetingTypes) => {
        const allMeetings = Object.keys(groupMeetingTypes).reduce(
          (acc, calendarId) => acc.concat(groupMeetingTypes[calendarId] || []),
          [] as MeetingType[],
        );
        return of(allMeetings);
      }),
      shareReplay(1),
    );
  }

  private combineMeetingTypes(): Observable<MeetingType[]> {
    return combineLatest([this.personalMeetingTypes$, this.allGroupMeetingTypes$]).pipe(
      map(([personal, group]) => [...(personal || []), ...(group || [])]),
    );
  }

  get slugFormControl() {
    return this.form.get('slug');
  }

  get eventList() {
    return this.events.get('eventList');
  }

  get groupList() {
    return this.events.get('groupList');
  }

  get personalEventTypes() {
    return this.listMeetingTypes?.filter((mt) => mt.type === PERSONAL_EVENT_TYPE) || [];
  }

  get teamEventTypes() {
    return this.listMeetingTypes?.filter((mt) => mt.type === TEAM_EVENT_TYPE) || [];
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }
  onSelectionChange(_event: any): void {
    this.selectedOptions = [
      ...new Map(
        [...this.unmatchedAssociations, ...(this.groupList?.value || []), ...(this.eventList?.value || [])].map(
          (item) => [item.id, item],
        ),
      ).values(),
    ];
  }

  onDelete(index: number, eventType: string): void {
    const [deletedOption] = this.selectedOptions.splice(index, 1);
    if (!deletedOption) return;
    this.unmatchedAssociations = this.unmatchedAssociations.filter((assoc) => assoc.id === deletedOption.id);

    if (eventType === GROUP) {
      this.groupList.setValue(this.groupList.value?.filter((group) => group.id !== deletedOption.id) || []);
    } else {
      this.eventList.setValue(this.eventList.value?.filter((event) => event.id !== deletedOption.id) || []);
    }
  }

  onDrop(event: CdkDragDrop<string[]>): void {
    const droppedItem = event.item.data;
    const previousIndex = this.selectedOptions.findIndex((item) => item === droppedItem);
    const currentIndex = event.currentIndex;

    moveItemInArray(this.selectedOptions, previousIndex, currentIndex);
    this.cdr.detectChanges();
  }

  getBadgeColor(calendarType: string): 'light-grey' | 'blue' | 'green' | 'yellow' {
    switch (calendarType) {
      case PERSONAL_EVENT_TYPE:
        return 'green';
      case TEAM_EVENT_TYPE:
        return 'blue';
      case GROUP:
        return 'yellow';
      default:
        return 'light-grey';
    }
  }

  get slugChanged(): boolean {
    return this.isUpdate && this.originalSlug !== (this.form.controls.slug.value as string);
  }

  uniqueSlug(): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      if (!control.value || control.value === this.originalSlug || !control.dirty) {
        return of(null);
      }
      return this.isSlugExist(control.value);
    };
  }

  isSlugExist(value: string) {
    return this.meetingSchedulerStoreService.checkGroupOrServiceSlugExist(value, SERVICE).pipe(
      debounceTime(500),
      distinctUntilChanged(),
      take(1),
      map((taken: boolean) => (taken ? { unique: true } : null)),
      catchError(() => {
        console.error('API Error in slug validation');
        return of(null);
      }),
      tap(() => this.cdr.detectChanges()),
    );
  }

  select(event: boolean, hostUser: HostUser): void {
    if (this.hostUserIds.indexOf(hostUser.userId) < 0) {
      this.hostUserIds.push(hostUser.userId);
    } else {
      this.hostUserIds = this.hostUserIds.filter((userId) => userId !== hostUser.userId);
    }
  }

  revertSlug(): void {
    this.form.controls.slug.setValue(this.originalSlug);
  }

  setColor(color: string): void {
    this.form.controls.color.setValue(color);
  }

  private buildServiceData(): Service {
    const { id, name, slug, description, color } = this.form.controls;

    return {
      id: id.value as string,
      name: name.value as string,
      slug: slug.value as string,
      description: description.value as string,
      hexColor: color.value as string,
      bookingUrl: slug.value as string,
      associations: this.selectedOptions.map((option) => ({
        id: option.id,
        identifier: option.type === GROUP ? option.type : 'event',
      })),
    } as Service;
  }

  private async openSlugUpdateDialog(service: Service): Promise<void> {
    const dialogRef = this.dialog.open(UpdateConfirmationDialogComponent, {
      maxWidth: '500px',
      data: {
        type: SERVICE,
        originalSlug: this.originalSlug,
        newSlug: service.slug,
        confirmationCallback: () => from(this.updateService(service.id, service)),
      },
    });

    await dialogRef.afterClosed().toPromise();
  }

  private async processUpdate(service: Service): Promise<void> {
    if (this.originalSlug === service.slug) {
      await this.updateService(service.id, service);
      return;
    }

    await this.openSlugUpdateDialog(service);
  }

  async createOrUpdateService(): Promise<void> {
    if (this.form.invalid || this.selectedOptions.length < 2) {
      this.form.markAllAsTouched();
      this.events.markAllAsTouched();

      if (this.selectedOptions.length < 2) {
        this.isAssociationsNotSelected = true;
      }
      return;
    }
    this.isButtonLoading$.next(true);
    const service = this.buildServiceData();

    try {
      this.isUpdate ? await this.processUpdate(service) : await this.createService(service);
      this.navigateToMeetingList();
    } catch (error) {
      this.handleError(error);
    } finally {
      this.isButtonLoading$.next(false);
    }
  }

  private async createService(service: Service): Promise<void> {
    await this.storeService.createService(service, this.hostid);
    this.analyticsService.trackEvent(POSTHOG_KEYS.SERVICE_CREATED, POSTHOG_CATEGORIES.USER, POSTHOG_ACTIONS.CLICK);
    this.alerts.openSuccessSnack('MEETING_SCHEDULER.MEETING_LIST.SERVICE.CREATED_SUCCESS');
  }

  private async updateService(id: string, service: Service): Promise<void> {
    await this.storeService.updateService(id, service);
    this.analyticsService.trackEvent(POSTHOG_KEYS.SERVICE_UPDATED, POSTHOG_CATEGORIES.USER, POSTHOG_ACTIONS.CLICK);
    this.alerts.openSuccessSnack('MEETING_SCHEDULER.MEETING_LIST.SERVICE.UPDATED_SUCCESS');
  }

  private handleError(error: any): void {
    const errorMsg = this.isUpdate
      ? 'MEETING_SCHEDULER.MEETING_LIST.SERVICE.UPDATED_ERROR'
      : 'MEETING_SCHEDULER.MEETING_LIST.SERVICE.CREATED_ERROR';

    this.alerts.openErrorSnack(errorMsg);
    console.error(error);
  }

  private async navigateToMeetingList(): Promise<unknown> {
    const meetingTypeListLink = await this.meetingTypeListViewPage$.pipe(take(1)).toPromise();
    return this.router.navigate([meetingTypeListLink]);
  }

  trackById(index: number, item: { id: number }) {
    return item.id;
  }
}
