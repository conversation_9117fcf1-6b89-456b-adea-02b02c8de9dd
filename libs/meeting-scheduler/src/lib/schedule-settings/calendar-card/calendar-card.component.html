<mat-card appearance="outlined" class="step-card">
  <mat-card-header>
    <mat-card-title>
      {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CALENDAR.TITLE' | translate }}
    </mat-card-title>
    <mat-card-subtitle class="subtitle">
      {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CALENDAR.DESCRIPTION' | translate }}
    </mat-card-subtitle>
  </mat-card-header>

  <mat-card-content *ngIf="(featureMicrosoftTeamsEnabled$ | async) === true">
    <meeting-scheduler-calendar-selection
      (calendarProviderSelected)="handleCalendarProvider($event)"
      [enableTracking]="enableTracking"
    >
    </meeting-scheduler-calendar-selection>
  </mat-card-content>
  <ng-content select="mat-card-actions"></ng-content>
</mat-card>
