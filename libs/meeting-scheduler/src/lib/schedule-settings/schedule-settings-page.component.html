<div [class]="(view$ | async) === VIEWS.WIZARD ? 'scheduler-setup-container' : 'scheduler-settings-container'">
  <div class="top-spacer"></div>
  <ng-container [ngSwitch]="view$ | async">
    <!-- TODO: Do we need this? Can we make this better? It wasn't around before-->
    <ng-container *ngSwitchCase="VIEWS.LOADING"></ng-container>

    <ng-container *ngSwitchCase="VIEWS.WIZARD">
      <meeting-scheduler-settings-wizard
        [isHostConfigured]="isHostConfigured$ | async"
        [isGoogleMeetConfigured]="isGoogleMeetConfigured$ | async"
        [isMicrosoftConfigured]="isMicrosoftConfigured$ | async"
        [hostPreferences]="hostPreferences$ | async"
        [initialSlug]="personalCalendarSlug$ | async"
        [hostId]="hostId$ | async"
      ></meeting-scheduler-settings-wizard>
    </ng-container>

    <ng-container *ngSwitchCase="VIEWS.SETTINGS">
      <meeting-scheduler-settings
        [isHostConfigured]="isHostConfigured$ | async"
        [isGoogleMeetConfigured]="isGoogleMeetConfigured$ | async"
        [isMicrosoftConfigured]="isMicrosoftConfigured$ | async"
        [hostPreferences]="hostPreferences$ | async"
      ></meeting-scheduler-settings>
    </ng-container>
  </ng-container>

  <a
    *ngIf="(isHostConfigured$ | async) && (view$ | async) === VIEWS.WIZARD"
    i18n="@@MEETING_SCHEDULER.SCHEDULE_SETTINGS.VIEW_SETTINGS"
    class="switch-view-link wizard-setting-padding"
    [routerLink]="['./']"
    [queryParams]="{ wizard: false }"
  >
    View Defaults
  </a>
</div>
