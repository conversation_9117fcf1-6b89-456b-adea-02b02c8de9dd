import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Observable, Subscription } from 'rxjs';

import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { AvailableServices, MeetingViewModelService } from '@vendasta/integrations';
import { Calendar, Preferences } from '@vendasta/meetings';
import { distinctUntilChanged, filter, first, map, pluck, tap } from 'rxjs/operators';
import { MeetingSchedulerStoreService } from '../data-providers/meeting-scheduler-store.service';
import { BreadCrumb } from '../sales-center-navigation/breadcrumbs.component';
import { ScheduleSettingsViewService, View } from './schedule-settings-view.service';
import { POSTHOG_ACTIONS, POSTHOG_CATEGORIES, POSTHOG_KEYS } from '../constants';
import { ProductAnalyticsService } from '@vendasta/product-analytics';

export const MEETINGS_LIST = 'MEETING_SCHEDULER.MEETING_LIST.BREADCRUMB';
export const MEETINGS_SETTINGS = 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.BREADCRUMB';

@Component({
  selector: 'meeting-scheduler-scheduled-meetings',
  templateUrl: './schedule-settings-page.component.html',
  styleUrls: ['./schedule-settings-page.component.scss', './calendar.component.scss'],
  standalone: false,
})
export class ScheduleSettingsPageComponent implements OnInit, OnDestroy {
  readonly breadCrumbs$: Observable<BreadCrumb[]>;

  isHostConfigured$: Observable<boolean>;
  isGoogleMeetConfigured$: Observable<boolean>;
  isMicrosoftConfigured$: Observable<boolean>;
  hostPreferences$: Observable<Preferences>;
  personalCalendar$: Observable<Calendar>;
  personalCalendarSlug$: Observable<string>;
  hostId$: Observable<string>;
  view$: Observable<View>;

  VIEWS = View;
  private readonly subscriptions: Subscription[] = [];

  constructor(
    private readonly translate: TranslateService,
    private readonly alertService: SnackbarService,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly meetingViewModelService: MeetingViewModelService,
    private readonly meetingSchedulerStoreService: MeetingSchedulerStoreService,
    private readonly scheduleSettingsViewService: ScheduleSettingsViewService,
    private readonly analyticsService: ProductAnalyticsService,
  ) {
    this.breadCrumbs$ = this.translate
      .stream([MEETINGS_LIST, MEETINGS_SETTINGS])
      .pipe(
        map((translations) => [
          { label: translations[MEETINGS_LIST], link: `/events-and-meetings/schedule` },
          { label: translations[MEETINGS_SETTINGS] },
        ]),
      );
  }

  ngOnInit(): void {
    this.alertIfConnectionOccurred();

    this.subscriptions.push(
      this.route.queryParams
        .pipe(
          tap((params) => {
            const messages = {
              connect: params['oauth2_status_message'],
              disconnect: params['disconnect_status_message'],
            };

            const rawMessage = messages.connect || messages.disconnect;
            const isSuccess = rawMessage?.toLowerCase().includes('succeed');
            let message: string | null = null;

            if (messages.connect) {
              message = isSuccess
                ? 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.CONNECTED_SUCESSFULLY'
                : 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.GENERIC_ERROR';
            } else if (messages.disconnect) {
              message = isSuccess
                ? 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.DISCONNECTED_SUCESSFULLY'
                : 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.GENERIC_ERROR';
            }

            if (message) {
              isSuccess ? this.alertService.openSuccessSnack(message) : this.alertService.openErrorSnack(message);
              const cleanedParams = { ...params };
              delete cleanedParams['calendarConnected'];
              delete cleanedParams['oauth2_status_message'];
              delete cleanedParams['disconnect_status_message'];

              this.analyticsService.trackEvent(
                POSTHOG_KEYS.HOST_PREFERENCE_UPDATED,
                POSTHOG_CATEGORIES.USER,
                POSTHOG_ACTIONS.CLICK,
              );
              this.router.navigate([], {
                relativeTo: this.route,
                queryParams: cleanedParams,
                replaceUrl: true,
              });
            }
          }),
          pluck('wizard'),
          filter<string>(Boolean),
          distinctUntilChanged(),
        )
        .subscribe((showWizardString) => {
          this.scheduleSettingsViewService.setView(showWizardString === 'true' ? View.WIZARD : View.SETTINGS);
        }),
    );
    this.view$ = this.scheduleSettingsViewService.view$;
    this.isHostConfigured$ = this.meetingSchedulerStoreService.loadHasHostEverBeenConfigured();
    this.hostPreferences$ = this.meetingSchedulerStoreService.loadUserPreferences();
    this.personalCalendar$ = this.meetingSchedulerStoreService.loadPersonalCalendar().pipe(
      map((calendar) => {
        if (calendar.slug === calendar.id || calendar.slug === calendar.externalId) {
          // the default for calendarSlug is the hostId, we don't want to show it by default.
          return { ...calendar, slug: '' };
        }
        return calendar;
      }),
    );

    this.personalCalendarSlug$ = this.personalCalendar$.pipe(map((calendar) => calendar.slug));
    this.isHostConfigured$.pipe(first()).subscribe((isHostConfigured) => {
      if (!isHostConfigured) {
        this.scheduleSettingsViewService.setView(View.WIZARD);
      }
    });
    this.isGoogleMeetConfigured$ = this.meetingViewModelService.isServiceConnected$(AvailableServices.GOOGLE_MEET);
    this.isMicrosoftConfigured$ = this.meetingViewModelService.isServiceConnected$(AvailableServices.MICROSOFT);
  }

  private alertIfConnectionOccurred(): void {
    const { calendarConnected, conferencingConnected } = this.route.snapshot.queryParams;
    if (calendarConnected === 'true') {
      this.alertService.openSuccessSnack('MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.CALENDAR_CONNECTED');
    } else if (calendarConnected === 'false') {
      this.alertService.openSuccessSnack('MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.CALENDAR_DISCONNECTED');
    }
    if (conferencingConnected === 'true') {
      this.alertService.openSuccessSnack(
        'MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.SET_PREFERRED_MEETING_APP_SUCCESS',
      );
    }
    if (calendarConnected || conferencingConnected) {
      // Remove query params
      this.router.navigate([], {
        queryParams: {
          calendarConnected: null,
          conferencingConnected: null,
        },
        queryParamsHandling: 'merge',
      });
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }
}
