import { AvailableServices } from '@vendasta/integrations';
import { Inject, Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map, shareReplay, tap } from 'rxjs/operators';
import { ActivatedRoute } from '@angular/router';
import { MeetingSchedulerServiceModule } from '../data-providers/meeting-scheduler-service.module';
import { LOCAL_STORAGE_TOKEN } from '../data-providers/local-storage';

export enum View {
  WIZARD = 'WIZARD',
  SETTINGS = 'SETTINGS',
  LOADING = 'LOADING',
}

interface State {
  view: View;
  // 0 - calendar, 1 - meeting app, 2 - availability
  wizardStep: number;
  meetingAppSelection: AvailableServices | undefined;
}

const defaultState: State = Object.freeze({
  view: View.SETTINGS,
  wizardStep: 0,
  meetingAppSelection: undefined,
});

const SCHEDULE_SETTINGS_VIEW_KEY = 'bookmenow-schedule-settings-key';

@Injectable({
  providedIn: MeetingSchedulerServiceModule,
})
export class ScheduleSettingsViewService {
  private readonly state$$: BehaviorSubject<State> = new BehaviorSubject(this.loadInitialState());
  private readonly state$: Observable<State>;

  readonly view$: Observable<View>;
  readonly wizardStep$: Observable<number>;
  readonly meetingAppSelection$: Observable<AvailableServices | undefined>;

  constructor(
    @Inject(LOCAL_STORAGE_TOKEN) private readonly localStorage: Storage,
    private readonly route: ActivatedRoute,
  ) {
    this.state$ = this.state$$.pipe(
      tap((state) => this.storeState(state)),
      shareReplay(1),
    );

    this.view$ = this.state$.pipe(map((s) => s.view));
    this.wizardStep$ = this.state$.pipe(map((s) => s.wizardStep));
    this.meetingAppSelection$ = this.state$.pipe(map((s) => s.meetingAppSelection));
  }

  setView(view: View): void {
    this.state$$.next({ ...this.state$$.getValue(), view });
  }

  setWizardStep(wizardStep: number): void {
    this.state$$.next({ ...this.state$$.getValue(), wizardStep });
  }

  setMeetingAppSelection(meetingAppSelection: AvailableServices): void {
    this.state$$.next({ ...this.state$$.getValue(), meetingAppSelection });
  }

  resetStateStorage(): void {
    this.localStorage.removeItem(SCHEDULE_SETTINGS_VIEW_KEY);
  }

  private storeState(state: State): void {
    this.localStorage.setItem(SCHEDULE_SETTINGS_VIEW_KEY, JSON.stringify(state));
  }

  private loadInitialState(): State {
    const stateString = this.localStorage.getItem(SCHEDULE_SETTINGS_VIEW_KEY);
    const wizardView = this.route.snapshot.queryParamMap.get('wizard');
    const state = stateString ? JSON.parse(stateString) : defaultState;
    if (wizardView) {
      return { ...state, view: View.WIZARD };
    }
    return state;
  }
}
