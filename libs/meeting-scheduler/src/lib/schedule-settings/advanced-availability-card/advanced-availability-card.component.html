<mat-card appearance="outlined" class="step-card">
  <mat-card-header *ngIf="currentView === 'settings'">
    <mat-card-title>
      {{ 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.TITLE' | translate }}
    </mat-card-title>
    <mat-card-subtitle class="subtitle">
      {{ 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.PROMPT' | translate }}
    </mat-card-subtitle>
  </mat-card-header>
  <mat-card-content>
    <div>
      <form *ngIf="!loadingAvailabilityForm; else loadingForm" [formGroup]="form" (ngSubmit)="setAvailability()">
        <div class="week">
          <mat-card
            appearance="outlined"
            *ngFor="let day of daysOfWeek; let i = index; let first = first"
            class="weekday"
            [style.margin-left.px]="first ? 0 : -1"
          >
            <mat-card-title class="weekday-title">
              <mat-checkbox [checked]="isDayEnabled(day)" (change)="setIsDayEnabled(day, $event.checked)">
              </mat-checkbox>
              <div>
                {{ indexToDayOfWeekMapping[i].name }}
              </div>
            </mat-card-title>
            <mat-card-content class="weekday-content" (click)="openEditAvailabilityDialog(day)">
              <div *ngIf="isDayEnabled(day)" class="weekday-ranges">
                <a *ngFor="let range of rangesForDay(day)">
                  {{ range.start | date: 'h:mma' | lowercase }}–&#10; {{ range.end | date: 'h:mma' | lowercase }}
                </a>
              </div>
              <div *ngIf="!isDayEnabled(day) || rangesForDay(day).length === 0" class="unavailable">
                {{ 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.UNAVAILABLE' | translate }}
              </div>
            </mat-card-content>
          </mat-card>
        </div>
        <div class="timezone-selector" *ngIf="currentView === 'settings'">
          <glxy-timezone-selector
            [initialTimezone]="timezone"
            (timezoneChanged)="onTimezoneChanged($event)"
          ></glxy-timezone-selector>
        </div>
      </form>
      <glxy-error *ngIf="shouldShowError()">
        {{ 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.MANDATORY' | translate }}
      </glxy-error>
    </div>
    <ng-template #loadingForm>
      <div class="form-row fields-row day-container">
        <div class="shimmer-box stencil-shimmer"></div>
        <div class="shimmer-box stencil-shimmer"></div>
        <div class="shimmer-box stencil-shimmer"></div>
        <div class="shimmer-box stencil-shimmer"></div>
        <div class="shimmer-box stencil-shimmer"></div>
        <div class="shimmer-box stencil-shimmer"></div>
        <div class="shimmer-box stencil-shimmer"></div>
      </div>
      <div class="form-row fields-row shimmer-margin">
        <div class="shimmer-box stencil-shimmer time-zone-message"></div>
      </div>
      <div class="form-row form-actions shimmer-buttons">
        <button mat-raised-button color="primary" type="submit" [disabled]="true">
          {{ 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.BUTTONS.SAVE' | translate }}
        </button>
      </div>
    </ng-template>
  </mat-card-content>
  <mat-card-actions *ngIf="currentView === 'settings'">
    <div class="timezone-gutter"></div>
    <div class="form-row form-actions">
      <button
        *ngIf="!savingAvailabilityForm"
        mat-raised-button
        color="primary"
        type="submit"
        (click)="setAvailability()"
      >
        {{ 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.BUTTONS.SAVE' | translate }}
      </button>
      <span *ngIf="savingAvailabilityForm">
        <mat-spinner [diameter]="30" [strokeWidth]="8"></mat-spinner>
      </span>
    </div>
  </mat-card-actions>
</mat-card>
