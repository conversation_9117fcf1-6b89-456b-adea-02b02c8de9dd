import { ChangeDetectorRef, Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { DayOfWeek, HostUser } from '@vendasta/meetings';
import moment from 'moment-timezone';
import { Observable, Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { MeetingSchedulerStoreService } from '../../data-providers/meeting-scheduler-store.service';
import { DailyAvailability, TimeRange } from '../../interface';
import { INVALID_MEETING_ID, CURRENT_VIEW, POSTHOG_KEYS, POSTHOG_CATEGORIES, POSTHOG_ACTIONS } from '../../constants';
import { ScheduleSettingsViewService } from '../schedule-settings-view.service';
import { indexToDayOfWeekMapping, ScheduleSettingsService } from '../schedule-settings.service';
import { EditAvailabilityDialogComponent } from './edit-availability-dialog';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { AvailabilityRuleList } from '@vendasta/meetings/lib/_internal/objects/meeting-host';

@Component({
  selector: 'meeting-scheduler-settings-advanced-availability-card',
  templateUrl: './advanced-availability-card.component.html',
  styleUrls: ['./advanced-availability-card.component.scss'],
  standalone: false,
})
export class AdvancedAvailabilityCardComponent implements OnInit, OnDestroy {
  @Input() timezone: string;
  @Input() currentView: string;
  @Input() meetingId: string;
  @Input() selectedHosts: HostUser[];
  @Input() hostAvailability: Observable<Map<string, AvailabilityRuleList>>;
  @Input() isMultiHost: boolean;
  @Input() displayAvailabilitySelectionError: boolean;
  @Output() availabilitySaved: EventEmitter<void> = new EventEmitter<void>();
  @Output() dayUpdated: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() enableTracking: boolean;
  @Output() hasSelectedAvailability: EventEmitter<boolean> = new EventEmitter<boolean>();

  daysOfWeek = [
    DayOfWeek.MONDAY,
    DayOfWeek.TUESDAY,
    DayOfWeek.WEDNESDAY,
    DayOfWeek.THURSDAY,
    DayOfWeek.FRIDAY,
    DayOfWeek.SATURDAY,
    DayOfWeek.SUNDAY,
  ];

  loadingAvailabilityForm = true;
  savingAvailabilityForm = false;
  isAvailabilityFormOpened = false;
  isAvailabilitySet = false;
  form: UntypedFormGroup;

  timeOptions: Date[] = [];

  private readonly subscriptions: Subscription[] = [];
  private availability: any;

  constructor(
    private readonly alertService: SnackbarService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly meetingSchedulerService: MeetingSchedulerStoreService,
    private readonly scheduledMeetingsService: ScheduleSettingsService,
    private readonly scheduleSettingsViewService: ScheduleSettingsViewService,
    private readonly dialog: MatDialog,
    private cdr: ChangeDetectorRef,
    private readonly analyticsService: ProductAnalyticsService,
  ) {}

  ngOnInit(): void {
    if (this.currentView == CURRENT_VIEW) {
      this.timeOptions = this.scheduledMeetingsService.timeOptions;
      this.setupAvailabilityForm();
    } else {
      this.setupAvailabilityForm();
    }
  }

  abbreviateTimezone(tz: string): string {
    return moment.tz(tz).zoneAbbr();
  }

  private async setupAvailabilityForm(): Promise<void> {
    this.loadingAvailabilityForm = true;
    if (this.currentView == CURRENT_VIEW) {
      this.availability = await this.scheduledMeetingsService.loadInitialAvailability();
    } else {
      if (this.meetingId == undefined) {
        this.meetingId = INVALID_MEETING_ID;
      }
      this.availability = await this.scheduledMeetingsService.loadInitialAvailability(this.meetingId);
    }
    const dayEnabled = new Map<DayOfWeek, boolean>();
    const ranges = this.availability.days;
    this.daysOfWeek.forEach((day) => dayEnabled.set(day, (ranges.get(day) || []).length > 0));

    this.isAvailabilitySet = Array.from(dayEnabled.values()).some((enabled) => enabled);
    this.hasSelectedAvailability.emit(this.isAvailabilitySet);

    this.form = this.formBuilder.group({
      dayEnabled: this.formBuilder.control(dayEnabled),
      ranges: this.formBuilder.control(ranges),
    });
    this.loadingAvailabilityForm = false;
  }

  setAvailability(): void {
    this.savingAvailabilityForm = true;
    const dayEnabled = this.form.controls.dayEnabled.value as Map<DayOfWeek, boolean>;
    const dayRanges = this.form.controls.ranges.value as Map<DayOfWeek, TimeRange[]>;

    const availability: DailyAvailability = { days: new Map() };
    dayEnabled.forEach((available, day) => {
      if (available) {
        availability.days.set(day, dayRanges.get(day));
      }
    });
    this.subscriptions.push(
      this.scheduledMeetingsService
        .setAvailability(availability, this.timezone, this.meetingId)
        .pipe(
          finalize(() => {
            this.savingAvailabilityForm = false;
          }),
        )
        .subscribe({
          next: () => {
            this.scheduleSettingsViewService.resetStateStorage();
            this.alertService.openSuccessSnack('MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.AVAILABILITY_SAVED');
            if (this.enableTracking) {
              this.analyticsService.trackEvent(
                POSTHOG_KEYS.HOST_PREFERENCE_UPDATED,
                POSTHOG_CATEGORIES.USER,
                POSTHOG_ACTIONS.CLICK,
              );
            }
            this.availabilitySaved.emit();
          },
          error: () => {
            this.alertService.openErrorSnack('MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.AVAILABILITY_SAVED_ERROR');
          },
        }),
    );
  }

  saveWithChanges(changes: Map<DayOfWeek, TimeRange[]>): void {
    const dayEnabled = this.form.controls.dayEnabled.value as Map<DayOfWeek, boolean>;
    const ranges = this.form.controls.ranges.value as Map<DayOfWeek, TimeRange[]>;
    changes.forEach((newRanges, day) => {
      dayEnabled.set(day, newRanges.length > 0);
      ranges.set(day, newRanges);
    });
    this.setAvailability();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }

  isDayEnabled(day: DayOfWeek): boolean {
    return this.form.controls.dayEnabled.value.get(day);
  }

  setIsDayEnabled(day: DayOfWeek, enabled: boolean): void {
    this.form.controls.dayEnabled.value.set(day, enabled);
    if (enabled && this.rangesForDay(day).length <= 0) {
      this.openEditAvailabilityDialog(day);
    }
    if (this.currentView !== CURRENT_VIEW) {
      this.dayUpdated.emit(true);

      const dayEnabled = this.form.controls.dayEnabled.value as Map<DayOfWeek, boolean>;
      const dayRanges = this.form.controls.ranges.value as Map<DayOfWeek, TimeRange[]>;
      const availability: DailyAvailability = { days: new Map() };
      dayEnabled.forEach((available, day) => {
        if (available) {
          availability.days.set(day, dayRanges.get(day));
        }
      });
      const data: { timeRanges: Map<DayOfWeek, TimeRange[]>; availability: Map<DayOfWeek, boolean> } = {
        availability: dayEnabled,
        timeRanges: dayRanges,
      };
      this.isAvailabilitySet = Array.from(dayEnabled.values()).some((enabled) => enabled);
      this.hasSelectedAvailability.emit(this.isAvailabilitySet);
      this.meetingSchedulerService.updateSchedule(data);
    }
  }

  rangesForDay(day: DayOfWeek): TimeRange[] {
    const ranges = this.form.controls.ranges.value as Map<DayOfWeek, TimeRange[]>;
    return ranges.get(day) || [];
  }

  private getCommonTimeSlots(day: DayOfWeek): string[] {
    // Helper to get time ranges for a user on a specific day
    const getTimeRanges = (userData: any, day: number) => {
      return userData.rules
        .filter((rule: any) => rule.day === day)
        .map((rule: any) => {
          const from = rule.timeSlot.from;
          const to = rule.timeSlot.to;
          return {
            from: {
              hours: from.hours,
              minutes: from.minutes || 0,
            },
            to: {
              hours: to.hours,
              minutes: to.minutes || 0,
            },
          };
        });
    };

    // Convert time to minutes since 00:00
    const toMinutes = (time: { hours: number; minutes: number }) => {
      return time.hours * 60 + (time.minutes || 0);
    };

    // Convert minutes to time object
    const toTime = (minutes: number) => {
      return {
        hours: Math.floor(minutes / 60),
        minutes: minutes % 60,
      };
    };

    // Format time to "h:mm AM/PM"
    const formatTime = (time: { hours: number; minutes: number }) => {
      const hours12 = time.hours % 12 || 12;
      const minutes = time.minutes.toString().padStart(2, '0');
      const ampm = time.hours < 12 || time.hours === 24 ? 'AM' : 'PM';
      return `${hours12}:${minutes} ${ampm}`;
    };

    // Intersect two lists of time ranges
    const intersectRanges = (a: any[], b: any[]) => {
      const result = [];
      for (const r1 of a) {
        for (const r2 of b) {
          const start = Math.max(toMinutes(r1.from), toMinutes(r2.from));
          const end = Math.min(toMinutes(r1.to), toMinutes(r2.to));
          if (start < end) {
            result.push({ from: toTime(start), to: toTime(end) });
          }
        }
      }
      return result;
    };

    const userIds: string[] = [];
    for (const host of this.selectedHosts) {
      userIds.push(host.userId);
    }

    if (userIds.length === 0) {
      return [];
    }

    let dataMap: Map<string, AvailabilityRuleList> = new Map();
    if (this.hostAvailability) {
      this.hostAvailability
        .subscribe((availability) => {
          dataMap = availability;
        })
        .unsubscribe(); // Immediately unsubscribe since we just want the current value
    }

    // Get all ranges from the first user
    let commonRanges = getTimeRanges(dataMap.get(userIds[0]), day);

    // Intersect with the rest
    for (let i = 1; i < userIds.length; i++) {
      const userRanges = getTimeRanges(dataMap.get(userIds[i]), day);
      commonRanges = intersectRanges(commonRanges, userRanges);
      if (commonRanges.length === 0) break; // early exit if no overlap
    }

    return commonRanges.map((slot) => `${formatTime(slot.from)} - ${formatTime(slot.to)}`);
  }

  openEditAvailabilityDialog(day: DayOfWeek): void {
    this.isAvailabilityFormOpened = true;
    if (this.savingAvailabilityForm) {
      return;
    }

    // Calculate common available slots for selected hosts
    const commonSlots = this.isMultiHost ? this.getCommonTimeSlots(day) : [];

    const dialogRef = this.dialog.open(EditAvailabilityDialogComponent, {
      data: {
        ranges: this.rangesForDay(day),
        day,
        saveCallback: (changes: Map<DayOfWeek, TimeRange[]>) => this.decideFlow(changes),
        isMultiHost: this.isMultiHost,
        availableSlots: commonSlots,
      },
    });
    this.subscriptions.push(
      dialogRef.afterClosed().subscribe(() => {
        const hasAnyRanges = this.rangesForDay(day).length > 0;
        if (!hasAnyRanges) {
          this.setIsDayEnabled(day, false);
        }
        this.cdr.detectChanges();
      }),
    );
  }

  decideFlow(changes) {
    if (this.currentView == CURRENT_VIEW) {
      this.saveWithChanges(changes);
    } else {
      const dayEnabled = this.form.controls.dayEnabled.value as Map<DayOfWeek, boolean>;
      const ranges = this.form.controls.ranges.value as Map<DayOfWeek, TimeRange[]>;

      this.isAvailabilitySet = Array.from(dayEnabled.values()).some((enabled) => enabled);
      this.hasSelectedAvailability.emit(this.isAvailabilitySet);
      changes.forEach((newRanges, day) => {
        dayEnabled.set(day, newRanges.length > 0);
        ranges.set(day, newRanges);
      });
      const data: { timeRanges: Map<DayOfWeek, TimeRange[]>; availability: Map<DayOfWeek, boolean> } = {
        availability: dayEnabled,
        timeRanges: ranges,
      };
      this.meetingSchedulerService.updateSchedule(data);
    }
  }

  onTimezoneChanged(timezone: string): void {
    this.timezone = timezone;
  }

  shouldShowError(): boolean {
    return (
      this.isMultiHost &&
      (this.isAvailabilityFormOpened || this.displayAvailabilitySelectionError) &&
      !this.isAvailabilitySet
    );
  }

  protected readonly indexToDayOfWeekMapping = indexToDayOfWeekMapping;
}
