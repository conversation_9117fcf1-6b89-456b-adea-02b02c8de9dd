<va-status-banner
  type="info"
  statusText="{{ 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.COMPLETE_SETUP' | translate }}"
></va-status-banner>
<mat-stepper
  #stepper
  data-testid="meeting-scheduler-setup-wizard"
  *ngIf="stepLabels$ | async as stepLabels"
  class="schedule-stepper"
  labelPosition="bottom"
  [selectedIndex]="wizardStep$ | async"
  (selectionChange)="onStepperSelectionChange($event)"
>
  <mat-step [label]="stepLabels.step1">
    <meeting-scheduler-calendar-slug-card
      [calendarId]="personalCalendarId$ | async"
      [setInitial]="true"
      [showHeaderIcon]="true"
      buttonLabel="{{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUTTONS.NEXT' | translate }}"
      (saveButtonClicked)="setStep(stepper.selectedIndex + 1)"
    ></meeting-scheduler-calendar-slug-card>
  </mat-step>
  <mat-step [label]="stepLabels.step2">
    <meeting-scheduler-settings-calendar-card
      *ngIf="(featureMicrosoftTeamsEnabled$ | async) === true"
      (calendarProviderSelected)="handleCalendarProvider($event)"
    >
      <mat-card-actions align="end" class="action-bar">
        <button mat-stroked-button *ngIf="!isGoogleMeetConfigured" matStepperNext class="skip-button">
          {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUTTONS.SETUP_LATER' | translate }}
        </button>
        <meeting-scheduler-connect-with-google-button
          *ngIf="selectedCalendarProvider === 'google'"
          [serviceKey]="SERVICE_KEYS.eventsAndMeetingsPageCalendarConnected"
          [primaryDisconnect]="false"
        ></meeting-scheduler-connect-with-google-button>
        <meeting-scheduler-connect-with-microsoft-button
          *ngIf="selectedCalendarProvider === 'microsoft' && (featureMicrosoftTeamsEnabled$ | async) === true"
          [serviceKey]="SERVICE_KEYS.eventsAndMeetingsPageCalendarConnected"
          [primaryDisconnect]="false"
        ></meeting-scheduler-connect-with-microsoft-button>

        <button
          *ngIf="isGoogleMeetConfigured"
          [disabled]="disableStep2Next$ | async"
          mat-raised-button
          color="primary"
          type="submit"
          matStepperNext
          class="step-3-next-button"
        >
          {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUTTONS.NEXT' | translate }}
        </button>

        <!--          </div>-->
      </mat-card-actions>
    </meeting-scheduler-settings-calendar-card>
    <meeting-scheduler-settings-calendar-card *ngIf="(featureMicrosoftTeamsEnabled$ | async) === false">
      <mat-card-actions align="end" class="action-bar">
        <button mat-stroked-button *ngIf="!isGoogleMeetConfigured" matStepperNext class="skip-button">
          {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUTTONS.SETUP_LATER' | translate }}
        </button>
        <meeting-scheduler-connect-with-google-button
          [serviceKey]="SERVICE_KEYS.eventsAndMeetingsPageCalendarConnected"
          [primaryDisconnect]="false"
        ></meeting-scheduler-connect-with-google-button>
        <meeting-scheduler-connect-with-microsoft-button
          *ngIf="(featureMicrosoftTeamsEnabled$ | async) === true"
          [serviceKey]="SERVICE_KEYS.eventsAndMeetingsPageCalendarConnected"
          [primaryDisconnect]="false"
        ></meeting-scheduler-connect-with-microsoft-button>

        <button
          *ngIf="isGoogleMeetConfigured"
          [disabled]="disableStep2Next$ | async"
          mat-raised-button
          color="primary"
          type="submit"
          matStepperNext
          class="step-3-next-button"
        >
          {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUTTONS.NEXT' | translate }}
        </button>
      </mat-card-actions>
    </meeting-scheduler-settings-calendar-card>
  </mat-step>
  <mat-step [label]="stepLabels.step3">
    <meeting-scheduler-settings-meeting-app-card
      [serviceKey]="SERVICE_KEYS.eventsAndMeetingsPageConferencingConnected"
      [hostPreferences]="hostPreferences"
      (integrationSelection)="onIntegrationSelection($event)"
    >
      <mat-card-actions class="step-2-actions">
        <button mat-stroked-button matStepperNext class="skip-button">
          {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUTTONS.SKIP' | translate }}
        </button>
        <button
          *ngIf="integrationsPageLink$ | async as integrationsPageLink"
          mat-stroked-button
          [routerLink]="[integrationsPageLink]"
        >
          Open integration settings
        </button>
        <button
          [disabled]="disableStep2Next$ | async"
          mat-raised-button
          color="primary"
          type="submit"
          matStepperNext
          class="step-3-next-button"
        >
          {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUTTONS.NEXT' | translate }}
        </button>
      </mat-card-actions>
    </meeting-scheduler-settings-meeting-app-card>
  </mat-step>
  <mat-step [label]="stepLabels.step4">
    <meeting-scheduler-settings-availability-card
      [isHostConfigured]="isHostConfigured"
      (availabilitySaved)="onAvailabilitySaved()"
    ></meeting-scheduler-settings-availability-card>
  </mat-step>
</mat-stepper>
