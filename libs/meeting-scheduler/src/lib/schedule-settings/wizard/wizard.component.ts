import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { StepperSelectionEvent } from '@angular/cdk/stepper';
import { Component, Inject, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Preferences } from '@vendasta/meetings';
import { BehaviorSubject, firstValueFrom, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  FEATURE_MEETING_MICROSOFT_TEAMS_TOKEN,
  INTEGRATIONS_PAGE_LINK_TOKEN,
  MEETING_INTEGRATION_SERVICE_KEYS_TOKEN,
  MEETING_TYPE_LIST_VIEW_PAGE_TOKEN,
  PERSONAL_CALENDAR_ID_TOKEN,
  RECOMMENDED_PERSONAL_CALENDAR_SLUGS_TOKEN,
} from '../../data-providers/providers';
import { MeetingIntegrationServiceKeys } from '../../interface';
import { IntegrationServiceSelection } from '../../meeting-integrations/integration-selection/integration-selection.component';
import { ScheduleSettingsViewService } from '../schedule-settings-view.service';
import { POSTHOG_ACTIONS, POSTHOG_CATEGORIES, POSTHOG_KEYS } from '../../constants';
import { ProductAnalyticsService } from '@vendasta/product-analytics';

interface StepLabels {
  step1: string;
  step2: string;
  step3: string;
  step4: string;
}

@Component({
  selector: 'meeting-scheduler-settings-wizard',
  templateUrl: './wizard.component.html',
  styleUrls: ['./wizard.component.scss'],
  standalone: false,
})
export class WizardComponent implements OnInit {
  @Input() isHostConfigured: boolean;
  @Input() isGoogleMeetConfigured: boolean;
  @Input() isMicrosoftConfigured: boolean;
  @Input() hostPreferences: Preferences;
  @Input() initialSlug: string;
  @Input() hostId: string;

  wizardStep$: Observable<number>;

  readonly onMobile$: Observable<boolean>;
  stepLabels$: Observable<StepLabels>;

  private readonly selection$$: BehaviorSubject<IntegrationServiceSelection> = new BehaviorSubject(undefined);
  disableStep2Next$: Observable<boolean>;
  selectedCalendarProvider: string;

  constructor(
    @Inject(MEETING_INTEGRATION_SERVICE_KEYS_TOKEN) public readonly SERVICE_KEYS: MeetingIntegrationServiceKeys,
    @Inject(INTEGRATIONS_PAGE_LINK_TOKEN) public readonly integrationsPageLink$: Observable<string>,
    @Inject(MEETING_TYPE_LIST_VIEW_PAGE_TOKEN) public readonly meetingListViewPageLink$: Observable<string>,
    @Inject(RECOMMENDED_PERSONAL_CALENDAR_SLUGS_TOKEN) public readonly recommendedSlugs$: Observable<string[]>,
    @Inject(PERSONAL_CALENDAR_ID_TOKEN) public readonly personalCalendarId$: Observable<string>,
    private readonly scheduleSettingsViewService: ScheduleSettingsViewService,
    private readonly router: Router,
    private readonly translate: TranslateService,
    private readonly breakpointObserver: BreakpointObserver,
    private readonly analyticsService: ProductAnalyticsService,
    @Inject(FEATURE_MEETING_MICROSOFT_TEAMS_TOKEN)
    readonly featureMicrosoftTeamsEnabled$: Observable<boolean>,
  ) {
    this.onMobile$ = breakpointObserver
      .observe([Breakpoints.XSmall, Breakpoints.Small])
      .pipe(map((result) => result.matches));
    this.stepLabels$ = this.onMobile$.pipe(
      map((onMobile) => {
        if (onMobile) {
          return { step1: '', step2: '', step3: '', step4: '' };
        }
        return {
          step1: this.translate.instant('MEETING_SCHEDULER.SCHEDULE_SETTINGS.STEPS.STEP_1'),
          step2: this.translate.instant('MEETING_SCHEDULER.SCHEDULE_SETTINGS.STEPS.STEP_2'),
          step3: this.translate.instant('MEETING_SCHEDULER.SCHEDULE_SETTINGS.STEPS.STEP_3'),
          step4: this.translate.instant('MEETING_SCHEDULER.SCHEDULE_SETTINGS.STEPS.STEP_4'),
        };
      }),
    );
  }

  ngOnInit(): void {
    this.wizardStep$ = this.scheduleSettingsViewService.wizardStep$;
    this.analyticsService.trackEvent(POSTHOG_KEYS.SETUP_WIZARD_OPENED, POSTHOG_CATEGORIES.USER, POSTHOG_ACTIONS.CLICK);
  }

  onStepperSelectionChange(selection: StepperSelectionEvent): void {
    this.scheduleSettingsViewService.setWizardStep(selection.selectedIndex);
    const step = selection.selectedIndex + 1;
    if (step != 1) {
      const previousStep = step - 1; // Marking previous step as completed
      const postHogKey = this.getPostHogKey(previousStep);
      this.analyticsService.trackEvent(postHogKey, POSTHOG_CATEGORIES.USER, POSTHOG_ACTIONS.CLICK);
    }
  }

  setStep(step: number): void {
    this.scheduleSettingsViewService.setWizardStep(step);
  }

  onIntegrationSelection(selection: IntegrationServiceSelection): void {
    this.selection$$.next(selection);
  }

  async onAvailabilitySaved() {
    const meetingListViewPageLink = await firstValueFrom(this.meetingListViewPageLink$);
    const postHogKey = this.getPostHogKey(4);
    this.analyticsService.trackEvent(postHogKey, POSTHOG_CATEGORIES.USER, POSTHOG_ACTIONS.CLICK);
    this.router.navigate([meetingListViewPageLink]);
  }

  getPostHogKey(step: number): string {
    const postHogKeysMap: Record<number, string> = {
      1: POSTHOG_KEYS.STEP_1_COMPLETED,
      2: POSTHOG_KEYS.STEP_2_COMPLETED,
      3: POSTHOG_KEYS.STEP_3_COMPLETED,
      4: POSTHOG_KEYS.STEP_4_COMPLETED,
    };
    return postHogKeysMap[step];
  }
  handleCalendarProvider(provider: string) {
    this.selectedCalendarProvider = provider;
  }
}
