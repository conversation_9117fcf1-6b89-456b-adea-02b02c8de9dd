import { NgModule } from '@angular/core';
import { MeetingSchedulerComponentsModule } from './meeting-scheduler-components.module';
import { RouterModule } from '@angular/router';
import { ScheduleSettingsPageComponent } from './schedule-settings/schedule-settings-page.component';
import { CalenderViewComponent } from './calendar-view/calendar-view.component';
import { MeetingTypeListViewComponent } from './meeting-type-list-view/meeting-type-list-view.component';
import { MeetingListComponent } from './meeting-list/meeting-list.component';
import { ScheduledMeetingsGuard } from './scheduled-meetings.guard';
import { BookingLinkPageComponent } from './booking-link/booking-link-page.component';
import { defaultMeetingSchedulerLinks } from './interface';
import { MeetingSchedulerNavigationComponent } from './navigation/meeting-scheduler-navigation.component';
import { NavigationModule } from './sales-center-navigation/navigation.module';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { LexiconModule } from '@galaxy/lexicon';
import baseTranslation from './i18n/assets/en_devel.json';
import { CalendarPageComponent } from './calendar/calendar-page.component';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GroupComponent } from './group/group.component';
import { ServiceComponent } from './service/service.component';
import { ScheduleMeetingButtonModule } from './meeting-actions/schedule-meeting/schedule-meeting-button.module';

/*
  MeetingSchedulerModule provides the full Meeting Scheduler experience - including routing.
  First, you must import the MeetingSchedulerDataModule.forRoot() and provide it configuration.
  Next, you can import this module or use the MeetingSchedulerComponents module to build your own experience.
  You can also use the submodules to use only small pieces of MeetingScheduler
 */
@NgModule({
  declarations: [MeetingSchedulerNavigationComponent],
  imports: [
    MeetingSchedulerComponentsModule,
    ScheduleMeetingButtonModule,
    RouterModule.forChild([
      {
        path: '',
        component: MeetingSchedulerNavigationComponent,
        children: [
          {
            path: defaultMeetingSchedulerLinks.calendarViewPageRelative,
            component: CalenderViewComponent,
          },
          {
            path: defaultMeetingSchedulerLinks.meetingTypeListViewPageRelative,
            component: MeetingTypeListViewComponent,
          },
          {
            path: defaultMeetingSchedulerLinks.settingsRelative,
            component: ScheduleSettingsPageComponent,
          },
          {
            path: defaultMeetingSchedulerLinks.meetingListPageRelative,
            component: MeetingListComponent,
            canActivate: [ScheduledMeetingsGuard],
          },
          {
            path: `${defaultMeetingSchedulerLinks.meetingListPageRelative}/:eventId/cancel`,
            component: MeetingListComponent,
            data: { action: 'cancel' },
          },
          {
            path: `${defaultMeetingSchedulerLinks.meetingListPageRelative}/:eventId/reschedule`,
            component: MeetingListComponent,
            data: { action: 'reschedule' },
          },
          {
            path: `${defaultMeetingSchedulerLinks.calendarPageRelative}/:calendarId`,
            component: CalendarPageComponent,
          },
          {
            path: `${defaultMeetingSchedulerLinks.eventTypePageRelative}/:calendarId`,
            component: BookingLinkPageComponent,
          },
          {
            path: `${defaultMeetingSchedulerLinks.eventTypePageRelative}/:calendarId/:eventTypeId`,
            component: BookingLinkPageComponent,
          },
          {
            path: defaultMeetingSchedulerLinks.eventTypePageRelative,
            component: BookingLinkPageComponent,
          },
          {
            path: defaultMeetingSchedulerLinks.groupPageRelative,
            component: GroupComponent,
          },
          {
            path: `${defaultMeetingSchedulerLinks.groupPageRelative}/:groupId`,
            component: GroupComponent,
          },
          {
            path: defaultMeetingSchedulerLinks.servicePageRelative,
            component: ServiceComponent,
          },
          {
            path: `${defaultMeetingSchedulerLinks.servicePageRelative}/:serviceId`,
            component: ServiceComponent,
          },
          {
            path: '',
            redirectTo: defaultMeetingSchedulerLinks.meetingListPageRelative,
            pathMatch: 'full',
          },
        ],
      },
    ]),
    NavigationModule,
    CommonModule,
    MatIconModule,
    MatButtonModule,
    GalaxyPageModule,
    TranslateModule,
    LexiconModule.forChild({
      componentName: 'common/meeting-scheduler',
      baseTranslation: baseTranslation,
    }),
  ],
})
export class MeetingSchedulerModule {}
