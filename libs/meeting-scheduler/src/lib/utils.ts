/**
 * Opening a new window exposes users to security exploit where malicious
 * parties can modify their browsers' "back history", leading to phishing
 * attacks.
 *
 * This adds the "noopener" option which prevents that exploit.
 */
import { isObservable, Observable, of } from 'rxjs';
import { DailyAvailability, TimeRange } from './interface';
import { DayOfWeek } from '@vendasta/meetings';

export function openSecureNewTab(url: string): Window {
  // tslint:disable-next-line:ban
  return window.open(url, '_blank', 'noopener');
}

export function openSecureCurrentTab(url: string): Window {
  // tslint:disable-next-line:ban
  return window.open(url, undefined, 'noopener');
}

export function getObservableString(v: string | undefined | null | Observable<string>): Observable<string> {
  if (isObservable(v)) {
    return v;
  }
  return of(v || '');
}

export function loadTimeOptions(): Date[] {
  const today = new Date();
  const timeOptions = [];
  for (let hour = 0; hour < 24; hour++) {
    timeOptions.push(
      new Date(today.getFullYear(), today.getMonth(), today.getDate(), hour, 0, 0),
      new Date(today.getFullYear(), today.getMonth(), today.getDate(), hour, 15, 0),
      new Date(today.getFullYear(), today.getMonth(), today.getDate(), hour, 30, 0),
      new Date(today.getFullYear(), today.getMonth(), today.getDate(), hour, 45, 0),
    );
  }
  return timeOptions;
}

export function getTimeOption(timeOptions: Date[], hours: number, minutes?: number): Date {
  hours = hours || 0;
  minutes = minutes || 0;
  return timeOptions[hours * 4 + Math.floor(minutes / 15)];
}

export function defaultTimeRange(): TimeRange {
  const timeOptions = loadTimeOptions();
  return { start: getTimeOption(timeOptions, 9), end: getTimeOption(timeOptions, 17) };
}

export function getDefaultAvailability(): DailyAvailability {
  const days = new Map<DayOfWeek, TimeRange[]>();
  const defaultTime = defaultTimeRange();
  days.set(DayOfWeek.MONDAY, [{ ...defaultTime }]);
  days.set(DayOfWeek.TUESDAY, [{ ...defaultTime }]);
  days.set(DayOfWeek.WEDNESDAY, [{ ...defaultTime }]);
  days.set(DayOfWeek.THURSDAY, [{ ...defaultTime }]);
  days.set(DayOfWeek.FRIDAY, [{ ...defaultTime }]);
  return { days };
}
