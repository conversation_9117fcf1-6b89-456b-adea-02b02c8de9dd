@use 'design-tokens' as *;

.badge-with-duration {
  display: flex;
  align-items: center;
  gap: $spacing-2;
  color: $tertiary-font-color;
  margin: 5px 0px;
}

.date-time-inputs {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: space-between;
}

.date-time-input-width {
  width: 49%;
}

.event-type-item {
  display: flex;
  flex-direction: column;
}

.contact-email {
  display: flex;
  color: $tertiary-font-color;
}

.contact-missing-email {
  display: flex;
  align-items: center;
}

.contact-missing-icon {
  color: red;
  margin-right: 5px;
}

.event-type-optgroup {
  font-weight: bolder;
}

.any-team-member-label {
  color: $secondary-font-color;
}
.guideline-add-contact-spacing {
  padding-top: 10px;
}
