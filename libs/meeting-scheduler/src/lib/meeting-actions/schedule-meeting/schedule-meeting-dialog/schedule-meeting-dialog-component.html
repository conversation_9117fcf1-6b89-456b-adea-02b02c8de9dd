<h2 mat-dialog-title>{{ 'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.BOOK_MEETING' | translate }}</h2>
<mat-dialog-content>
  <form [formGroup]="meetingScheduleForm">
    <glxy-form-field prefixIcon="search">
      <glxy-label>{{ 'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.CONTACT.PLACEHOLDER' | translate }}</glxy-label>
      <input
        type="text"
        matInput
        formControlName="userName"
        [matAutocomplete]="userNameAuto"
        placeholder="{{ 'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.CONTACT.PLACEHOLDER' | translate }}"
      />
      <mat-autocomplete #userNameAuto="matAutocomplete">
        <mat-option
          *ngIf="userList.length === 0 && !isContactLoading && getFormValue('userName') !== ''"
          disabled="true"
        >
          {{ 'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.CONTACT.EMPTY_STATE' | translate }}
        </mat-option>
        <mat-option
          *ngFor="let user of userList"
          [value]="user.name"
          (click)="updateInvitee(user)"
          [disabled]="!user.email"
        >
          {{ user.name }}
          <div *ngIf="!user.email" class="contact-missing-email">
            <mat-icon class="contact-missing-icon"> warning </mat-icon>
            {{ 'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.CONTACT.MISSING_EMAIL' | translate }}
          </div>
          <span class="contact-email" *ngIf="user.email">{{ user.email }}</span>
        </mat-option>
        <mat-option *ngIf="isContactLoading">
          <glxy-loading-spinner [inline]="true" />
        </mat-option>
      </mat-autocomplete>
      <div class="guideline-add-contact-spacing"></div>
      <a (click)="createContact()">{{ 'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.ADD_CONTACT' | translate }}</a>
    </glxy-form-field>

    <glxy-form-field>
      <mat-chip-grid
        #additionalGuestEmailGrid
        [attr.aria-label]="'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.GUEST_EMAIL' | translate"
      >
        <mat-chip-row *ngFor="let emailID of inviteeEmails" [removable]="true" (removed)="removeInviteeEmail(emailID)">
          {{ emailID }}
          <mat-icon matChipRemove>cancel</mat-icon>
        </mat-chip-row>

        <input
          placeholder="{{ 'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.GUEST_EMAIL.PLACEHOLDER' | translate }}"
          [matChipInputFor]="additionalGuestEmailGrid"
          [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
          [matChipInputAddOnBlur]="true"
          (matChipInputTokenEnd)="addInviteeEmail($event.value)"
          formControlName="additionalEmails"
        />
      </mat-chip-grid>
    </glxy-form-field>

    <glxy-form-field prefixIcon="search">
      <glxy-label>{{ 'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.EVENT_TYPE' | translate }}</glxy-label>
      <input
        type="text"
        matInput
        formControlName="eventType"
        [matAutocomplete]="eventTypeAuto"
        placeholder="{{ 'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.EVENT_TYPE.PLACEHOLDER' | translate }}"
      />
      <mat-autocomplete #eventTypeAuto="matAutocomplete" [displayWith]="displayMeetingType">
        <!-- Personal Events -->
        <ng-container *ngIf="filteredPersonalMeetingTypes$ | async as personalMeetingTypes">
          <ng-container *ngIf="personalMeetingTypes.length > 0">
            <mat-optgroup
              class="event-type-optgroup"
              label="{{ 'MEETING_SCHEDULER.MEETING_LIST.PERSONAL_EVENTS' | translate }}"
            >
              <mat-option *ngFor="let option of personalMeetingTypes" [value]="option">
                {{ option.name }}
                <div class="badge-with-duration">{{ option.duration | duration }}</div>
              </mat-option>
            </mat-optgroup>
          </ng-container>
        </ng-container>

        <!-- Team Events -->
        <ng-container *ngIf="filteredTeamMeetingTypes$ | async as teamEventTypes ">
          <ng-container *ngIf="teamEventTypes.length > 0">
            <mat-optgroup
              class="event-type-optgroup"
              label="{{ 'MEETING_SCHEDULER.MEETING_LIST.TEAM_EVENTS' | translate }}"
            >
              <mat-option *ngFor="let option of teamEventTypes" [value]="option">
                {{ option.name }}
                <div class="badge-with-duration">{{ option.duration | duration }}</div>
              </mat-option>
            </mat-optgroup>
          </ng-container>
        </ng-container>
      </mat-autocomplete>
    </glxy-form-field>

    <glxy-form-field
      *ngIf="getFormValue('eventType')?.id && getFormValue('eventType')?.locationType !== MeetingLocationType.VIDEO"
    >
      <glxy-label>{{ 'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.LOCATION' | translate }}</glxy-label>
      <input
        matInput
        formControlName="location"
        type="text"
        [value]="meetingScheduleForm.get('location')"
        [disabled]="getFormValue('eventType')?.locationType === MeetingLocationType.IN_PERSON_USER_SITE"
        placeholder="{{ 'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.LOCATION.PLACEHOLDER' | translate }}"
      />
      <div *ngIf="locationGuideline" class="guideline-add-contact-spacing">Guideline - {{locationGuideline}}</div>
    </glxy-form-field>

    <glxy-form-field
      *ngIf="displayTeamMemberSelection && getFormValue('eventType')?.meetingType !== TeamEventMeetingType.MULTI_HOST"
    >
      <glxy-label>{{ 'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.TEAM_MEMBER' | translate }}</glxy-label>
      <mat-select
        placeholder="{{ 'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.TEAM_MEMBER.PLACEHOLDER' | translate }}"
        formControlName="teamMemberID"
      >
        <mat-option [value]="'any'">
          <span class="any-team-member-label">
            {{ 'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.TEAM_MEMBER.ANY' | translate }}
          </span>
        </mat-option>
        <mat-option *ngFor="let user of getFormValue('eventType')?.hostUsers" [value]="user.userId">
          {{ user.displayName }}
        </mat-option>
      </mat-select>
    </glxy-form-field>

    <glxy-form-field
      *ngIf="displayTeamMemberSelection && getFormValue('eventType')?.meetingType === TeamEventMeetingType.MULTI_HOST"
    >
      <glxy-label>{{ 'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.HOST' | translate }}</glxy-label>
      <span> {{ hostUserDisplayNames() }}. </span>
    </glxy-form-field>

    <glxy-form-field *ngIf="hostPreferences$ | async as hostPreference">
      <glxy-timezone-selector
        [initialTimezone]="this.timezone"
        (timezoneChanged)="onTimezoneChanged($event)"
      ></glxy-timezone-selector>
    </glxy-form-field>

    <div class="date-time-inputs">
      <div class="date-time-input-width">
        <glxy-form-field>
          <glxy-label> {{ 'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.DATE_TIME' | translate }} </glxy-label>

          <mat-datepicker-toggle
            matPrefix
            [for]="meetingScheduleDate"
            meetingSchedulerDatePickerNavigation
            (prevClicked)="onPrevMonth()"
            (nextClicked)="onNextMonth()"
            data-calendar-id="meeting-calendar-1"
          ></mat-datepicker-toggle>
          <input
            matInput
            placeholder="{{ 'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.DATE_TIME.DATE.PLACEHOLDER' | translate }}"
            [matDatepicker]="meetingScheduleDate"
            (click)="meetingScheduleDate.open()"
            [matDatepickerFilter]="validateDateFn"
            formControlName="meetingDate"
            [min]="calenderDateStart"
            [max]="calenderDateEnd"
          />
          <mat-datepicker #meetingScheduleDate [disabled]="isCalenderDisabled" />
        </glxy-form-field>
      </div>
      <div class="date-time-input-width">
        <glxy-form-field>
          <mat-select
            placeholder="{{ 'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.DATE_TIME.TIME.PLACEHOLDER' | translate}}"
            formControlName="meetingTime"
            [disabled]="isCalenderDisabled"
          >
            <mat-option *ngFor="let slot of availableTimeSlots" [value]="slot"> {{slot}} </mat-option>
          </mat-select>
        </glxy-form-field>
      </div>
    </div>
  </form>
</mat-dialog-content>
<mat-dialog-actions>
  <button mat-stroked-button mat-dialog-close>{{ 'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.CLOSE' | translate }}</button>
  <button mat-flat-button color="primary" (click)="bookMeeting()" [disabled]="disableBookMeeting()">
    <glxy-button-loading-indicator [isLoading]="isBookingSubmitted">
      {{ 'MEETING_SCHEDULER.MEETING_ACTION.DIALOG.BOOK_MEETING' | translate }}
    </glxy-button-loading-indicator>
  </button>
</mat-dialog-actions>
