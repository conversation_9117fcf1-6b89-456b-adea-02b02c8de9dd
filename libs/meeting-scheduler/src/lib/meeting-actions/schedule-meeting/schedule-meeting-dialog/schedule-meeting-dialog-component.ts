import { AfterViewInit, Component, inject, Inject, Injector, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { distinctUntilChanged, firstValueFrom, fromEvent, map, Observable, Subscription } from 'rxjs';
import {
  createCrmObjectModalProviders,
  CrmCreateCrmObjectModalComponent,
  CrmCreateCrmObjectModalResult,
  CrmFieldService,
  StandardExternalIds,
} from '@galaxy/crm/static';
import { MatDialog } from '@angular/material/dialog';
import {
  GroupMeetingType,
  MeetingSchedulerStoreService,
} from '../../../data-providers/meeting-scheduler-store.service';
import { CRMApiService, CrmObject, CrmObjectSearch, ListCrmObjectsRequest } from '@vendasta/crm';
import { debounceTime, startWith, switchMap } from 'rxjs/operators';
import { PERSONAL_CALENDAR_ID_TOKEN } from '../../../data-providers/providers';
import { MatAutocomplete, MatAutocompleteTrigger } from '@angular/material/autocomplete';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import {
  MeetingLocationType,
  MeetingType,
  Preferences,
  TimeSpan,
  Contact,
  TeamEventMeetingType,
} from '@vendasta/meetings';
import { Calendar } from '@vendasta/meetings/lib/shared/calendar';
import moment from 'moment';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { TranslateService } from '@ngx-translate/core';
import { TEAM_EVENT_TYPE, POSTHOG_KEYS, POSTHOG_CATEGORIES, POSTHOG_ACTIONS } from '../../../constants';
import { ProductAnalyticsService } from '@vendasta/product-analytics';

const PAGE_SIZE = 25;

export interface ContactData {
  contactID: string;
  accountGroupID: string;
  email: string;
  name: string;
  phoneNumber: string;
  firstName: string;
  lastName: string;
}

@Component({
  selector: 'meeting-scheduler-schedule-meeting-dialog',
  templateUrl: './schedule-meeting-dialog-component.html',
  styleUrls: ['./schedule-meeting-dialog-component.scss'],
  standalone: false,
})
export class ScheduleMeetingDialogComponent implements OnInit, AfterViewInit, OnDestroy {
  private readonly injector = inject(Injector);
  private readonly objectType = 'Contact';
  private lastGetUserListRequest: any;
  @ViewChild(MatAutocompleteTrigger) autocompleteTrigger!: MatAutocompleteTrigger;
  @ViewChild(MatAutocomplete) autocomplete!: MatAutocomplete;
  private dateTimeMap: Map<string, string[]> = new Map<string, string[]>();
  isBookingSubmitted = false;
  isBookingSubmitDisabled = true;

  personalMeetingTypes$: Observable<MeetingType[]>;
  groupMeetingTypes$: Observable<{ [calendarId: string]: GroupMeetingType[] }>;
  teamMeetingTypes$: Observable<MeetingType[]>;
  filteredPersonalMeetingTypes$: Observable<MeetingType[]>;
  filteredTeamMeetingTypes$: Observable<MeetingType[]>;

  meetingScheduleForm = new FormGroup({
    userName: new FormControl(''),
    userID: new FormControl(''),
    eventType: new FormControl<MeetingType | null>(null),
    location: new FormControl({ value: null, disabled: true, guideline: null }),
    teamMemberID: new FormControl(''),
    meetingDate: new FormControl<Date | null>(null),
    meetingTime: new FormControl(''),
    additionalEmails: new FormControl('', [Validators.email]),
  });

  scrollSubscription?: Subscription;

  subscription: Subscription[] = [];

  calender: Calendar;

  selectedUser: ContactData;
  cursor = '';
  userList: ContactData[] = [];
  isContactLoading = false;
  hasMoreContacts = false;
  inviteeEmails = new Set<string>();
  separatorKeysCodes: number[] = [ENTER, COMMA];
  locationGuideline = '';
  displayTeamMemberSelection = false;
  isCalenderDisabled = true;
  timezone = '';
  skipUserNameChange = false;

  hostPreferences$: Observable<Preferences>;
  availableTimeSlots: string[];
  calenderDateStart: Date;
  calenderDateEnd: Date;
  // Additional Email Support Functions
  currentMonth: Date = new Date(); // starting from current month
  maxMonths = 3;

  monthlyDateTimeMap = new Map<string, string[]>(); // cache by month key "YYYY-MM"
  validateDateFn: (d: Date | null) => boolean = () => false;
  constructor(
    private readonly dialog: MatDialog,
    private readonly crmService: CRMApiService,
    private readonly crmFieldService: CrmFieldService,
    @Inject(PERSONAL_CALENDAR_ID_TOKEN) public readonly personalCalendarId$: Observable<string>,
    private readonly meetingSchedulerStoreService: MeetingSchedulerStoreService,
    private snackbarService: SnackbarService,
    private readonly translate: TranslateService,
    private readonly analyticsService: ProductAnalyticsService,
  ) {
    this.calculateCalenderDateEnd();
  }

  // Generic term for partner & SMB ID
  namespace: string;

  formatDateWithoutTimezone(date: Date | null): string {
    const options: Intl.DateTimeFormatOptions = {
      // timeZone: this.timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    };
    // Format date parts using Intl.DateTimeFormat
    const formatter = new Intl.DateTimeFormat('en-CA', options); // en-CA => YYYY-MM-DD
    return formatter.format(date);
  }

  formatDate(date: Date | null): string {
    const options: Intl.DateTimeFormatOptions = {
      timeZone: this.timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    };
    // Format date parts using Intl.DateTimeFormat
    const formatter = new Intl.DateTimeFormat('en-CA', options); // en-CA => YYYY-MM-DD
    return formatter.format(date);
  }

  validateDate = (d: Date | null): boolean => {
    if (!d) return false;
    const dateString = this.formatDateWithoutTimezone(d);
    return this.dateTimeMap.has(dateString);
  };

  disableBookMeeting(): boolean {
    if (!this.selectedUser?.name) {
      return true;
    }
    if (!this.getFormValue('meetingTime')) {
      return true;
    }
    return false;
  }

  ngOnInit() {
    // Fetch user preferred timezone
    this.validateDateFn = this.validateDate.bind(this);
    this.hostPreferences$ = this.meetingSchedulerStoreService.loadUserPreferences();
    this.subscription.push(
      this.hostPreferences$.subscribe((hostPreference) => {
        this.timezone = hostPreference?.timezone?.id;
      }),

      // Fetch user calender information

      this.meetingSchedulerStoreService.loadPersonalCalendar().subscribe((calendar) => {
        this.calender = calendar;
        if (calendar.applicationContext?.user_context === 'SMB') {
          this.namespace = calendar.applicationContext.business_id;
        } else {
          this.namespace = calendar.applicationContext.partner_id;
        }
      }),
    );

    // fetch event types of user
    this.personalMeetingTypes$ = this.meetingSchedulerStoreService.loadPersonalMeetingTypes({ returnCache: true }).pipe(
      map((meetingTypes) => {
        if (!meetingTypes) {
          return null;
        }
        return meetingTypes.sort((mt1, mt2) => {
          return mt1.name.localeCompare(mt2.name, undefined, { numeric: true });
        });
      }),
    );

    this.groupMeetingTypes$ = this.meetingSchedulerStoreService.loadGroupMeetingTypes({ returnCache: true });
    this.teamMeetingTypes$ = this.groupMeetingTypes$.pipe(
      map((groupMeetingTypes) => meetingTypeMapToArray(groupMeetingTypes)),
    );

    // Filter event type from personal list
    this.filteredPersonalMeetingTypes$ = this.meetingScheduleForm.get('eventType')!.valueChanges.pipe(
      startWith(''), // Initial empty value
      switchMap((value) => this._personal_meeting_type_filter(typeof value === 'string' ? value : value?.name || '')),
    );

    // Filter event type from team list
    this.filteredTeamMeetingTypes$ = this.meetingScheduleForm.get('eventType')!.valueChanges.pipe(
      startWith(''), // Initial empty value
      switchMap((value) => this._team_meeting_type_filter(typeof value === 'string' ? value : value?.name || '')),
    );

    // Fetch user from contact list
    this.subscription.push(
      this.meetingScheduleForm
        .get('userName')!
        .valueChanges.pipe(debounceTime(300), distinctUntilChanged())
        .subscribe(() => {
          if (this.skipUserNameChange) {
            this.skipUserNameChange = false;
            return;
          }
          this.selectedUser = {} as ContactData;
          this.fetchContacts(false);
        }),

      // Update states on event type selection
      this.meetingScheduleForm
        .get('eventType')!
        .valueChanges.pipe(distinctUntilChanged())
        .subscribe((meetingType: MeetingType | null) => {
          this.locationGuideline = '';
          this.meetingScheduleForm.get('location')!.setValue(null);
          this.meetingScheduleForm.get('location')!.disable();
          this.displayTeamMemberSelection = false;
          this.isBookingSubmitDisabled = true;
          this.meetingScheduleForm.get('teamMemberID')!.setValue(null);
          this.meetingScheduleForm.get('meetingDate')!.setValue(null);
          this.meetingScheduleForm.get('meetingTime')!.setValue(null);
          this.isCalenderDisabled = true;
          if (meetingType?.id) {
            if (
              meetingType.CalendarType.trim() === TEAM_EVENT_TYPE &&
              (meetingType.isClientChoice ||
                this.getFormValue('eventType')?.meetingType === TeamEventMeetingType.MULTI_HOST ||
                this.getFormValue('eventType')?.meetingType === TeamEventMeetingType.CLIENT_CHOICE)
            ) {
              this.displayTeamMemberSelection = true;
              if (this.getFormValue('eventType')?.meetingType === TeamEventMeetingType.MULTI_HOST) {
                this.meetingScheduleForm.get('eventType')!.setValue(meetingType);
                this.resetCalendarAndFetch();
              }
            } else {
              this.meetingScheduleForm.get('eventType')!.setValue(meetingType);
              this.resetCalendarAndFetch();
            }
            if (meetingType.locationType === MeetingLocationType.IN_PERSON_USER_SITE) {
              this.meetingScheduleForm.get('location')!.setValue(meetingType.location);
              this.meetingScheduleForm.get('location')!.disable();
            } else if (meetingType.locationType === MeetingLocationType.IN_PERSON_CLIENT_SITE) {
              this.meetingScheduleForm.get('location')!.setValue(null);
              this.meetingScheduleForm.get('location')!.enable();
              this.locationGuideline = meetingType.locationGuidelines;
            }
          }
        }),

      // Update states on team member selection
      this.meetingScheduleForm.get('teamMemberID')!.valueChanges.subscribe(() => {
        // To handle null set during meeting type update not trigger fetchCalenderAvailability()
        if (this.getFormValue('teamMemberID')) {
          this.isCalenderDisabled = true;
          this.isBookingSubmitDisabled = true;
          this.meetingScheduleForm.get('meetingDate')!.setValue(null);
          this.meetingScheduleForm.get('meetingTime')!.setValue(null);
          this.resetCalendarAndFetch();
        }
      }),

      // Update states on meeting date selection
      this.meetingScheduleForm.get('meetingDate')!.valueChanges.subscribe((meetingDate) => {
        this.meetingScheduleForm.get('meetingTime')!.setValue(null);
        this.isBookingSubmitDisabled = true;
        this.availableTimeSlots = this.dateTimeMap.get(this.formatDateWithoutTimezone(meetingDate));
      }),

      // Update state on meeting time selection
      this.meetingScheduleForm.get('meetingTime')!.valueChanges.subscribe(() => {
        // Field should not be enabled when value is set to null
        if (this.meetingScheduleForm.get('meetingTime')) {
          this.isBookingSubmitDisabled = false;
        }
      }),
    );
  }

  private _personal_meeting_type_filter(value: string): Observable<MeetingType[]> {
    const filterValue = value?.toLowerCase() || '';

    return this.personalMeetingTypes$.pipe(
      map((meetingTypes: MeetingType[]) =>
        meetingTypes.filter((meetingType) => meetingType.name.toLowerCase().includes(filterValue)),
      ),
    );
  }

  private _team_meeting_type_filter(value: string): Observable<MeetingType[]> {
    const filterValue = value?.toLowerCase() || '';

    return this.teamMeetingTypes$.pipe(
      map((meetingTypes: GroupMeetingType[]) =>
        meetingTypes.filter((meetingType) => meetingType.name.toLowerCase().includes(filterValue)),
      ),
    );
  }

  ngOnDestroy() {
    this.subscription.forEach((sub) => sub.unsubscribe());
    this.subscription = [];
    this.scrollSubscription?.unsubscribe(); // Clean up subscriptions
  }

  // Event Type Support Function
  displayMeetingType(meetingType?: MeetingType): string {
    if (!meetingType) {
      return '';
    }
    return meetingType ? meetingType.name : '';
  }
  // Event Type Support Function

  // Additional Email Support Functions
  addInviteeEmail(value: string): void {
    if (this.meetingScheduleForm.get('additionalEmails').invalid) {
      return;
    }
    const trimmedValue = (this.getFormValue('additionalEmails') || '').trim();
    if (!trimmedValue) {
      return;
    }
    this.inviteeEmails.add(value);
    this.resetInviteeEmailInput();
  }

  private resetInviteeEmailInput(): void {
    this.meetingScheduleForm.get('additionalEmails').setValue('');
    this.meetingScheduleForm.get('additionalEmails').markAsPristine();
  }

  removeInviteeEmail(value: string): void {
    this.inviteeEmails.delete(value);
  }

  getMonthKey(date: Date): string {
    return date.toISOString().slice(0, 7); // "YYYY-MM"
  }

  calculateCalenderDateEnd() {
    const now = new Date();
    const targetMonth = new Date(now);
    targetMonth.setMonth(targetMonth.getMonth() + this.maxMonths);

    // go to next month & day 0 = last day of previous month
    const endOfMonthLocal = new Date(targetMonth.getFullYear(), targetMonth.getMonth() + 1, 0, 23, 59, 59, 999);

    this.calenderDateEnd = endOfMonthLocal;
  }

  getStartOfMonth(year: number, month: number): Date {
    return new Date(year, month, 1, 0, 0, 0, 0);
  }

  getEndOfMonth(year: number, month: number): Date {
    return new Date(year, month + 1, 0, 23, 59, 59, 999);
  }

  private resetCalendarAndFetch(): void {
    this.monthlyDateTimeMap = new Map<string, string[]>();
    this.dateTimeMap = new Map<string, string[]>();
    this.currentMonth = new Date();
    this.fetchCalendarAvailabilityForMonth(this.currentMonth);
  }

  getFormValue(controlName: string) {
    return this.meetingScheduleForm.get(controlName)?.value;
  }

  fetchCalendarAvailabilityForMonth(monthStart: Date): void {
    const monthKey = this.getMonthKey(monthStart);

    // Skip if data already cached
    if (this.monthlyDateTimeMap.has(monthKey)) return;

    this.isCalenderDisabled = true;

    const now = new Date();
    const startOfMonth = this.getStartOfMonth(monthStart.getFullYear(), monthStart.getMonth());
    const endOfMonth = this.getEndOfMonth(monthStart.getFullYear(), monthStart.getMonth());

    const start = now > startOfMonth ? now : startOfMonth;
    const end = endOfMonth > this.calenderDateEnd ? this.calenderDateEnd : endOfMonth;

    const meetingType = this.getFormValue('eventType');
    if (!meetingType?.id) return this.finishLoading();

    let userID = this.getFormValue('teamMemberID') || '';
    if (meetingType.isClientChoice && !userID) return this.finishLoading();
    if (userID === 'any') userID = '';

    const timeRange: TimeSpan = { start, end };

    const response = this.meetingSchedulerStoreService.loadAvailableTimeSlots({
      calendarId: meetingType.calendarId,
      meetingTypeId: meetingType.id,
      timeZone: { id: this.timezone },
      userID,
      timeSpan: timeRange,
    });

    this.subscription.push(
      response.subscribe((availableTimeSlots) => {
        const monthDateMap = new Map<string, string[]>();

        for (const slot of availableTimeSlots) {
          const date = this.formatDate(slot.start); // YYYY-MM-DD
          const time = new Date(slot.start).toLocaleTimeString('en-US', {
            timeZone: this.timezone,
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          });

          this.pushToMapList(monthDateMap, date, time);
          this.pushToMapList(this.dateTimeMap, date, time);
        }

        // Cache flat times for this month
        const flatTimes = Array.from(monthDateMap.entries()).reduce<string[]>(
          (acc, [, times]) => acc.concat(times),
          [],
        );
        this.monthlyDateTimeMap.set(monthKey, flatTimes);

        const allDates = Array.from(this.dateTimeMap.keys()).sort();
        if (allDates.length) {
          this.calenderDateStart = new Date(allDates[0]);
        }

        this.finishLoading();
      }),
    );
  }

  private pushToMapList(map: Map<string, string[]>, key: string, value: string): void {
    let list = map.get(key);
    if (!list) {
      list = [];
      map.set(key, list);
    }
    list.push(value);
  }

  private finishLoading(): void {
    this.isCalenderDisabled = false;
    this.validateDateFn = this.validateDate.bind(this);
  }

  onNextMonth(): void {
    const nextMonth = new Date(this.currentMonth);
    nextMonth.setMonth(nextMonth.getMonth() + 1);

    const maxAllowedDate = new Date(Date.now());
    maxAllowedDate.setMonth(maxAllowedDate.getMonth() + this.maxMonths);

    // Allow navigation up to the same day 3 months ahead
    if (nextMonth > maxAllowedDate) return;

    this.currentMonth = nextMonth;
    this.fetchCalendarAvailabilityForMonth(nextMonth);
  }

  onPrevMonth(): void {
    const prevMonth = new Date(this.currentMonth);
    prevMonth.setMonth(prevMonth.getMonth() - 1);

    const now = new Date(Date.now());
    const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    // no going before current month
    if (prevMonth < currentMonthStart) return;

    this.currentMonth = prevMonth;
    this.fetchCalendarAvailabilityForMonth(prevMonth);
  }

  // User Name Support Functions
  ngAfterViewInit() {
    // User Name field enable scroller for infinite scrolling
    this.subscription.push(
      this.autocomplete.opened.subscribe(() => {
        setTimeout(() => {
          const panel = this.autocomplete.panel?.nativeElement;
          if (panel) {
            this.attachScrollListener(panel);
          }
        }, 0);
      }),
    );
  }

  attachScrollListener(panel: HTMLElement) {
    if (this.scrollSubscription) {
      this.scrollSubscription.unsubscribe(); // Unsubscribe previous listener if any
    }

    this.scrollSubscription = fromEvent(panel, 'scroll')
      .pipe(debounceTime(100))
      .subscribe(() => {
        if (
          !this.isContactLoading &&
          panel.scrollTop + panel.clientHeight >= panel.scrollHeight - 10 &&
          this.hasMoreContacts
        ) {
          this.fetchContacts(true);
        }
      });
  }

  private openCreateCrmObjectModal(): Observable<CrmCreateCrmObjectModalResult> {
    return this.dialog
      .open(CrmCreateCrmObjectModalComponent, {
        data: { objectType: this.objectType },
        injector: createCrmObjectModalProviders(this.objectType, this.injector),
      })
      .afterClosed();
  }

  async createContact() {
    const createModalResult = await firstValueFrom(this.openCreateCrmObjectModal());
    if (createModalResult?.crmObject?.crmObjectId) {
      const userObject = createModalResult.crmObject as CrmObject;
      const email = this.getEmailForContact(userObject);
      if (!email) {
        this.snackbarService.openErrorSnack(
          this.translate.instant('MEETING_SCHEDULER.MEETING_ACTION.DIALOG.CONTACT.CREATE_CONTACT.MISSING_EMAIL'),
        );
        return;
      }
      this.selectedUser = {
        firstName: this.getFirstNameForContact(userObject),
        lastName: this.getLastNameForContact(userObject),
        email: this.getEmailForContact(userObject),
        phoneNumber: this.getPhoneNumberForContact(userObject),
        name: this.getNameForContact(userObject),
      } as ContactData;
      this.skipUserNameChange = true;
      this.meetingScheduleForm.get('userName').setValue(this.selectedUser.name, { emitEvent: false });
    }
  }

  getEmailForContact(contact: CrmObject): string {
    return this.crmFieldService.getFieldValueFromCrmObject(contact, StandardExternalIds.Email)?.stringValue;
  }

  getPhoneNumberForContact(contact: CrmObject): string {
    return this.crmFieldService.getFieldValueFromCrmObject(contact, StandardExternalIds.PhoneNumber)?.stringValue;
  }

  getFirstNameForContact(contact: CrmObject): string {
    return this.crmFieldService.getFieldValueFromCrmObject(contact, StandardExternalIds.FirstName)?.stringValue;
  }

  getLastNameForContact(contact: CrmObject): string {
    return this.crmFieldService.getFieldValueFromCrmObject(contact, StandardExternalIds.LastName)?.stringValue;
  }

  getNameForContact(contact: CrmObject): string {
    const firstName = this.crmFieldService.getFieldValueFromCrmObject(
      contact,
      StandardExternalIds.FirstName,
    )?.stringValue;
    const lastName = this.crmFieldService.getFieldValueFromCrmObject(
      contact,
      StandardExternalIds.LastName,
    )?.stringValue;

    const fullName = [firstName, lastName].filter(Boolean).join(' ').trim();

    if (fullName) {
      return fullName;
    }
    return this.crmFieldService.getFieldValueFromCrmObject(contact, StandardExternalIds.Email)?.stringValue || '';
  }

  public fetchContacts(isLoadMore: boolean): void {
    let searchTerm = '';
    if (!isLoadMore) {
      this.cursor = '';
      searchTerm = this.getFormValue('userName');
    }

    if (this.lastGetUserListRequest) {
      this.lastGetUserListRequest.unsubscribe();
    }

    const request = {
      namespace: this.namespace,
      search: { searchTerm } as CrmObjectSearch,
      pagingOptions: { pageSize: PAGE_SIZE, cursor: this.cursor },
    } as ListCrmObjectsRequest;

    this.isContactLoading = true;
    this.lastGetUserListRequest = this.crmService.listContacts(request).subscribe({
      next: (response) => {
        const contacts =
          (response?.crmObjects?.map((contact: CrmObject) => ({
            name: this.getNameForContact(contact),
            email: this.getEmailForContact(contact),
            phoneNumber: this.getPhoneNumberForContact(contact),
            contactID: contact.crmObjectId,
            firstName: this.getFirstNameForContact(contact),
            lastName: this.getLastNameForContact(contact),
          })) as ContactData[]) ?? [];
        if (isLoadMore) {
          this.userList = [...this.userList, ...contacts];
        } else {
          this.userList = contacts;
        }
        this.hasMoreContacts = response?.pagingMetadata?.hasMore;
        this.cursor = response?.pagingMetadata?.nextCursor;
        this.isContactLoading = false;
      },
      error: () => {
        this.isContactLoading = false;
      },
    });
  }

  updateInvitee(user: ContactData) {
    this.selectedUser = user;
    this.skipUserNameChange = true;
  }
  // User Name Support Functions

  // Timezone Support Functions
  onTimezoneChanged(timezone: string): void {
    if (this.timezone === timezone) {
      return;
    }
    this.timezone = timezone;
    this.isBookingSubmitDisabled = true;
    this.meetingScheduleForm.get('meetingDate')!.setValue(null);
    this.meetingScheduleForm.get('meetingTime')!.setValue(null);
    this.resetCalendarAndFetch();
  }

  // Meeting Booking Support Functions
  bookMeeting() {
    this.isBookingSubmitted = true;
    const meetingType: MeetingType = this.getFormValue('eventType');

    const contacts: Contact[] = [];
    contacts.push(
      new Contact({
        firstName: this.selectedUser.firstName,
        lastName: this.selectedUser.lastName,
        isPrimary: true,
        email: this.selectedUser.email,
        phoneNumber: this.selectedUser.phoneNumber,
        timeZone: { id: this.timezone },
      }),
    );

    for (const emailID of this.inviteeEmails) {
      contacts.push(new Contact({ email: emailID, isPrimary: false, timeZone: { id: this.timezone } }));
    }

    const dateString = moment(this.getFormValue('meetingDate')).format('YYYY-MM-DD');
    const dateTimeString = `${dateString} ${this.getFormValue('meetingTime')}`;
    const localDateTime = moment.tz(dateTimeString, 'YYYY-MM-DD hh:mm A', this.timezone);
    const meetingStartTime = localDateTime.toDate();
    const location = this.getFormValue('location') || '';
    let userID = this.getFormValue('teamMemberID') || '';
    if (userID === 'any') {
      userID = '';
    }

    this.subscription.push(
      this.meetingSchedulerStoreService
        .hostBookMeeting({
          calendarId: meetingType.calendarId,
          meetingTypeId: meetingType.id,
          start: meetingStartTime,
          attendees: contacts,
          location: location,
          comment: '',
          userId: userID,
        })
        .subscribe({
          next: () => {
            this.isBookingSubmitted = false;
            this.analyticsService.trackEvent(
              POSTHOG_KEYS.MEETING_BOOKED_FROM_MY_MEETINGS,
              POSTHOG_CATEGORIES.USER,
              POSTHOG_ACTIONS.CLICK,
            );
            this.snackbarService.openSuccessSnack(
              this.translate.instant('MEETING_SCHEDULER.MEETING_ACTION.DIALOG.SUCCESS_MESSAGE'),
            );
            this.dialog.closeAll();
          },
          error: (err) => {
            this.isBookingSubmitted = false;
            console.error('Error booking meeting:', err);
            this.snackbarService.openErrorSnack(
              this.translate.instant('MEETING_SCHEDULER.MEETING_ACTION.DIALOG.ERROR_MESSAGE') +
                'Error Message - ' +
                err.error.message +
                '. Error Code - ' +
                err.error.code,
            );
          },
        }),
    );
    return;
  }
  // Meeting Booking Support Functions

  hostUserDisplayNames(): string {
    const hostUsers = this.getFormValue('eventType')?.hostUsers;
    return Array.isArray(hostUsers) ? hostUsers.map((host) => host.displayName).join(', ') : '';
  }

  protected readonly MeetingLocationType = MeetingLocationType;
  protected readonly TeamEventMeetingType = TeamEventMeetingType;
}

function meetingTypeMapToArray(meetingTypeMap: { [key: string]: MeetingType[] }): MeetingType[] {
  return Object.values(meetingTypeMap || {}).reduce((allTeamMeetingTypes, meetingTypes) => {
    return [...allTeamMeetingTypes, ...meetingTypes];
  }, []);
}
