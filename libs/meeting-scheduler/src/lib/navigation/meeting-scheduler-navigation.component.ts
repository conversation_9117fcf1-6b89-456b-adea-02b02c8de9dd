import { Component, Inject, OnD<PERSON>roy, OnInit, Signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { Observable, Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import { MeetingSchedulerNavActionButton } from '../interface';
import { BreadCrumb } from '../sales-center-navigation/breadcrumbs.component';
import { MeetingSchedulerNavigationBreadcrumbService } from './navigation-breadcrumbs.service';
import { FEATURE_GROUPS_AND_SERVICES_TOKEN } from '../data-providers/providers';

@Component({
  selector: 'meeting-scheduler-navigation',
  templateUrl: './meeting-scheduler-navigation.component.html',
  styleUrls: ['./meeting-scheduler-navigation.component.scss'],
  standalone: false,
})
export class MeetingSchedulerNavigationComponent implements OnInit, OnDestroy {
  private readonly breadcrumbs$: Observable<BreadCrumb[]>;
  protected readonly previousPage$: Observable<{ text: string; url: string } | null>;
  protected readonly currentPageTitle$: Observable<string>;
  protected readonly actions: Signal<MeetingSchedulerNavActionButton[]>;
  private readonly subscriptions: Subscription[] = [];

  hideBackNavigation = false;
  displayBookMeetingButton = false;

  constructor(
    private readonly navigationService: MeetingSchedulerNavigationBreadcrumbService,
    @Inject(FEATURE_GROUPS_AND_SERVICES_TOKEN) readonly featureGroupsandServicesEnabled$: Observable<boolean>,
  ) {
    this.breadcrumbs$ = this.navigationService.breadcrumbs$.pipe(
      map((msBreadcrumbs) => {
        return (msBreadcrumbs || []).map((b) => {
          return {
            label: b.text,
            link: b.url || undefined, // an empty string link will break the nav
          };
        });
      }),
    );

    this.currentPageTitle$ = this.breadcrumbs$.pipe(
      map((breadcrumbs) => {
        return breadcrumbs[breadcrumbs.length - 1]?.label || '';
      }),
    );

    this.actions = toSignal(this.navigationService.actions$);
  }

  ngOnInit() {
    this.subscriptions.push(
      this.navigationService.hideBackNavigation$.subscribe((value) => {
        this.hideBackNavigation = value;
      }),
      this.navigationService.displayBookMeetingButton$.subscribe((value) => {
        this.displayBookMeetingButton = value;
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }
}
