<glxy-page [pagePadding]="false">
  <glxy-page-toolbar>
    <glxy-page-nav *ngIf="!this.hideBackNavigation">
      <glxy-page-nav-button
        [useHistory]="true"
        [historyBackButtonTitle]="'COMMON.ACTION_LABELS.BACK' | translate"
      ></glxy-page-nav-button>
    </glxy-page-nav>
    <glxy-page-title>
      <strong>{{ currentPageTitle$ | async }}</strong>
    </glxy-page-title>
    <div class="nav-content-and-action-items">
      @if (actions()) {
        <glxy-page-actions>
          @for (action of actions(); track action) {
            @if (action.link && action.navigationOption === 'routerLink') {
              <a [routerLink]="action.link">
                <ng-container *ngTemplateOutlet="actionButtonTemplate; context: { action: action }"></ng-container>
              </a>
            }
            @if (action.link && (!action.navigationOption || action.navigationOption === 'href')) {
              <a [href]="action.link">
                <ng-container *ngTemplateOutlet="actionButtonTemplate; context: { action: action }"></ng-container>
              </a>
            }
            @if (!action.link) {
              <ng-container *ngTemplateOutlet="actionButtonTemplate; context: { action: action }"></ng-container>
            }
          }
        </glxy-page-actions>
      }
      <div class="margin-left" *ngIf="displayBookMeetingButton && featureGroupsandServicesEnabled$ | async">
        <meeting-scheduler-schedule-meeting-button></meeting-scheduler-schedule-meeting-button>
      </div>
    </div>
  </glxy-page-toolbar>
  <router-outlet></router-outlet>
</glxy-page>

<ng-template #actionButtonTemplate let-action="action">
  @switch (action.buttonType) {
    @case ('mat-icon-button') {
      <button mat-stroked-button>
        <mat-icon class="settings-icon">{{ action.icon }}</mat-icon>
        <strong> {{ action.labelTranslationKey | translate }}</strong>
      </button>
    }
    @case ('mat-raised-button') {
      <button mat-raised-button color="primary">
        @if (action.icon) {
          <mat-icon>{{ action.icon }}</mat-icon>
        }
        {{ action.labelTranslationKey | translate }}
      </button>
    }
    @case ('mat-button') {
      <button mat-button color="primary">
        @if (action.icon) {
          <mat-icon>{{ action.icon }}</mat-icon>
        }
        {{ action.labelTranslationKey | translate }}
      </button>
    }
    @case ('mat-stroked-button') {
      <button mat-stroked-button>
        @if (action.icon) {
          <mat-icon>{{ action.icon }}</mat-icon>
        }
        {{ action.labelTranslationKey | translate }}
      </button>
    }
  }
</ng-template>
