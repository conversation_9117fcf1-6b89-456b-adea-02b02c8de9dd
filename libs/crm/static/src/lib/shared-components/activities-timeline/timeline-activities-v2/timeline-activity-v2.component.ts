import { Component, inject, input, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { RecordChangeType } from '../../../tokens-and-interfaces';
import { TranslationModule } from '../../../i18n/translation-module';
import { VALID_RECORD_CHANGE_TYPES } from '../../../constants';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { MatIconModule } from '@angular/material/icon';
import { ObserversModule } from '@angular/cdk/observers';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatInputModule } from '@angular/material/input';
import { ReactiveFormsModule } from '@angular/forms';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { CrmActivityCallComponent } from './activities/system-activities/call.component';
import { CrmActivityEmailComponent } from './activities/system-activities/email.component';
import { CrmActivityNoteComponent } from './activities/system-activities/note.component';
import { CrmActivityRecordChangeComponent } from './activities/system-activities/record-change.component';
import { CrmActivityOpportunityUpdatedComponent } from './activities/system-activities/opportunity-change.component';
import { CrmCommunicationComponent } from './activities/system-activities/communication.component';
import { ActivityAssociationService } from '../activities/activity-association.service';
import { ActivityInterface } from '@vendasta/crm';
import { ActivityTypes, CrmObjectIdentifier } from '../activities';
import { CrmIsTaskOverduePipe } from '../../../shared-pipes';
import { ActivitiesTimelineService, ActivityExtended } from '../timeline/timeline.service';
import { CrmFieldService, StandardExternalIds, StandardIds } from '../../../shared-services';
import { CrmActivityMeetingComponent } from './activities/system-activities/meeting.component';
import { CrmActivityLoggedNoteV2Component } from './activities/logged-note-v2.component';
import { CrmActivityLoggedEmailV2Component } from './activities/logged-email-v2.component';
import { CrmActivityLoggedCallV2Component } from './activities/logged-call-v2.component';
import { CrmActivityLoggedMeetingV2Component } from './activities/logged-meeting-v2.component';
import { CrmActivityLoggedCommunicationV2Component } from './activities/logged-communication-v2.component';
import { CrmActivityTaskV2Component } from './activities/task-v2.component';

@Component({
  selector: 'crm-timeline-activity-v2',
  templateUrl: './timeline-activity-v2.component.html',
  styleUrls: ['./timeline-activity-v2.component.scss'],
  providers: [ActivityAssociationService],
  imports: [
    CommonModule,
    TranslateModule,
    TranslationModule,
    GalaxyEmptyStateModule,
    GalaxyLoadingSpinnerModule,
    MatIconModule,
    ObserversModule,
    CrmActivityRecordChangeComponent,
    CrmActivityOpportunityUpdatedComponent,
    GalaxyFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    CrmIsTaskOverduePipe,
    MatMenuModule,
    MatButtonModule,
    CrmCommunicationComponent,
    CrmActivityCallComponent,
    CrmActivityOpportunityUpdatedComponent,
    CrmActivityMeetingComponent,
    CrmActivityEmailComponent,
    CrmActivityNoteComponent,
    CrmActivityLoggedNoteV2Component,
    CrmActivityLoggedEmailV2Component,
    CrmActivityLoggedCallV2Component,
    CrmActivityLoggedMeetingV2Component,
    CrmActivityLoggedCommunicationV2Component,
    CrmActivityTaskV2Component,
  ],
})
export class CrmTimelineActivityV2Component {
  @Input({ required: true }) activity!: ActivityExtended;
  crmObjectIdentifier = input.required<CrmObjectIdentifier>();
  @Input() isLast = false;
  showCopyLink = input(false);

  private readonly activitiesTimelineService = inject(ActivitiesTimelineService);
  private readonly fieldService = inject(CrmFieldService);

  uniqueUsers$ = this.activitiesTimelineService.state.select('uniqueUsers');

  isActivityValid() {
    const activity = this.activity;
    if (activity === null || activity === undefined) {
      return false;
    }

    switch (activity.crmObjectSubtype) {
      case this.ActivityType.Email:
        return true;
      case this.ActivityType.Note:
        return (
          this.fieldService.getFieldValueFromCrmObject(activity, StandardIds.ActivityNoteTitle)?.stringValue ||
          this.fieldService.getFieldValueFromCrmObject(activity, StandardIds.ActivityNoteBody)?.stringValue
        );
      case this.ActivityType.Meeting:
        return true;
      case this.ActivityType.Call:
        return true;
      case this.ActivityType.RecordChange:
        return VALID_RECORD_CHANGE_TYPES.includes(
          this.fieldService.getFieldValueFromCrmObject(activity, StandardExternalIds.ActivityRecordChangeType)
            ?.stringValue as RecordChangeType,
        );
      case this.ActivityType.Task:
        return true;
      case this.ActivityType.Communication:
        return true;
      case this.ActivityType.OpportunityChange:
        return true;
      default:
        console.error(`Unexpected activity type: ${activity.crmObjectSubtype}`);
        return false;
    }
  }

  updateActivity(activity: ActivityInterface): void {
    this.activitiesTimelineService.updateActivity(activity);
  }

  protected readonly ActivityType = ActivityTypes;
}
