@use 'design-tokens' as dt;

.task-container {
  display: flex;
  .task-content {
    margin-top: dt.$spacing-2 + dt.$spacing-1 / 2;
  }
}

.activity-data-label {
  display: block;
  @include dt.text-preset-5;
  color: dt.$secondary-font-color;
}

.activity-data-value {
  display: block;
  margin-bottom: dt.$spacing-3;

  &:empty:before {
    content: '—';
    color: dt.$tertiary-font-color;
  }
}

.card-content {
  overflow-wrap: anywhere;
}

.open-task {
  color: dt.$icon-color;
}

.completed-task {
  color: dt.$success-icon-color;
}

.overdue-task {
  color: dt.$error-icon-color;
}

.task-details {
  margin-left: dt.$negative-2;
}
