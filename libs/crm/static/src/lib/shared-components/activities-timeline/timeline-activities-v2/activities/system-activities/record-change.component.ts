import { CommonModule } from '@angular/common';
import { Component, computed, effect, Inject, model, signal } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ActivityInterface, CRMChangeLogApiService, CrmObjectChangeLogInterface } from '@vendasta/crm';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';

import { SalespersonService, SalesSdk } from '@vendasta/sales';
import { Salesperson } from '@vendasta/sales/lib/_internal/objects';
import { combineLatest, EMPTY, of, switchMap, take } from 'rxjs';
import { catchError } from 'rxjs/operators';
import {
  CrmDependencies,
  CrmInjectionToken,
  ObjectType,
  RecordChangeType,
  SimplifiedUser,
} from '../../../../../tokens-and-interfaces';
import { TranslateForCrmObjectService } from '../../../../../i18n/translate-for-crm-object/translate-for-crm-object.service';
import { TranslationModule } from '../../../../../i18n/translation-module';

import { CrmFieldService, StandardExternalIds, StandardIds } from '../../../../../shared-services';
import { ActivityCardComponent } from './activity-card/activity-card.component';
import { ActivityAssociationService, ActivityRecordChangeIcons } from '../../../activities';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'crm-activity-record-change',
  templateUrl: './record-change.component.html',
  styleUrls: ['./record-change.component.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    TranslationModule,
    MatCardModule,
    GalaxyPipesModule,
    GalaxyAvatarModule,
    ActivityCardComponent,
    RouterLink,
  ],
})
export class CrmActivityRecordChangeComponent {
  activity = model<ActivityInterface>({});
  showPrimaryAssociation = model<boolean>(false);

  private readonly namespace = toSignal(this.config.namespace$);

  objectType = computed(() => {
    const activity = this.activity();
    if (activity.associations === undefined || activity.associations.length === 0) {
      console.warn('No associations found for activity. Could not deduce object type.', this.activity().crmObjectId);
      return undefined;
    }
    return activity.associations[0].crmObjectType as ObjectType;
  });
  objectType$ = toObservable(this.objectType);

  private readonly salespersonNotAssignedTranslation = toSignal(
    this.objectType$.pipe(
      switchMap((objectType) => {
        return this.translateForCrmObjectService.getTranslationForCrmObject(
          objectType || 'Contact',
          'RECORD_CHANGE_ACTIVITY.IMPORTANT_FIELD_UPDATED_SALESPERSON_NOT_ASSIGNED',
        );
      }),
    ),
  );
  private readonly detailsUpdatedTranslation = toSignal(
    this.objectType$.pipe(
      switchMap((objectType) => {
        return this.translateForCrmObjectService.getTranslationForCrmObject(
          objectType || 'Contact',
          'RECORD_CHANGE_ACTIVITY.DETAILS_UPDATED',
        );
      }),
    ),
  );

  recordChangeType = computed(() => {
    return this.getStringFieldData(this.activity(), StandardExternalIds.ActivityRecordChangeType) as RecordChangeType;
  });

  isReady = signal<boolean>(false);
  isImportantFieldUpdated = computed(() => this.recordChangeType() === 'IMPORTANT_FIELD_UPDATED');

  icon = computed(() => {
    const objectType = this.objectType() as string;
    if (!objectType) return '';
    return ActivityRecordChangeIcons[objectType];
  });

  sourceName = computed(() => {
    const activity = this.activity();
    return activity ? this.getStringFieldData(activity, StandardIds.ActivitySourceName) : '';
  });

  changeLogUserName = '';

  primaryAssociation = this.associationService.primaryAssociation;
  routePrefix = this.associationService.routePrefix;

  cardInfo = computed<ActivityCardInformation>(() => {
    return {
      headerText: this.headerText(),
      contentText: this.contentText(),
      isReady: this.isReady(),
      isImportantFieldUpdated: this.isImportantFieldUpdated(),
    };
  });

  headerText = computed(() => {
    const effectResult = this.effectResult();
    const recordChangeType = this.recordChangeType();
    const computedObjectType = this.objectType();
    const sourceName = this.sourceName();

    if (!computedObjectType) {
      return '';
    }

    if (recordChangeType === 'CREATED') {
      const translationKey = sourceName ? 'ACTIVITY.RECORD_CHANGE.CREATED_VIA' : 'ACTIVITY.RECORD_CHANGE.CREATED';
      let translation = this.translate.instant(translationKey);
      if (this.showPrimaryAssociation()) {
        translation = translation.toLowerCase();
      }

      return sourceName ? `${translation} ${sourceName}` : translation;
    }

    if (this.isImportantFieldUpdated()) {
      this.changeLogUserName = effectResult?.changeLogUser?.[0]?.displayName ?? '';
      return this.detailsUpdatedTranslation();
    }

    return '';
  });

  effectResult = signal<EffectResult | undefined>(undefined);

  contentText = computed(() => {
    const effectResult = this.effectResult();
    const computedObjectType = this.objectType();

    if (!computedObjectType) return '';

    if (this.isImportantFieldUpdated()) {
      const salespersonKey = this.translate.instant('ACTIVITY.RECORD_CHANGE.SALESPERSON');

      if (effectResult?.changeLogSalesPerson === null && effectResult?.changeLogSalespersonId) {
        const unknownKey = 'ACTIVITY.RECORD_CHANGE.IMPORTANT_FIELD_UPDATED_SALESPERSON_UNKNOWN';
        const salespersonUnknown = this.translate.instant(unknownKey);
        return `${salespersonKey} ${salespersonUnknown}`.trim();
      }

      if (effectResult?.changeLogSalesPerson) {
        const salespersonFullName = [
          effectResult.changeLogSalesPerson.firstName ?? '',
          effectResult.changeLogSalesPerson.lastName ?? '',
        ]
          .map((name) => name.trim())
          .join(' ');

        return `${salespersonKey} ${salespersonFullName}`.trim();
      }
      return `${salespersonKey} ${this.salespersonNotAssignedTranslation()}`.trim();
    }

    return '';
  });

  constructor(
    private readonly fieldService: CrmFieldService,
    private readonly translateForCrmObjectService: TranslateForCrmObjectService,
    private readonly changeLogService: CRMChangeLogApiService,
    private readonly associationService: ActivityAssociationService,
    private readonly translate: TranslateService,
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    @Inject(SalespersonService) private readonly salespersonService: SalesSdk,
  ) {
    effect(() => {
      this.associationService.activity = this.activity();
      if (!this.recordChangeType() || !this.isImportantFieldUpdated()) {
        this.effectResult.set(undefined);
        this.isReady.set(true);
      } else if (this.isImportantFieldUpdated()) {
        const companyId =
          this.activity().associations?.find((association) => association.crmObjectType === 'Company')?.crmObjectId ??
          '';
        const changeId = this.getStringFieldData(this.activity(), StandardIds.RecordChangeReferenceFieldID);

        this.changeLogService
          .getMultiChangeLog({
            crmObjectType: this.objectType(),
            namespace: this.namespace(),
            crmChangelogIdentifiers: [
              {
                crmObjectId: companyId,
                crmChangeId: changeId,
              },
            ],
          })
          .pipe(
            switchMap((res) => {
              const object = res.changeLogs[0];
              const salesPersonSubId = this.getStringFieldData(object, StandardIds.CompanyPrimarySalespersonID);
              const createdByUserId = object.createdByUserId;
              const namespace = this.namespace() || '';

              return combineLatest([
                of(object),
                salesPersonSubId
                  ? this.salespersonService
                      .getSalespersonBySubjectId(namespace, salesPersonSubId)
                      .pipe(catchError(() => of(null)))
                  : of(null),
                createdByUserId ? this.config.services?.userService?.getMultiUsers([createdByUserId]) || EMPTY : EMPTY,
                of(salesPersonSubId),
              ]);
            }),
            take(1),
          )
          .subscribe(([object, salesPerson, simplifiedUser, salesPersonSubId]) => {
            const effectResult = {} as EffectResult;
            effectResult.changeLog = object;
            effectResult.changeLogSalesPerson = salesPerson;
            effectResult.changeLogUser = simplifiedUser;
            effectResult.changeLogSalespersonId = salesPersonSubId || null;
            this.effectResult.set(effectResult);
            this.isReady.set(true);
          });
      }
    });
  }

  private getStringFieldData(activity: ActivityInterface, fieldId: string): string {
    return this.fieldService.getFieldValueFromCrmObject(activity, fieldId)?.stringValue ?? '';
  }
}

interface ActivityCardInformation {
  headerText: string;
  contentText: string;
  isReady: boolean;
  isImportantFieldUpdated: boolean;
}

interface EffectResult {
  changeLog: CrmObjectChangeLogInterface;
  changeLogSalesPerson: Salesperson | null;
  changeLogUser: SimplifiedUser[] | null;
  changeLogSalespersonId: string | null;
}
