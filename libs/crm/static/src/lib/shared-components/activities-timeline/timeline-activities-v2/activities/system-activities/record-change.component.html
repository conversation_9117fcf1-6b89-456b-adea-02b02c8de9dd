@if (cardInfo(); as cardInfo) {
  <crm-activity-card [activity]="activity()" [showAssociationPopover]="false" [icon]="icon()">
    <span activity-title>
      @if (headerText(); as headerText) {
        @if (cardInfo.isImportantFieldUpdated) {
          <strong>{{ headerText }}</strong>
          {{ 'ACTIVITY.RECORD_CHANGE.BY' | translate }}
          {{ changeLogUserName }}
          @if (showPrimaryAssociation()) {
            {{ 'OBJECT_TYPES.FOR_LABEL' | translate }}
            <ng-container *ngTemplateOutlet="primaryAssociationLink"></ng-container>
          }
        } @else {
          @if (showPrimaryAssociation()) {
            <ng-container *ngTemplateOutlet="primaryAssociationLink"></ng-container>
          }
          {{ headerText }}
        }
      } @else {
        {{ 'ACTIVITY.UNKNOWN_TITLE' | translate }}
      }
    </span>

    <div activity-preview>
      @if (cardInfo.contentText; as contentText) {
        {{ contentText }}
      }
    </div>
  </crm-activity-card>
}

<ng-template #primaryAssociationLink>
  @if (primaryAssociation(); as primaryAssociation) {
    <a [routerLink]="routePrefix() + '/' + primaryAssociation.linkUrl">{{ primaryAssociation.name }}</a>
  }
</ng-template>
