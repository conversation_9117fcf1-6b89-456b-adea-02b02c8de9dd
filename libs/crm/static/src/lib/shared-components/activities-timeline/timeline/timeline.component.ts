import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Inject, input, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { ObserversModule } from '@angular/cdk/observers';
import { TranslateModule } from '@ngx-translate/core';
import { CrmDependencies, CrmInjectionToken, ObjectAssociationType, ObjectType } from '../../../tokens-and-interfaces';
import { TranslationModule } from '../../../i18n/translation-module';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { MatIconModule } from '@angular/material/icon';
import { ActivityInterface } from '@vendasta/crm';
import { RxState } from '@rx-angular/state';
import { combineLatest, distinctUntilChanged, filter, map, Observable } from 'rxjs';
import { ActivitiesTimelineService, ActivityExtended, ActivitySectionInterface } from './timeline.service';
import { CrmFieldService, StandardIds } from '../../../shared-services/crm-services/field.service';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatInputModule } from '@angular/material/input';
import { ReactiveFormsModule } from '@angular/forms';
import { GalaxyFilterInterface } from '@vendasta/galaxy/filter/chips';
import { SubscriptionList } from '@vendasta/rx-utils';
import { DateFormat } from '@vendasta/galaxy/utility/date-utils';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';

import { CrmObjectIdentifier } from '../activities';
import { CrmTimelineActivityComponent } from './timeline-activity.component';
import { CRMTrackingService } from '../../../shared-services';
import { CrmTimelineActivityV2Component } from '../timeline-activities-v2/timeline-activity-v2.component';

@Component({
  selector: 'crm-timeline',
  templateUrl: './timeline.component.html',
  styleUrls: ['./timeline.component.scss'],
  providers: [RxState],
  imports: [
    CommonModule,
    TranslateModule,
    TranslationModule,
    GalaxyEmptyStateModule,
    GalaxyLoadingSpinnerModule,
    MatIconModule,
    ObserversModule,
    GalaxyFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    MatMenuModule,
    MatButtonModule,
    CrmTimelineActivityComponent,
    CrmTimelineActivityV2Component,
  ],
})
export class CrmTimelineComponent implements OnDestroy, OnInit {
  showCopyLink = input(false);

  @Input()
  set crmObjectId(crmObjectId: string) {
    this.activitiesTimelineService.crmObjectId$$.next(crmObjectId);
  }

  @Input()
  set objectType(objectType: ObjectType | ObjectAssociationType) {
    this.activitiesTimelineService.objectType$$.next(objectType);
  }

  @Output() activitiesChanged = new EventEmitter<ActivityInterface[]>();

  readonly dateFormat = DateFormat.medium;
  readonly now = new Date();

  crmIdentifiers$: Observable<CrmObjectIdentifier> = combineLatest([
    this.activitiesTimelineService.crmObjectId$$.asObservable(),
    this.activitiesTimelineService.objectType$$.asObservable(),
  ]).pipe(map(([crmObjectId, objectType]) => ({ crmObjectId, objectType }) as CrmObjectIdentifier));
  activitySections$ = this.activitiesTimelineService.state.select('activitySections');
  readonly filters$ = this.activitiesTimelineService.state.select('filters');

  loading$ = combineLatest([
    this.activitiesTimelineService.state.select('activitiesLoading'),
    this.activitiesTimelineService.state.select('activitiesLoadMore'),
  ]).pipe(map(([activitiesLoading, activitiesLoadMore]) => activitiesLoading || activitiesLoadMore));
  filtersPristine$ = combineLatest([
    this.activitiesTimelineService.state.select('searchTerm'),
    this.activitiesTimelineService.state.select('filters'),
  ]).pipe(
    map(([searchTerm, filters]) => {
      if (searchTerm.length !== 0) {
        return false;
      }
      if (filters.length !== 0) {
        return false;
      }
      return true;
    }),
  );
  showLoadingAfterActivities$ = combineLatest([
    this.loading$,
    this.activitiesTimelineService.state.select('activitiesPagedResponseMetadata'),
  ]).pipe(map(([loading, paged]) => loading && paged));
  showLoadingReplacingActivities$ = combineLatest([
    this.loading$,
    this.activitiesTimelineService.state.select('activitiesPagedResponseMetadata'),
  ]).pipe(map(([loading, paged]) => loading && !paged));

  subscriptions = SubscriptionList.new();

  hasCrmTimelineActivitiesFeatureFlag$ = this.config.hasTimelineActivitiesFeatureFlag$;

  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    private readonly activitiesTimelineService: ActivitiesTimelineService,
    private readonly fieldService: CrmFieldService,
    private readonly trackingService: CRMTrackingService,
  ) {}

  ngOnInit() {
    this.setupSplitActivitiesIntoSections(!!this.activitiesTimelineService.crmObjectId$$.getValue());
    this.activitiesTimelineService.state.connect('activitySections', this.activitySections$);
    this.activitiesTimelineService.state
      .select('activitiesChanged')
      .pipe(distinctUntilChanged(), filter(Boolean))
      .subscribe(() => {
        this.activitiesTimelineService.state.set({ activitiesChanged: false });
        this.activitiesChanged.emit(this.activitiesTimelineService.state.get('activities'));
      });
  }

  ngOnDestroy() {
    this.subscriptions.destroy();
  }

  setFilters(filters: GalaxyFilterInterface[]) {
    this.activitiesTimelineService.state.set({ filters: filters });
    this.activitiesTimelineService.reloadActivities();
    this.trackingService.trackFilterUsage(this.objectType, filters);
  }

  setSearchTerm(searchTerm: string) {
    // set searchTerm imperatively since we want it available with the next reload
    this.activitiesTimelineService.state.set({ searchTerm: searchTerm });
    this.activitiesTimelineService.reloadActivities();
  }

  setupSplitActivitiesIntoSections(prioritizeOverdueTask: boolean): void {
    this.activitySections$ = combineLatest([
      this.activitiesTimelineService.state.select('activities'),
      this.activitiesTimelineService.state.select('uniqueUsers'),
    ]).pipe(
      map(([activities, uniqueUsers]) => {
        return activities.map((activity) => {
          return {
            ...activity,
            ownerUser: uniqueUsers.find((user) => user.userId === activity.ownerId),
            isLoggedActivity:
              this.fieldService.getFieldValueFromCrmObject(activity, StandardIds.ActivityManuallyLogged)
                ?.booleanValue ?? false,
          } as ActivityExtended;
        });
      }),
      map((activitiesWithOwnerUser) =>
        this.activitiesTimelineService.getActivitySections(activitiesWithOwnerUser, prioritizeOverdueTask),
      ),
    );
  }

  addActivity(activity: ActivityInterface): void {
    this.activitiesTimelineService.state.set({
      activities: [activity, ...this.activitiesTimelineService.state.get('activities')],
    });
  }

  loadMoreActivities() {
    this.activitiesTimelineService.loadMoreActivities();
  }

  trackActivitySectionsBy(index: number, section: ActivitySectionInterface) {
    return `${index}-${section.name}`;
  }

  trackActivitiesBy(index: number, activity: ActivityInterface) {
    return `${index}-${activity.crmObjectId}`;
  }
}
