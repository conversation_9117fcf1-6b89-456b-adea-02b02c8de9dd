import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  EventEmitter,
  input,
  Input,
  Output,
  signal,
  WritableSignal,
  inject,
  OnInit,
  DestroyRef,
} from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { Router, RouterModule } from '@angular/router';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { combineLatest, Observable, of, filter, map } from 'rxjs';
import {
  ActivityType,
  CrmDependencies,
  CrmInjectionToken,
  CrmObjectDependencies,
  CrmObjectInjectionToken,
  CRMRowObject,
  ListObjectView,
  MultiRowAction,
  ObjectType,
  SingleRowAction,
} from '../../tokens-and-interfaces';
import { TranslationModule } from '../../i18n/translation-module';
import { ListObjectsTableComponent } from './list-objects-table.component';
import { CustomRowStyle } from '../../shared-services/table-customization/_abstract-table-customization.service';
import { PresetFilters } from '../filter-view';
import { Row } from '@vendasta/galaxy/table';
import { toObservable, takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BreakpointObserver } from '@angular/cdk/layout';
import { TranslateForCrmObjectPipe } from '../../i18n';
import { FeatureFlagsApiService } from '@vendasta/partner';
import { switchMap } from 'rxjs/operators';
import { ViewTabsComponent } from '@galaxy/crm/components/view-tabs';

@Component({
  selector: 'crm-list-objects-page',
  templateUrl: './list-objects-page.component.html',
  imports: [
    CommonModule,
    MatMenuModule,
    GalaxyPageModule,
    GalaxyTooltipModule,
    ListObjectsTableComponent,
    MatButtonModule,
    MatIconModule,
    RouterModule,
    TranslationModule,
    TranslateForCrmObjectPipe,
    ViewTabsComponent,
  ],
  styleUrls: ['./list-objects-page.component.scss'],
})
export class ListObjectsPageComponent implements OnInit {
  private readonly config: CrmDependencies = inject(CrmInjectionToken);
  private readonly router = inject(Router);
  private readonly breakpointObserver = inject(BreakpointObserver);
  private readonly crmObjectDependencies: CrmObjectDependencies | null = inject(CrmObjectInjectionToken, {
    optional: true,
  });
  private readonly destroyRef = inject(DestroyRef);

  @Input({ required: true }) objectType!: ObjectType;
  @Input() activityType: ActivityType | undefined;
  @Input() customRowStyle: CustomRowStyle | undefined;
  @Input() multiActions: MultiRowAction[] = [];
  @Input() singleActions: SingleRowAction[] = [];
  @Input() createObjectRoute = '';
  @Input() accountPageRoute = '';
  @Input() presetFilters?: PresetFilters;
  @Input() refreshRow$?: Observable<string>;
  importCSVRoute = input<string>();
  private readonly importCSVRoute$ = toObservable(this.importCSVRoute);
  readonly canCreateObject = computed(() => {
    if (this.currentView() === 'table' || this.objectType !== 'Opportunity') {
      return true;
    }
    return this.isPipelineSelected();
  });
  readonly currentView: WritableSignal<ListObjectView> = signal('unknown');
  readonly isPipelineSelected = signal(false);

  @Output() editClick: EventEmitter<CRMRowObject> = new EventEmitter();
  @Output() selectionChange: EventEmitter<Row[]> = new EventEmitter();

  private readonly featureFlagService = inject(FeatureFlagsApiService);
  protected readonly hasNewTabFF$ = this.config.parentNamespace$.pipe(
    switchMap((partnerId) => {
      return this.featureFlagService.batchGetStatus({
        featureIds: ['crm_table_view_as_tabs'],
        partnerId: partnerId,
      });
    }),
  );

  protected readonly mobileView$ = this.breakpointObserver
    .observe('(max-width: 768px)')
    .pipe(map((breakpoints) => breakpoints.matches));

  protected get typeForTitle(): ObjectType | ActivityType {
    return this.activityType ?? this.objectType;
  }

  protected readonly createObjectRoute$: Observable<string> = this.config.routePrefix$.pipe(
    filter(() => !!this.createObjectRoute),
    map((crm) => {
      if (this.crmObjectDependencies) {
        return this.createObjectRoute;
      }
      return `/${crm}/${this.createObjectRoute}`;
    }),
  );

  protected readonly importCSVRouteWithPrefix$: Observable<string> = combineLatest([
    this.config.routePrefix$,
    this.importCSVRoute$,
  ]).pipe(
    filter(([_, importCSVRoute]) => !!importCSVRoute),
    map(([crm, route]) => `${crm}/${route}`),
  );
  objectSubtype$ = this.crmObjectDependencies?.objectSubtype$ ?? of('');
  subtypeChange = true;

  ngOnInit(): void {
    this.objectSubtype$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((objectSubtype) => {
      if (!objectSubtype) {
        return;
      }
      this.subtypeChange = false;
      setTimeout(() => {
        this.subtypeChange = true;
      });
    });
  }

  navigateToCsvImportRoute(route: string): void {
    this.router.navigate([route], {
      state: { previousPageObjectType: this.objectType },
    });
  }

  setPipelineSelected(isPipelineSelected: boolean): void {
    this.isPipelineSelected.set(isPipelineSelected);
  }

  storeCurrentView(view: ListObjectView): void {
    this.currentView.set(view);
  }
}
