@use 'design-tokens' as *;
@import 'utilities';

// pane class is added outside of angular component scope
// as part of global styles
.cdk-overlay-pane.crm-phone-country-selector-overlay-pane {
  @include phone {
    width: 85% !important;
  }

  @include tablet {
    width: 300px !important;
  }

  @include desktop {
    width: 300px !important;
  }

  @include desktop-large {
    width: 300px !important;
  }

  .crm-phone-country-option .mdc-list-item__primary-text {
    display: inline-flex;

    .crm-phone-country-option-calling-code {
      flex: 1 0 auto;
      width: 42px;
    }
    .crm-phone-country-option-display {
      flex: 1 1 auto;
    }
  }
}

.crm-phone-form {
  margin-bottom: $spacing-4;

  .glxy-label {
    font-size: $font-preset-4-size;
    font-weight: 500;
    display: block;
    margin-bottom: $spacing-2;
  }

  .crm-phone-code-prefix {
    padding-left: $spacing-2;
    padding-right: $spacing-2;

    @media (max-width: $mobile-breakpoint-max) {
      padding-right: $spacing-1;
    }
  }

  .crm-phone,
  .crm-phone-country {
    margin-bottom: 0;
    @media (max-width: $mobile-breakpoint-max) {
      margin-bottom: $spacing-4;
    }
  }

  .crm-phone-extension {
    margin-bottom: 0;
  }

  .glxy-form-row > .crm-phone input {
    padding-left: $spacing-1;
  }

  .crm-phone-form-error {
    margin-top: 0;
  }
}
