import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, Inject, ViewEncapsulation } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { RendererSelectorComponent } from '../../renderer-selector.component';
import { BaseCrmModelDrivenFormComponent } from './base-model-driven-form.component';
import { CrmFormLoadingIndicatorComponent } from './form-loading-indicator.component';

import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import {
  CrmDependencies,
  CrmInjectionToken,
  CrmObjectInjectionToken,
  ObjectType,
} from '../../../../tokens-and-interfaces';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { RendererSelectorService } from '../../renderer-selector.service';
import { ValidatorFactoryService } from '../../validator-factory.service';
import { InlineEditService } from '../../inline-edit.service';
import { ScoreService, SystemFieldIds } from '../../../../shared-services';
import {
  CrmFieldService,
  StandardExternalIds,
  StandardIds,
} from '../../../../shared-services/crm-services/field.service';
import { TranslateByObjectTypeService, TranslateForCrmObjectPipe } from '../../../../i18n';
import { combineLatest, map, Observable, of, switchMap } from 'rxjs';

@Component({
  selector: 'crm-basic-field-group-form',
  templateUrl: './basic-field-group-form.component.html',
  styleUrls: ['./base-model-driven-form.component.scss'],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    GalaxyEmptyStateModule,
    MatIconModule,
    RendererSelectorComponent,
    MatCardModule,
    CrmFormLoadingIndicatorComponent,
    MatButtonModule,
    MatDialogModule,
    TranslateForCrmObjectPipe,
  ],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CrmBasicFieldGroupFormComponent extends BaseCrmModelDrivenFormComponent {
  basicGroupFieldsMap$: Observable<Map<ObjectType, string[]>>;
  private readonly crmObjectDependencies = inject(CrmObjectInjectionToken, { optional: true });
  protected readonly objectSubtype$: Observable<string> = this.crmObjectDependencies?.objectSubtype$ ?? of('');

  constructor(
    @Inject(CrmInjectionToken) protected override readonly config: CrmDependencies,
    protected override snackService: SnackbarService,
    protected override rendererSelector: RendererSelectorService,
    protected override readonly validatorFactory: ValidatorFactoryService,
    protected override readonly inlineEditService: InlineEditService,
    protected readonly fieldService: CrmFieldService,
    protected override readonly scoreService: ScoreService,
    protected override readonly translateByObjectTypeService: TranslateByObjectTypeService,
  ) {
    super(
      config,
      snackService,
      rendererSelector,
      validatorFactory,
      inlineEditService,
      fieldService,
      scoreService,
      translateByObjectTypeService,
    );

    const customObjectFields$ = this.objectSubtype$.pipe(
      switchMap((subtype) => {
        if (!subtype) {
          return of([]);
        }
        return of(this.fieldConfig?.fieldSchemas?.map((f) => f.fieldId) ?? []);
      }),
    );
    this.basicGroupFieldsMap$ = combineLatest([
      this.config.contact?.additionalBaseFormFieldIds$ || of([]),
      this.config.company?.additionalBaseFormFieldIds$ || of([]),
      this.config.opportunity?.additionalBaseFormFieldIds$ || of([]),
      customObjectFields$,
    ]).pipe(
      map(([contactFields, companyFields, opportunityFields, customObjectFields]) => {
        const basicGroupFieldsMap = new Map<ObjectType, string[]>();
        basicGroupFieldsMap.set('Contact', [
          fieldService.getFieldId(StandardExternalIds.FirstName),
          fieldService.getFieldId(StandardExternalIds.LastName),
          fieldService.getFieldId(StandardExternalIds.PhoneNumber),
          fieldService.getFieldId(StandardExternalIds.Email),
          fieldService.getFieldId(StandardExternalIds.Tags),
          StandardIds.ContactLinkedInURL,
          StandardIds.ContactJobTitle,
          ...contactFields,
        ]);
        basicGroupFieldsMap.set('Company', [
          StandardIds.CompanyName,
          StandardIds.CompanyWebsite,
          StandardIds.CompanyPrimaryAddressLine1,
          StandardIds.CompanyPrimaryAddressLine2,
          StandardIds.CompanyPrimaryAddressCity,
          StandardIds.CompanyPrimaryAddressState,
          StandardIds.CompanyPrimaryAddressPostalCode,
          StandardIds.CompanyPrimaryAddressCountry,
          StandardIds.CompanyPhoneNumber,
          StandardIds.CompanyTags,
          StandardIds.CompanyPrimaryAddressGooglePlaceID,
          ...companyFields,
        ]);
        basicGroupFieldsMap.set('Opportunity', [
          StandardIds.OpportunityName,
          SystemFieldIds.OpportunityOwnerID,
          StandardIds.OpportunityPipelineID,
          StandardIds.OpportunityProbability,
          StandardIds.OpportunityAmount,
          StandardIds.OpportunityExpectedCloseDate,
          ...opportunityFields,
        ]);
        basicGroupFieldsMap.set('CustomObject', [...customObjectFields]);
        return basicGroupFieldsMap;
      }),
    );
  }
}
