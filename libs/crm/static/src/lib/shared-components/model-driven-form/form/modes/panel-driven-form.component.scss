@use 'design-tokens' as *;

$panel-field-bottom-spacing: $spacing-3;

.crm-panel-form {
  .glxy-form-field .mat-mdc-select,
  .glxy-form-field .mat-datepicker-input,
  .glxy-form-field input:not([type='checkbox']):not([type='radio']):not([type='color']) {
    height: 30px;
  }

  .glxy-form-field mat-datepicker-toggle .mat-mdc-icon-button {
    width: 30px;
    height: 30px;
    padding-right: calc($spacing-2 + 2px);
  }

  .glxy-form-field .input-wrapper:not(:hover, :focus-within) {
    // needed to avoid the border from jumping when the input is focused
    border: 1px transparent solid;
    box-shadow: none;
    .mat-icon,
    .mat-datepicker-toggle {
      display: none;
    }
  }

  .crm-datetime:not(:hover, :focus-within) {
    input[type='datetime-local']::-webkit-calendar-picker-indicator {
      opacity: 0;
    }
  }

  .glxy-form-field.glxy-form-field-invalid .input-wrapper:not(:hover, :focus-within) {
    // needed to avoid the border from jumping when the input is focused
    border: 1px $error-border-color solid;
    box-shadow: 0 0 0 1px $error-border-color;
  }

  .glxy-form-field .label-and-hint label {
    margin-left: calc($spacing-2 + 4px);
    margin-bottom: $negative-2;
  }

  .glxy-form-field .label-and-hint label .glxy-label {
    position: relative;
    background: $card-background-color;
    padding: 0 calc($spacing-1 - 2px);
  }

  .glxy-label,
  .crm-phone-form .glxy-label {
    @include text-preset-5;
    color: $secondary-text-color;
  }

  .glxy-form-field.bottom-spacing--default {
    margin-bottom: $panel-field-bottom-spacing;

    @media (max-width: $mobile-breakpoint-max) {
      margin-bottom: $panel-field-bottom-spacing;
    }
  }

  .crm-phone-form {
    margin-bottom: $panel-field-bottom-spacing;

    .crm-phone,
    .crm-phone-country {
      margin-bottom: $panel-field-bottom-spacing;
    }

    .crm-phone-extension {
      margin-bottom: 0;
    }

    .crm-phone-country {
      width: 25%;
    }
    .crm-phone {
      padding-left: $spacing-2;
      width: 75%;
    }
    .crm-phone-extension {
      width: 100%;
    }
  }

  .divider {
    margin-bottom: $panel-field-bottom-spacing;
  }
}

.crm-panel-button-container {
  padding-top: $spacing-3;
}
