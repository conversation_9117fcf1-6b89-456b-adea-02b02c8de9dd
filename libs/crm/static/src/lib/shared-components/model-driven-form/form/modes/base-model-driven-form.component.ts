import { Component, EventEmitter, Inject, Input, OnChanges, OnDestroy, Output, SimpleChanges } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { RendererSelectorService } from '../../renderer-selector.service';
import { ValidatorFactoryService } from '../../validator-factory.service';
import { InlineEditService } from '../../inline-edit.service';
import { ScoreService } from '../../../../shared-services';
import { CrmFieldService } from '../../../../shared-services/crm-services/field.service';
import {
  CrmInjectionToken,
  CrmDependencies,
  FieldConfigInterface,
  FieldGroupInterface,
  FieldInterface,
  FieldSchemaInterface,
  LoadingStatus,
  ObjectType,
  RenderableFieldGroupInterface,
  RenderableFieldInterface,
  SavingStatus,
} from '../../../../tokens-and-interfaces';
import { TranslateByObjectTypeService } from '../../../../i18n';

const SNACKBAR_DURATION = 10000;

@Component({
  template: `
    <!-- base component, template should be implemented by inherited -->
    <ng-container></ng-container>
  `,
  standalone: true,
})
export class BaseCrmModelDrivenFormComponent implements OnChanges, OnDestroy {
  @Input({ required: true }) objectType!: ObjectType;
  @Input() existingObjectId = '';

  @Input() fieldConfig: FieldConfigInterface = {};

  @Input() submitButtonText = '';
  @Input() cancelButtonText = '';

  @Input() loadingStatus!: LoadingStatus;
  @Input() savingStatus!: SavingStatus;
  @Input() readOnly = false;
  @Input() inlineEdit = false;
  @Input() isEditing = false;

  @Output() formSubmit = new EventEmitter<FieldInterface[]>();
  @Output() formCancel = new EventEmitter();

  formGroup = new UntypedFormGroup({});

  readOnlyFields: RenderableFieldInterface[] = [];
  renderableFields: RenderableFieldInterface[] = [];
  renderableFieldGroups: RenderableFieldGroupInterface[] = [];
  readonlyRenderableFieldGroups: RenderableFieldGroupInterface[] = [];

  constructor(
    @Inject(CrmInjectionToken) protected readonly config: CrmDependencies,
    protected snackService: SnackbarService,
    protected rendererSelector: RendererSelectorService,
    protected readonly validatorFactory: ValidatorFactoryService,
    protected readonly inlineEditService: InlineEditService,
    protected readonly crmFieldService: CrmFieldService,
    protected readonly scoreService: ScoreService,
    protected readonly translateByObjectTypeService: TranslateByObjectTypeService,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['fieldConfig'] || changes['readOnly']) {
      this.initialize(this.fieldConfig?.fieldSchemas ?? [], this.fieldConfig?.fieldGroups ?? []);
    }
    if (changes['existingObjectId']) {
      this.inlineEditService.setObjectId(this.existingObjectId);
      this.scoreService.setObjectId(this.existingObjectId);
    }
    if (changes['inlineEdit']) {
      this.inlineEditService.setInlineEdit(this.inlineEdit);
    }
  }

  ngOnDestroy(): void {
    // have to free memory or else these components stay up in memory forever
    this.readOnlyFields = [];
    this.renderableFields = [];
    this.renderableFieldGroups = [];
    this.readonlyRenderableFieldGroups = [];
  }

  private setupValidation(fieldSchemas: FieldSchemaInterface[]): void {
    this.validatorFactory.initialize(
      this.objectType,
      this.existingObjectId,
      this.renderableFields,
      this.formGroup.valueChanges,
    );
    const fieldSchemaMap = new Map();
    for (const schema of fieldSchemas) {
      fieldSchemaMap.set(schema.fieldId, schema);
    }
    for (const field of this.renderableFields) {
      this.rendererSelector.setupFieldValidators(field, fieldSchemaMap);
    }
  }

  protected initialize(fieldSchemas: FieldSchemaInterface[], fieldGroups: FieldGroupInterface[]): void {
    if (fieldSchemas.length === 0 || fieldGroups.length === 0) {
      return;
    }

    // values are re-instantiated to avoid duplicating field value references internally
    this.renderableFieldGroups = [];
    this.renderableFields = [];
    this.readOnlyFields = [];
    fieldGroups.forEach((group) => {
      const renderableFieldGroup: RenderableFieldGroupInterface = {
        description: group.description,
        fields: [],
        editableFieldCount: 0,
      };

      const readOnlyRenderableFieldGroup: RenderableFieldGroupInterface = {
        description: group.description,
        fields: [],
        editableFieldCount: 0,
      };
      group.fieldIds.forEach((fieldId) => {
        const fieldSchema = fieldSchemas.find((fieldSchema) => fieldSchema.fieldId === fieldId);
        if (fieldSchema === undefined) {
          console.warn(`Couldn't find field with fieldId ${fieldId} in group with description ${group.description}`);
          return;
        }
        const renderableField = this.rendererSelector.chooseHandler(
          this.objectType,
          fieldSchema.fieldId,
          fieldSchema,
          fieldSchema.value,
          true,
          this.readOnly || fieldSchema.readOnly,
          this.formGroup,
          this.isEditing,
          this.existingObjectId,
        );
        if (renderableField !== null) {
          this.formGroup.setControl(renderableField.fieldId, renderableField.formControl);
          if (fieldSchema.hasDefaultValue) {
            renderableField.formControl.markAsDirty();
          }
          if (fieldSchema.readOnly) {
            this.readOnlyFields.push(renderableField);
            if (
              this.crmFieldService.shouldRenderReadonlyField(renderableField.fieldId) &&
              renderableField.value !== null
            ) {
              readOnlyRenderableFieldGroup.fields.push(renderableField);
            }
          } else {
            this.renderableFields.push(renderableField);
            renderableFieldGroup.fields.push(renderableField);
          }
        }
      });

      if (renderableFieldGroup.fields.length === 0) {
        console.warn(`Couldn't find any fields for group with description ${group.description}`);
        return;
      }
      if (readOnlyRenderableFieldGroup.fields.length > 0) {
        this.readonlyRenderableFieldGroups.push(readOnlyRenderableFieldGroup);
      }
      renderableFieldGroup.editableFieldCount = renderableFieldGroup.fields.reduce(
        (acc, field) => acc + (field.readOnly ? 0 : 1),
        0,
      );
      this.renderableFieldGroups.push(renderableFieldGroup);
    });
    this.setupValidation(fieldSchemas);
  }

  protected onSubmit(): void {
    if (this.formGroup.pristine) {
      this.snackService.openErrorSnack('MODEL_DRIVEN_FORM.SUBMITTED_PRISTINE', {
        duration: SNACKBAR_DURATION,
      });
      return;
    }
    if (this.formGroup.invalid) {
      this.formGroup.markAllAsTouched();
      const errorMessage = this.existingObjectId ? 'MODEL_DRIVEN_FORM.UPDATE_ERROR' : 'MODEL_DRIVEN_FORM.CREATE_ERROR';
      const translatedObjectType = this.translateByObjectTypeService.getTranslatedObjectType(this.objectType);
      this.snackService.openErrorSnack(errorMessage, {
        duration: SNACKBAR_DURATION,
        interpolateTranslateParams: { objectType: translatedObjectType.toLowerCase() },
      });
      return;
    }
    this.formSubmit.emit([...this.renderableFields, ...this.readOnlyFields]);
  }

  onCancel(): void {
    this.formCancel.emit();
  }
}
