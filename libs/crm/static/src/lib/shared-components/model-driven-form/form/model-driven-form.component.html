<ng-container [ngSwitch]="mode">
  <ng-container *ngSwitchCase="'panel'">
    <crm-panel-driven-form
      [objectType]="objectType"
      [existingObjectId]="existingObjectId"
      [submitButtonText]="submitButtonText | translate"
      [cancelButtonText]="cancelButtonText | translate"
      [loadingStatus]="loadingStatus"
      [savingStatus]="savingStatus"
      [fieldConfig]="fieldConfig"
      [readOnly]="readOnly"
      [inlineEdit]="true"
      [isEditing]="isEditing"
      (formSubmit)="onSubmit($event)"
      (formCancel)="onCancel()"
    ></crm-panel-driven-form>
  </ng-container>
  <ng-container *ngSwitchDefault>
    <crm-card-group-driven-form
      [objectType]="objectType"
      [existingObjectId]="existingObjectId"
      [submitButtonText]="submitButtonText | translate"
      [cancelButtonText]="cancelButtonText | translate"
      [loadingStatus]="loadingStatus"
      [savingStatus]="savingStatus"
      [fieldConfig]="fieldConfig"
      [readOnly]="readOnly"
      [isEditing]="isEditing"
      (formSubmit)="onSubmit($event)"
      (formCancel)="onCancel()"
      [(associationFormFields)]="associationFormFields"
    ></crm-card-group-driven-form>
  </ng-container>
  <ng-container *ngSwitchCase="'basic-field-group'">
    <crm-basic-field-group-form
      [objectType]="objectType"
      [existingObjectId]="existingObjectId"
      [submitButtonText]="submitButtonText | translate"
      [cancelButtonText]="cancelButtonText | translate"
      [loadingStatus]="loadingStatus"
      [savingStatus]="savingStatus"
      [fieldConfig]="fieldConfig"
      [isEditing]="isEditing"
      [readOnly]="readOnly"
      (formSubmit)="onSubmit($event)"
      (formCancel)="onCancel()"
    ></crm-basic-field-group-form>
  </ng-container>
</ng-container>
