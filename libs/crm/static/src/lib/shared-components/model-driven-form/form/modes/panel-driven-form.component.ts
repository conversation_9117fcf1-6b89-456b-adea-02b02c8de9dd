import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, ViewEncapsulation } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { RendererSelectorComponent } from '../../renderer-selector.component';
import { BaseCrmModelDrivenFormComponent } from './base-model-driven-form.component';
import { CrmFormButtonGroupComponent } from './form-button-group.component';
import { CrmFormLoadingIndicatorComponent } from './form-loading-indicator.component';
import { MatDividerModule } from '@angular/material/divider';

@Component({
  selector: 'crm-panel-driven-form',
  templateUrl: './panel-driven-form.component.html',
  styleUrls: ['./base-model-driven-form.component.scss', './panel-driven-form.component.scss'],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    RendererSelectorComponent,
    CrmFormLoadingIndicatorComponent,
    CrmFormButtonGroupComponent,
    MatDividerModule,
  ],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CrmPanelDrivenFormComponent extends BaseCrmModelDrivenFormComponent {}
