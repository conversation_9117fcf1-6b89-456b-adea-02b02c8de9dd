import { ChangeDetectionStrategy, Component, EventEmitter, Input, model, Output } from '@angular/core';
import { CrmCardGroupDrivenFormComponent } from './modes/card-group-driven-form.component';
import { CrmPanelDrivenFormComponent } from './modes/panel-driven-form.component';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { CrmBasicFieldGroupFormComponent } from './modes/basic-field-group-form.component';
import { ScoreService } from '../../../shared-services';
import { InlineEditService } from '../inline-edit.service';
import {
  AssociationFormField,
  FieldConfigInterface,
  FieldInterface,
  LoadingStatus,
  ObjectType,
  SavingStatus,
} from '../../../tokens-and-interfaces';
import { SyncFieldFormInputService } from '../renderers/accounts/sync-field-form-input.service';

type FormMode = 'panel' | 'card-group' | 'basic-field-group';

@Component({
  selector: 'crm-model-driven-form',
  templateUrl: './model-driven-form.component.html',
  styleUrls: ['./modes/base-model-driven-form.component.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    CrmCardGroupDrivenFormComponent,
    CrmPanelDrivenFormComponent,
    CrmBasicFieldGroupFormComponent,
  ],
  providers: [InlineEditService, ScoreService, SyncFieldFormInputService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CrmModelDrivenFormComponent {
  @Input({ required: true }) objectType!: ObjectType;
  @Input() existingObjectId = '';
  @Input() fieldConfig: FieldConfigInterface = {};
  @Input() isEditing = false;

  // these are translation keys
  @Input() submitButtonText = 'ACTIONS.SAVE';
  @Input() cancelButtonText = 'ACTIONS.CANCEL';

  @Input() loadingStatus!: LoadingStatus;
  @Input() savingStatus!: SavingStatus;
  @Input() readOnly = false;
  @Input() mode: FormMode = 'card-group';

  @Output() formSubmit = new EventEmitter<FieldInterface[]>();
  @Output() formCancel = new EventEmitter();

  protected readonly associationFormFields = model<AssociationFormField[]>([]);

  onSubmit(fields: FieldInterface[]): void {
    this.formSubmit.emit(fields);
  }

  onCancel(): void {
    this.formCancel.emit();
  }
}
