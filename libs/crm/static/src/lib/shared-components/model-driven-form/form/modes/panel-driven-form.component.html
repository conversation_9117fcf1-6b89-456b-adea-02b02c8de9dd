<crm-form-loading-indicator [loadingStatus]="loadingStatus">
  <form class="crm-model-driven-form crm-panel-form" form-content [formGroup]="formGroup">
    <!-- groups are ignored in panel model -->
    @for (renderableFieldGroup of renderableFieldGroups; track renderableFieldGroup) {
      @for (renderableField of renderableFieldGroup.fields; track renderableField) {
        <crm-renderer-selector [handler]="renderableField"></crm-renderer-selector>
      }
    }
    @for (renderableFieldGroup of readonlyRenderableFieldGroups; track renderableFieldGroup) {
      @for (renderableField of renderableFieldGroup.fields; track renderableField) {
        <crm-renderer-selector [handler]="renderableField"></crm-renderer-selector>
      }
    }
    @if (!readOnly && !inlineEdit) {
      <div class="crm-panel-button-container">
        <crm-form-button-group
          [submitButtonText]="submitButtonText | translate"
          [cancelButtonText]="cancelButtonText | translate"
          [savingStatus]="savingStatus"
          (clickedOnSubmit)="onSubmit()"
          (clickedOnCancel)="onCancel()"
        ></crm-form-button-group>
      </div>
    }
  </form>
</crm-form-loading-indicator>
