import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON> } from './renderers/boolean';
import { Date<PERSON>ieldHandler } from './renderers/date';
import { DatetimeFieldHandler } from './renderers/datetime';
import { emailValidatorCodeMapping } from './renderers/email_validator';
import { IntegerFieldHandler } from './renderers/integer';
import { FloatFieldHandler } from './renderers/float/float-field-handler';
import { PhoneFieldHandler } from './renderers/phone/phone-field-handler';
import { TagsFieldHandler } from './renderers/tags';
import { TextFieldHandler } from './renderers/text';
import { CurrencyFieldHandler } from './renderers/currency/currency-field-handler';
import { FieldValidation } from './field-validation';
import { Inject, Injectable } from '@angular/core';
import { ValidatorFactoryService } from './validator-factory.service';
import { CustomFieldHandler } from './renderers/custom';
import { EmailFieldHandler } from './renderers/email';
import { GeopointFieldHandler } from './renderers/geopoint';
import { GooglePlaceSelectorComponent } from './renderers/google-place-selector/google-place-selector.component';
import {
  CrmDependencies,
  CrmInjectionToken,
  FieldSchemaInterface,
  FieldType,
  FormCustomInput,
  ObjectType,
  RenderableFieldInterface,
} from '../../tokens-and-interfaces';
import { StandardIds, SystemFieldIds } from '../../shared-services';
import { UntypedFormGroup } from '@angular/forms';
import { buildE164FormatValidator } from './renderers/phone/phone-validator';
import { PipelineSelectorComponent } from './renderers/pipeline-selector/pipeline-selector.component';
import { OpportunityNameComponent } from './renderers/opportunity-name/opportunity-name.component';
import { HiddenTextFormInputComponent } from './renderers/hidden/hidden-text';
import { HiddenDatetimeFormInputComponent } from './renderers/hidden/hidden-datetime';
import { MarketingConsentSelectorComponent } from './renderers/marketing-consent-selector/marketing-consent-selector.component';
import { SocialMediaUrlComponent } from './renderers/url/social-media-url';
import { StageSelectorComponent } from './renderers/stage-selector/stage-selector.component';

@Injectable()
export class RendererSelectorService {
  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    private readonly validatorFactory: ValidatorFactoryService,
  ) {}

  defaultCustomInputs: FormCustomInput[] = [
    {
      fieldId: StandardIds.CompanyName,
      component: GooglePlaceSelectorComponent,
    },
    {
      fieldId: StandardIds.OpportunityPipelineID,
      component: PipelineSelectorComponent,
    },
    {
      fieldId: StandardIds.OpportunityName,
      component: OpportunityNameComponent,
    },
    {
      fieldId: StandardIds.OpportunityProbability,
      component: StageSelectorComponent,
    },
    {
      fieldId: StandardIds.CompanyPrimaryAddressGooglePlaceID,
      component: HiddenTextFormInputComponent,
    },
    {
      fieldId: StandardIds.ContactMarketingEmailConsentStatus,
      component: MarketingConsentSelectorComponent,
    },
    {
      fieldId: StandardIds.ContactMarketingEmailConsentTimestamp,
      component: HiddenDatetimeFormInputComponent,
    },
    {
      fieldId: StandardIds.ContactMarketingEmailConsentSource,
      component: HiddenTextFormInputComponent,
    },
    {
      fieldId: StandardIds.ContactSMSConsentStatus,
      component: MarketingConsentSelectorComponent,
    },
    {
      fieldId: StandardIds.ContactSMSConsentTimestamp,
      component: HiddenDatetimeFormInputComponent,
    },
    {
      fieldId: StandardIds.ContactSMSConsentSource,
      component: HiddenTextFormInputComponent,
    },
    {
      fieldId: StandardIds.CompanyLinkedInURL,
      component: SocialMediaUrlComponent,
    },
    {
      fieldId: StandardIds.CompanyFacebookURL,
      component: SocialMediaUrlComponent,
    },
    {
      fieldId: StandardIds.CompanyPinterestURL,
      component: SocialMediaUrlComponent,
    },
    {
      fieldId: StandardIds.CompanyTikTokURL,
      component: SocialMediaUrlComponent,
    },
    {
      fieldId: StandardIds.CompanyXURL,
      component: SocialMediaUrlComponent,
    },
    {
      fieldId: StandardIds.CompanyInstagramURL,
      component: SocialMediaUrlComponent,
    },
    {
      fieldId: StandardIds.CompanyWebsite,
      component: SocialMediaUrlComponent,
    },
    {
      fieldId: StandardIds.OpportunityStatus,
      component: HiddenTextFormInputComponent,
    },
  ];

  chooseHandler(
    objectType: ObjectType,
    fieldId: string,
    fieldSchema: FieldSchemaInterface,
    fieldData: any,
    showLabel: boolean,
    readOnly = false,
    formGroup: UntypedFormGroup,
    isEditing = false,
    objectId?: string,
  ): RenderableFieldInterface | null {
    const label = showLabel ? fieldSchema.fieldName : '';
    const customForms: FormCustomInput[] = [];
    const objectDependencies = this.config[objectType.toLowerCase() as keyof CrmDependencies];
    if (
      objectDependencies &&
      typeof objectDependencies === 'object' &&
      'formCustomInputs' in objectDependencies &&
      objectDependencies.formCustomInputs
    ) {
      customForms.push(...objectDependencies.formCustomInputs);
    }
    customForms.push(...this.defaultCustomInputs);

    const customForm = customForms?.find((f) => f.fieldId === fieldId);
    if (customForm) {
      return new CustomFieldHandler(
        customForm,
        objectType,
        fieldId,
        label,
        fieldData,
        readOnly,
        formGroup,
        isEditing,
        objectId,
      );
    }

    if (fieldId === StandardIds.CompanyPrimaryAddressLatitudeLongitude) {
      return new GeopointFieldHandler(
        objectType,
        fieldId,
        fieldSchema.fieldName,
        label,
        fieldData,
        fieldSchema.fieldDescription,
        true,
      );
    }

    switch (fieldSchema.fieldType) {
      case FieldType.FIELD_TYPE_PHONE:
        return new PhoneFieldHandler(
          objectType,
          fieldId,
          fieldSchema.fieldName,
          label,
          fieldData,
          fieldSchema.fieldDescription,
          readOnly,
        );
      case FieldType.FIELD_TYPE_STRING:
        return new TextFieldHandler(
          objectType,
          fieldId,
          fieldSchema.fieldName,
          label,
          fieldData,
          fieldSchema.fieldDescription,
          readOnly,
        );
      case FieldType.FIELD_TYPE_INTEGER:
        return new IntegerFieldHandler(
          objectType,
          fieldId,
          fieldSchema.fieldName,
          label,
          fieldData as number,
          fieldSchema.fieldDescription,
          readOnly,
        );
      case FieldType.FIELD_TYPE_FLOAT:
        return new FloatFieldHandler(
          objectType,
          fieldId,
          fieldSchema.fieldName,
          label,
          fieldData as number,
          fieldSchema.fieldDescription,
          readOnly,
        );
      case FieldType.FIELD_TYPE_DATE:
        return new DateFieldHandler(
          objectType,
          fieldId,
          fieldSchema.fieldName,
          label,
          fieldData,
          fieldSchema.fieldDescription,
          readOnly,
        );
      case FieldType.FIELD_TYPE_DATETIME:
        return new DatetimeFieldHandler(
          objectType,
          fieldId,
          fieldSchema.fieldName,
          label,
          fieldData,
          fieldSchema.fieldDescription,
          readOnly,
        );
      case FieldType.FIELD_TYPE_BOOLEAN:
        return new BooleanFieldHandler(
          objectType,
          fieldId,
          fieldSchema.fieldName,
          label,
          fieldData,
          fieldSchema.fieldDescription,
          readOnly,
        );
      case FieldType.FIELD_TYPE_EMAIL:
        return new EmailFieldHandler(
          objectType,
          fieldId,
          fieldSchema.fieldName,
          label,
          fieldData,
          fieldSchema.fieldDescription,
          readOnly,
        );
      case FieldType.FIELD_TYPE_TAG:
        return new TagsFieldHandler(
          objectType,
          fieldId,
          fieldSchema.fieldName,
          label,
          fieldData,
          fieldSchema.fieldDescription,
          readOnly,
          25,
          200,
          'MODEL_DRIVEN_FORM.ADD_TAG_PROMPT',
          'MODEL_DRIVEN_FORM.TAG_LIMIT_ERROR',
        );
      case FieldType.FIELD_TYPE_STRING_LIST:
        return new TagsFieldHandler(
          objectType,
          fieldId,
          fieldSchema.fieldName,
          label,
          fieldData,
          fieldSchema.fieldDescription,
          readOnly,
          25, // use same limit as tags
          200, // use same limit as tags
          'MODEL_DRIVEN_FORM.ADD_STRINGLIST_ITEM_PROMPT',
          '',
        );
      case FieldType.FIELD_TYPE_CURRENCY:
        return new CurrencyFieldHandler(
          objectType,
          fieldId,
          fieldSchema.fieldName,
          label,
          fieldData,
          fieldSchema.fieldDescription,
          readOnly,
        );
      default:
        console.warn(
          `Could not choose renderer/handler for field ${fieldSchema.fieldId} with type ${fieldSchema.fieldType}`,
        );
        return null;
    }
  }

  setupFieldValidators(field: RenderableFieldInterface, fieldSchemaMap: Map<string, FieldSchemaInterface>): void {
    const schema = fieldSchemaMap.get(field.fieldId);
    const validation = new FieldValidation();
    const systemExternalIds = [SystemFieldIds.CompanyExternalID, SystemFieldIds.ContactExternalID];

    // Add field schema validators to all fields
    validation.addValidationError(this.validatorFactory.buildFieldSchemaValidatorCodeMappings(field.fieldId));

    switch (schema?.fieldType) {
      case FieldType.FIELD_TYPE_STRING:
        if (systemExternalIds.includes(field.fieldId)) {
          validation.addValidationError(this.validatorFactory.buildUniquenessValidatorCodeMappings(field.fieldId));
        }
        break;
      case FieldType.FIELD_TYPE_EMAIL:
        validation.addValidationError(emailValidatorCodeMapping);
        validation.addValidationError(this.validatorFactory.buildUniquenessValidatorCodeMappings(field.fieldId));
        break;
      case FieldType.FIELD_TYPE_PHONE:
        validation.addValidationError(this.validatorFactory.buildUniquenessValidatorCodeMappings(field.fieldId));
        validation.addValidationError(buildE164FormatValidator(schema ?? ({} as FieldSchemaInterface)));
        break;
      default:
        // Field schema validators are already added above for all field types
        break;
    }

    // Set validation for all fields (not just specific types)
    field.setValidation(validation);
  }
}
