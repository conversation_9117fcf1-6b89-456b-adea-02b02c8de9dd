import { ObjectType, RecordChangeType } from './tokens-and-interfaces';

export const businessAccountLink = '/businesses/accounts/%s/details';
export const salesCenterLink = '/sales/redirect?nextUrl=/info/%s';
export const SNACKBAR_DURATION = 10000;
export const SEARCH_DEBOUNCE_MS = 300;
export const LIST_OBJECTS_DEFAULT_PAGE_SIZE = 25;
export const LIST_OBJECTS_PAGE_SIZE_OPTIONS = [LIST_OBJECTS_DEFAULT_PAGE_SIZE, 50, 100];
export const SYSTEM_NAMESPACE = 'system';
export const ACTIVITIES_TIMELINE_PAGE_SIZE = 25;
export const VALID_RECORD_CHANGE_TYPES = ['CREATED', 'IMPORTANT_FIELD_UPDATED'] as RecordChangeType[];
export const AVATAR_WIDTH = 36;
export const RESERVED_TAGS = ['primary'];
export const PRIMARY_TAG = 'Primary';
export const DEFAULT_TAG_SEARCH_SIZE = 10;
// paths cannot start with a slash
export const PAGE_ROUTES = {
  CONTACT: {
    ROOT: 'contact',
    SUBROUTES: {
      LIST: 'list',
      BOARD: 'board',
      CREATE: 'create',
      EDIT: 'edit/:crmObjectId',
      PROFILE: 'profile/:crmObjectId',
    },
    USER: 'bc-admin',
  },
  COMPANY: {
    ROOT: 'company',
    SUBROUTES: {
      LIST: 'list',
      BOARD: 'board',
      CREATE: 'create',
      EDIT: 'edit/:crmObjectId',
      PROFILE: 'profile/:crmObjectId',
      REDIRECT: 'redirect',
    },
    ACCOUNT: 'manage-accounts',
  },
  OPPORTUNITY: {
    ROOT: 'opportunity',
    SUBROUTES: {
      LIST: 'list',
      BOARD: 'board',
      CREATE: 'create',
      EDIT: 'edit/:crmObjectId',
      PROFILE: 'details/:crmObjectId',
    },
  },
  TASK: {
    ROOT: 'task',
    SUBROUTES: {
      LIST: 'list',
      BOARD: 'board',
    },
  },
  ACTIVITIES: {
    ROOT: 'activities',
    SUBROUTES: {
      FEED: 'feed',
    },
  },
  PROSPECT: {
    ROOT: 'prospect',
    SUBROUTES: {
      BUSINESSES: 'find-businesses',
    },
  },
  CUSTOM_FIELDS: {
    ROOT: 'custom-fields',
    SUBROUTES: {
      LIST: 'list',
    },
  },
  PIPELINE_SETTINGS: {
    ROOT: 'pipeline/settings',
  },
  CUSTOM_OBJECT: {
    ROOT: 'custom-object/:customObjectTypeID',
    SUBROUTES: {
      LIST: 'list',
      BOARD: 'board',
      CREATE: 'create',
      EDIT: 'edit/:crmObjectId',
      PROFILE: 'profile/:crmObjectId',
    },
  },
  PERMISSION_REQUIRED: 'permission-required',
  BULK_IMPORT: {
    ROOT: 'bulk-import',
  },
  MULTI_LOCATION: {
    CONTACTS: {
      ROOT: 'contacts',
      SUBROUTES: {
        LIST: 'list',
        EDIT: ':namespace/edit/:crmObjectId',
        PROFILE: ':namespace/profile/:crmObjectId',
      },
    },
  },
};

export const enum BREAKPOINT_WIDTH {
  NAV = '768px',
  TABLET = '1150px',
  MOBILE = '768px',
}

export const enum AssociationType {
  ContactToCompany = 'Contact-Company',
  CompanyToCompany = 'Company-Company',
  ContactToOpportunity = 'Contact-Opportunity',
  CompanyToOpportunity = 'Company-Opportunity',
  ContactToCustomObject = 'Contact-CustomObject',
  CompanyToCustomObject = 'Company-CustomObject',
  OpportunityToCustomObject = 'Opportunity-CustomObject',
  CustomObjectToCustomObject = 'CustomObject-CustomObject',
  Invalid = 'invalid',
}

export const ASSOCIATION_MAP = new Map<ObjectType, Map<ObjectType, AssociationType>>([
  [
    'Contact',
    new Map([
      ['Company', AssociationType.ContactToCompany],
      ['Opportunity', AssociationType.ContactToOpportunity],
      ['CustomObject', AssociationType.ContactToCustomObject],
    ]),
  ],
  [
    'Company',
    new Map([
      ['Contact', AssociationType.ContactToCompany],
      ['Company', AssociationType.CompanyToCompany],
      ['Opportunity', AssociationType.CompanyToOpportunity],
      ['CustomObject', AssociationType.CompanyToCustomObject],
    ]),
  ],
  [
    'Opportunity',
    new Map([
      ['Contact', AssociationType.ContactToOpportunity],
      ['Company', AssociationType.CompanyToOpportunity],
      ['CustomObject', AssociationType.OpportunityToCustomObject],
    ]),
  ],
  [
    'CustomObject',
    new Map([
      ['Contact', AssociationType.ContactToCustomObject],
      ['Company', AssociationType.CompanyToCustomObject],
      ['Opportunity', AssociationType.OpportunityToCustomObject],
      ['CustomObject', AssociationType.CustomObjectToCustomObject],
    ]),
  ],
]);

export const enum AssociationDirection {
  From = 'from',
  To = 'to',
  Invalid = 'Invalid',
}

export const enum ObjectTypePrefix {
  Opportunity = 'OpportunityID',
}

export const enum OpportunityStatus {
  ClosedWon = 'Closed Won',
  ClosedLost = 'Closed Lost',
  Open = 'Open',
}
