import { Inject, Injectable } from '@angular/core';
import {
  Activity,
  ActivityInterface,
  CRMActivityApiService,
  FieldMaskInterface,
  ListActivitiesRequestInterface,
  ListActivitiesResponseInterface,
} from '@vendasta/crm';
import { Observable } from 'rxjs';
import { map, switchMap, take } from 'rxjs/operators';
import { CrmDependencies, CrmInjectionToken } from '../../tokens-and-interfaces';

@Injectable()
export class ActivityService {
  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    private readonly activityService: CRMActivityApiService,
  ) {}

  listActivities(request: ListActivitiesRequestInterface): Observable<ListActivitiesResponseInterface> {
    return this.config.namespace$.pipe(
      switchMap((namespace) => this.activityService.listActivities({ ...request, namespace: namespace }).pipe(take(1))),
    );
  }

  createActivity(activity: ActivityInterface): Observable<string> {
    return this.config.namespace$.pipe(
      switchMap((namespace) =>
        this.activityService.createActivity({ namespace: namespace, activity: activity }).pipe(
          map((response) => response.crmObjectId),
          take(1),
        ),
      ),
    );
  }

  getMultiActivities(crmObjectIds: string[]): Observable<Activity[]> {
    return this.config.namespace$.pipe(
      switchMap((namespace) =>
        this.activityService
          .getMultiActivity({ namespace: namespace, crmObjectIds: crmObjectIds })
          .pipe(map((response) => response.activities)),
      ),
    );
  }

  updateActivity(activity: ActivityInterface, fieldMask: FieldMaskInterface): Observable<null> {
    const fieldMaskPaths = fieldMask?.paths?.length ?? 0;
    if (fieldMaskPaths === 0) {
      throw new Error('Field mask with at least one path is required for updating an activity');
    }
    return this.config.namespace$.pipe(
      switchMap((namespace) =>
        this.activityService.updateActivity({ namespace: namespace, activity: activity, fieldMask: fieldMask }),
      ),
      map(() => null),
    );
  }

  updateActivityMultilocation(
    activity: ActivityInterface,
    fieldMask: FieldMaskInterface,
    namespace: string | null,
  ): Observable<null> {
    const fieldMaskPaths = fieldMask?.paths?.length ?? 0;
    if (fieldMaskPaths === 0) {
      throw new Error('Field mask with at least one path is required for updating an activity');
    }
    if (!namespace) {
      throw new Error('Namespace is required for updating an activity');
    }
    return this.activityService
      .updateActivity({ namespace: namespace, activity: activity, fieldMask: fieldMask })
      .pipe(map(() => null));
  }
}
