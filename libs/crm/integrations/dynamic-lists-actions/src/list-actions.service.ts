import { inject, Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Row } from '@vendasta/galaxy/table';
import { convertGalaxyFilters, CRMSelectAllOptions, ObjectType } from '@galaxy/crm/static';
import { firstValueFrom, take } from 'rxjs';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { DialogData, SelectListModalComponent } from './select-list-modal/select-list-modal.component';
import { ListItemsApiService, ListDefinition } from '@vendasta/bulk-actions';
import { ObjectSearch } from '@vendasta/bulk-actions';

@Injectable({ providedIn: 'root' })
export class ListActionsService {
  private readonly dialog = inject(MatDialog);
  private readonly listItemsApiService = inject(ListItemsApiService);
  private readonly snackbarService = inject(SnackbarService);

  private async openSelectListModal(
    namespace: string,
    objectType: ObjectType,
    numberOfRows: number,
  ): Promise<ListDefinition> {
    const data = {
      namespace: namespace,
      objectType: objectType,
      numberOfRows: numberOfRows,
    } as DialogData;
    const dialog = this.dialog.open(SelectListModalComponent, {
      data: data,
      width: '500px',
    });
    return firstValueFrom(dialog.afterClosed());
  }

  async addToList(namespace: string, objectType: ObjectType, rows: Row[]): Promise<void> {
    const resourceIds = rows.map((row) => row.id).filter((id) => id);
    const selectedList = await this.openSelectListModal(namespace, objectType, resourceIds.length);
    if (!selectedList || resourceIds.length === 0) {
      return;
    }
    this.listItemsApiService
      .addMultiStaticItem({
        namespace: namespace,
        listId: selectedList.listId,
        resourceId: resourceIds,
      })
      .pipe(take(1))
      .subscribe({
        next: () => {
          const messageKey =
            resourceIds.length === 1
              ? 'DYNAMIC_LISTS.SUCCESS.ADDED_TO_LIST_SINGULAR'
              : 'DYNAMIC_LISTS.SUCCESS.ADDED_TO_LIST';
          this.snackbarService.openSuccessSnack(messageKey, {
            interpolateTranslateParams: {
              numberOfItems: resourceIds.length?.toString() ?? '0',
              listName: selectedList.name,
            },
          });
        },
        error: () => {
          this.snackbarService.openErrorSnack('DYNAMIC_LISTS.ERRORS.FAILED_TO_ADD', {
            interpolateTranslateParams: {
              listName: selectedList.name,
            },
          });
        },
      });
  }

  async addSelectAllToList(namespace: string, objectType: ObjectType, options?: CRMSelectAllOptions): Promise<void> {
    const selectedList = await this.openSelectListModal(namespace, objectType, options?.totalObjects || 0);
    if (!selectedList || options?.totalObjects === 0) {
      return;
    }
    const filters = convertGalaxyFilters(options?.filters);

    this.listItemsApiService
      .bulkAddStaticItems({
        namespace: namespace,
        listId: selectedList.listId,
        search: {
          searchTerm: options?.search || '',
        } as ObjectSearch,
        filters: filters,
      })
      .pipe(take(1))
      .subscribe({
        next: () => {
          const messageKey =
            options?.totalObjects === 1
              ? 'DYNAMIC_LISTS.SUCCESS.ADDING_TO_LIST_SINGULAR'
              : 'DYNAMIC_LISTS.SUCCESS.ADDING_TO_LIST';
          this.snackbarService.openSuccessSnack(messageKey, {
            interpolateTranslateParams: {
              numberOfItems: options?.totalObjects?.toString() || '0',
              listName: selectedList.name,
            },
          });
        },
        error: () => {
          this.snackbarService.openErrorSnack('DYNAMIC_LISTS.ERRORS.FAILED_TO_ADD', {
            interpolateTranslateParams: {
              listName: selectedList.name,
            },
          });
        },
      });
  }
}
