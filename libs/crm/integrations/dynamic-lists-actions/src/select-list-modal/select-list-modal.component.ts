import { CommonModule } from '@angular/common';
import { Component, ElementRef, inject, OnInit, ViewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogClose, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { GalaxyConfirmationModalModule } from '@vendasta/galaxy/confirmation-modal';
import { ObjectType, TranslationModule } from '@galaxy/crm/static';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import {
  FilterGroupOperator,
  ListDefinition,
  ListDefinitionsApiService,
  FilterOperator,
  ListDefinitionsResponse,
} from '@vendasta/bulk-actions';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';

import { MatInput } from '@angular/material/input';
import { GalaxyTagsModule } from '@vendasta/galaxy/tags';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { catchError, expand, Observable, of, reduce, take, takeWhile } from 'rxjs';
import { MatAutocomplete, MatAutocompleteTrigger, MatOption } from '@angular/material/autocomplete';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';

export interface DialogData {
  namespace: string;
  objectType: ObjectType;
  numberOfRows: number;
}

@Component({
  imports: [
    CommonModule,
    GalaxyConfirmationModalModule,
    MatButtonModule,
    MatIconModule,
    MatDialogClose,
    TranslationModule,
    GalaxyFormFieldModule,
    MatInput,
    ReactiveFormsModule,
    GalaxyTagsModule,
    GalaxyTooltipModule,
    MatAutocompleteTrigger,
    MatAutocomplete,
    MatOption,
    GalaxyPipesModule,
  ],
  selector: 'crm-select-list-modal',
  templateUrl: `./select-list-modal.component.html`,
  styleUrls: [`./select-list-modal.component.scss`],
  providers: [],
})
export class SelectListModalComponent implements OnInit {
  public dialogRef = inject(MatDialogRef<SelectListModalComponent>);
  private readonly listDefinitionsApiService = inject(ListDefinitionsApiService);
  private readonly dialogData: DialogData = inject(MAT_DIALOG_DATA);
  resourceType = this.dialogData?.objectType ?? 'Contact';
  numberOfRows = this.dialogData?.numberOfRows ?? 0;

  options: ListDefinition[] = [];
  filteredOptions!: ListDefinition[];
  selectedListControl = new FormControl<string>('', { nonNullable: true, validators: [Validators.required] });

  @ViewChild('input') input: ElementRef<HTMLInputElement> | undefined;

  ngOnInit(): void {
    this.fetchAllLists(this.dialogData.namespace, this.dialogData.objectType)
      .pipe(take(1))
      .subscribe((lists) => {
        this.options = lists ?? [];
        this.filteredOptions = this.options;
      });
  }

  private fetchListDefinitions(
    namespace: string,
    objectType: string,
    cursor: string,
    pageSize: number,
  ): Observable<ListDefinitionsResponse> {
    return this.listDefinitionsApiService.listDefinitions({
      namespace: namespace,
      filters: {
        operator: FilterGroupOperator.FILTER_GROUP_OPERATOR_AND,
        filters: [
          {
            fieldId: 'resource_type',
            operator: FilterOperator.FILTER_OPERATOR_IS,
            values: [{ string: objectType }],
          },
          {
            fieldId: 'list_type',
            operator: FilterOperator.FILTER_OPERATOR_IS,
            values: [{ string: 'Static' }],
          },
        ],
      },
      pagingOptions: {
        cursor: cursor,
        pageSize: pageSize,
      },
    });
  }

  private fetchAllLists(namespace: string, objectType: string): Observable<ListDefinition[]> {
    return this.fetchListDefinitions(namespace, objectType, '', 100).pipe(
      expand((response) => this.fetchListDefinitions(namespace, objectType, response.pagingMetadata?.nextCursor, 100)),
      takeWhile((response) => !!response.pagingMetadata?.hasMore, true),
      reduce((all, { listDefinitions = [] }) => all.concat(listDefinitions), [] as ListDefinition[]),
      catchError(() => {
        return of([]);
      }),
    );
  }

  filter(): void {
    const filterValue = this.input?.nativeElement.value.toLowerCase() || '';
    this.filteredOptions = this.options.filter((o) => o?.name.toLowerCase().includes(filterValue));
  }

  addToList() {
    if (this.selectedListControl.invalid) {
      return;
    }
    const selectedList = this.options.find((definition) => definition.listId === this.selectedListControl.value);
    this.dialogRef.close(selectedList);
  }

  displayFn(options: ListDefinition[]): (listId: string) => string {
    return (listId: string): string => {
      return options?.find((option) => option.listId === listId)?.name || '';
    };
  }
}
