<glxy-confirmation-body>
  <glxy-confirmation-title>
    {{ 'DYNAMIC_LISTS.SELECT_LIST_MODAL.TITLE' | translate }}
  </glxy-confirmation-title>
  <glxy-confirmation-custom-content>
    <form>
      @if (filteredOptions) {
        <glxy-form-field suffixIcon="arrow_drop_down">
          <glxy-label>
            @switch (resourceType) {
              @case ('Contact') {
                {{ 'DYNAMIC_LISTS.SELECT_LIST_MODAL.SELECT_CONTACT_LIST_LABEL' | translate }}
              }
              @case ('Company') {
                {{ 'DYNAMIC_LISTS.SELECT_LIST_MODAL.SELECT_COMPANY_LIST_LABEL' | translate }}
              }
            }
          </glxy-label>
          <input
            #input
            type="text"
            [placeholder]="'DYNAMIC_LISTS.SELECT_LIST_MODAL.SELECT_LIST_PLACEHOLDER' | translate"
            matInput
            [formControl]="selectedListControl"
            [matAutocomplete]="auto"
            (input)="filter()"
            (focus)="filter()"
          />
          <mat-autocomplete [requireSelection]="true" #auto="matAutocomplete" [displayWith]="displayFn(options)">
            @for (option of filteredOptions; track option) {
              <mat-option [value]="option.listId">{{ option.name }}</mat-option>
            }
          </mat-autocomplete>
          @if (!selectedListControl.untouched && selectedListControl.hasError('required')) {
            <glxy-error>
              {{ 'DYNAMIC_LISTS.SELECT_LIST_MODAL.SELECT_LIST_REQUIRED' | translate }}
            </glxy-error>
          }
        </glxy-form-field>
      } @else {
        <div class="stencil-shimmer"></div>
      }
    </form>
  </glxy-confirmation-custom-content>
</glxy-confirmation-body>

<glxy-confirmation-actions>
  <glxy-confirmation-primary-actions>
    @switch (resourceType) {
      @case ('Contact') {
        <div
          class="number-of-rows-selected"
          [innerHTML]="
            'DYNAMIC_LISTS.SELECT_LIST_MODAL.AMOUNT_OF_SELECTED_CONTACTS'
              | translate: { amount: numberOfRows }
              | iTrustThisHtml
          "
        ></div>
      }
      @case ('Company') {
        <div
          class="number-of-rows-selected"
          [innerHTML]="
            'DYNAMIC_LISTS.SELECT_LIST_MODAL.AMOUNT_OF_SELECTED_COMPANIES'
              | translate: { amount: numberOfRows }
              | iTrustThisHtml
          "
        ></div>
      }
    }
    <button mat-stroked-button matDialogClose>
      {{ 'ACTIONS.CANCEL' | translate }}
    </button>

    <button mat-flat-button color="primary" (click)="addToList()">
      {{ 'DYNAMIC_LISTS.ACTIONS.ADD' | translate }}
    </button>
  </glxy-confirmation-primary-actions>
</glxy-confirmation-actions>
