import { Inject, Injectable, Optional } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Row } from '@vendasta/galaxy/table';
import { StartManualAutomationDialogComponent, StartManualAutomationDialogData } from '@galaxy/automata/shared';
import { AutomataOperationsService, Automation, Context, EntityType } from '@vendasta/automata';
import {
  convertGalaxyFilters,
  CRM_ACCESS_SERVICE_TOKEN,
  CrmAccessService,
  CRMSelectAllOptions,
} from '@galaxy/crm/static';
import { combineLatest, firstValueFrom, of, take } from 'rxjs';
import { map } from 'rxjs/operators';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { StartManualAutomationsRequestInterface } from '@vendasta/crm-integrations/lib/_internal/interfaces';
import { CrmAutomationApiService } from '@vendasta/crm-integrations';
import { EphemeralAutomationService } from '@galaxy/automata';

const PERMISSION_DENIED = 7;

function contextForAutomation(namespace: string): Context {
  if (namespace.startsWith('AG-')) {
    return Context.AUTOMATION_CONTEXT_SMB;
  }
  return Context.AUTOMATION_CONTEXT_PARTNER;
}

function onlySalesRunnable(context: Context) {
  if (context === Context.AUTOMATION_CONTEXT_SMB) {
    return false;
  }
  return true;
}

@Injectable({ providedIn: 'root' })
export class AutomationActionsService {
  constructor(
    private readonly dialog: MatDialog,
    private readonly crmAutomationService: CrmAutomationApiService,
    private readonly snackbarService: SnackbarService,
    private readonly ephemeralAutomationService: EphemeralAutomationService,
    private readonly operationsService: AutomataOperationsService,
    @Optional() @Inject(CRM_ACCESS_SERVICE_TOKEN) private readonly accessService: CrmAccessService,
  ) {}

  async openStartManualAutomationDialog(
    namespace: string,
    objectType: string,
    rows: Row[],
    dontStartAutomation: boolean,
    isListOfEntities?: boolean,
  ): Promise<Automation> {
    const canModifyAutomations$ = this.accessService?.canModifyAutomations$
      ? this.accessService.canModifyAutomations$
      : of(false);
    const hasAdminAccess$ = this.accessService?.hasPartnerPermissions$
      ? this.accessService.hasPartnerPermissions$
      : of(false);
    const options = await firstValueFrom(
      combineLatest([canModifyAutomations$, hasAdminAccess$]).pipe(
        map(([editPermission, adminPermission]) => {
          return {
            hideSettingsLink: !editPermission,
            onlySalespersonAutomations: !adminPermission && onlySalesRunnable(contextForAutomation(namespace)),
            onlySelectDontStart: dontStartAutomation,
            isListOfEntities: isListOfEntities,
          };
        }),
      ),
    );

    const entityType = crmObjectTypeToEntityType(objectType);
    const entityMap: Map<EntityType, string[]> = new Map();
    entityMap.set(
      entityType,
      rows.map((row) => row.id),
    );
    const data = {
      namespace: namespace,
      entities: entityMap,
      context: contextForAutomation(namespace),
      options: options,
    } as StartManualAutomationDialogData;
    const dialog = this.dialog.open(StartManualAutomationDialogComponent, {
      data: data,
      width: '500px',
    });
    return firstValueFrom(dialog.afterClosed());
  }

  async openSelectAllStartManualAutomationDialog(
    namespace: string,
    objectType: string,
    options?: CRMSelectAllOptions,
  ): Promise<void> {
    const automation = await this.openStartManualAutomationDialog(namespace, objectType, [], true);
    if (!automation) {
      return;
    }
    this.runSelectAllAutomation(namespace, objectType, automation.id, automation.name, options);
  }

  runSelectAllAutomation(
    namespace: string,
    objectType: string,
    automationId: string,
    automationName: string,
    options?: CRMSelectAllOptions,
  ): void {
    const request = {
      automationId: automationId,
      criteria: {
        namespace: namespace,
        objectType: objectType,
        filters: convertGalaxyFilters(options?.filters || []),
        searchTerm: options?.search,
      },
    } as StartManualAutomationsRequestInterface;
    this.crmAutomationService
      .startManualAutomations(request)
      .pipe(take(1))
      .subscribe({
        next: () => {
          this.snackbarService.openSuccessSnack('AUTOMATIONS.MANUAL_AUTOMATIONS.AUTOMATION_STARTED', {
            interpolateTranslateParams: {
              name: automationName,
            },
          });
        },
        error: (err) => {
          console.error(err);
          if (err.error.code === PERMISSION_DENIED) {
            this.snackbarService.openErrorSnack('AUTOMATIONS.EDITOR.ERRORS.PERMISSION_DENIED_STARTING_AUTOMATION');
          } else {
            this.snackbarService.openErrorSnack('AUTOMATIONS.EDITOR.ERRORS.ERROR_STARTING_AUTOMATION');
          }
        },
      });
  }

  async openEphemeralAutomationSidepanel(namespace: string, objectType: string, rows: Row[]): Promise<boolean> {
    const entityType = crmObjectTypeToEntityType(objectType);
    const automationId = await this.buildEphemeralAutomationToRun(namespace, entityType);
    if (!automationId) {
      return false;
    }
    const startEntities$ = this.operationsService.startAutomation(
      automationId,
      undefined,
      undefined,
      rows.map((row) => ({ entityId: row.id })),
      namespace,
    );
    return firstValueFrom(startEntities$);
  }

  async openSelectAllBulkActionSidepanel(
    namespace: string,
    objectType: string,
    options?: CRMSelectAllOptions,
  ): Promise<void> {
    const entityType = crmObjectTypeToEntityType(objectType);
    const automationId = await this.buildEphemeralAutomationToRun(namespace, entityType);
    if (!automationId) {
      return;
    }
    this.runSelectAllAutomation(namespace, objectType, automationId, 'Bulk Action', options);
    return;
  }

  buildEphemeralAutomationToRun(namespace: string, entityType: EntityType): Promise<string> {
    return firstValueFrom(this.ephemeralAutomationService.createEphemeralAutomation(namespace, entityType));
  }
}

function crmObjectTypeToEntityType(objectType: string): EntityType {
  let entityType = EntityType.ENTITY_TYPE_COMPANY;
  if (objectType === 'Contact') {
    entityType = EntityType.ENTITY_TYPE_CONTACT;
  }
  return entityType;
}
