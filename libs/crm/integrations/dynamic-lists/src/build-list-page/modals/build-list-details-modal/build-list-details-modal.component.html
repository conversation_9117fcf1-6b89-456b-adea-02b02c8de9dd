@if (loadingPermissions()) {
  <glxy-loading-spinner></glxy-loading-spinner>
} @else {
  <glxy-confirmation-body>
    <glxy-confirmation-title>
      @if (isEditing) {
        {{ 'DYNAMIC_LISTS.LIST_DETAILS_MODAL.TITLE' | translate }}
      } @else {
        {{ 'DYNAMIC_LISTS.CREATE_MODAL.TITLE' | translate }}
      }
    </glxy-confirmation-title>
    <glxy-confirmation-custom-content>
      <form [formGroup]="createForm">
        @if (!isEditing) {
          <glxy-form-field>
            <glxy-label>{{ 'DYNAMIC_LISTS.CREATE_MODAL.FORM.RESOURCE_TYPE' | translate }}</glxy-label>
            <mat-button-toggle-group formControlName="resourceType">
              @if (hasContactPermissions$ | async) {
                <mat-button-toggle value="Contact">
                  <mat-icon>person</mat-icon>
                  {{ 'CONTACT.TITLE' | translate }}
                </mat-button-toggle>
              }
              @if (hasCompanyPermissions$ | async) {
                <mat-button-toggle value="Company">
                  <mat-icon>business</mat-icon>
                  {{ 'COMPANY.TITLE' | translate }}
                </mat-button-toggle>
              }
            </mat-button-toggle-group>
          </glxy-form-field>
          <glxy-form-field>
            <glxy-label>
              {{ 'DYNAMIC_LISTS.CREATE_MODAL.FORM.LIST_TYPE' | translate }}
              <mat-icon
                [glxyTooltip]="'DYNAMIC_LISTS.LIST_TYPES_DESCRIPTION' | translate"
                [tooltipPositions]="tooltipPositions"
                class="info-icon"
              >
                info_outline
              </mat-icon>
            </glxy-label>
            <mat-button-toggle-group formControlName="listType">
              <mat-button-toggle [value]="ListType.LIST_TYPE_STATIC">
                <mat-icon>reorder</mat-icon>
                {{ 'DYNAMIC_LISTS.LIST_TYPES.STATIC' | translate }}
              </mat-button-toggle>
              <mat-button-toggle [value]="ListType.LIST_TYPE_DYNAMIC">
                <mat-icon>clear_all</mat-icon>
                {{ 'DYNAMIC_LISTS.LIST_TYPES.SMART' | translate }}
              </mat-button-toggle>
            </mat-button-toggle-group>
          </glxy-form-field>
        }
        <glxy-form-field required="true">
          <glxy-label>{{ 'DYNAMIC_LISTS.CREATE_MODAL.FORM.NAME' | translate }}</glxy-label>
          <input
            formControlName="name"
            type="text"
            [placeholder]="'DYNAMIC_LISTS.CREATE_MODAL.FORM.NAME_PLACEHOLDER' | translate"
            matInput
          />
          <glxy-error *ngIf="!name.untouched && name.hasError('required')">
            {{ 'DYNAMIC_LISTS.CREATE_MODAL.FORM.NAME_REQUIRED' | translate }}
          </glxy-error>
        </glxy-form-field>
        <glxy-form-field>
          <glxy-label>{{ 'DYNAMIC_LISTS.CREATE_MODAL.FORM.DESCRIPTION' | translate }}</glxy-label>
          <textarea
            formControlName="description"
            matInput
            [placeholder]="'DYNAMIC_LISTS.CREATE_MODAL.FORM.DESCRIPTION_PLACEHOLDER' | translate"
            cdkTextareaAutosize
            cdkAutosizeMinRows="3"
            cdkAutosizeMaxRows="10"
            maxlength="500"
          ></textarea>
          <glxy-hint align="right">{{ description.value?.length || 0 }}/500</glxy-hint>
        </glxy-form-field>
      </form>
    </glxy-confirmation-custom-content>
  </glxy-confirmation-body>

  <glxy-confirmation-actions>
    <glxy-confirmation-primary-actions>
      <button mat-stroked-button matDialogClose>
        {{ 'ACTIONS.CANCEL' | translate }}
      </button>

      <button mat-flat-button color="primary" (click)="saveListDetails()">
        @if (isEditing) {
          {{ 'ACTIONS.SAVE' | translate }}
        } @else {
          {{ 'ACTIONS.CREATE' | translate }}
        }
      </button>
    </glxy-confirmation-primary-actions>
  </glxy-confirmation-actions>
}
