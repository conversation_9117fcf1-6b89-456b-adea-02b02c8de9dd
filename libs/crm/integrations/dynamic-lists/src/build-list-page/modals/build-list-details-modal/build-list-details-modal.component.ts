import { CommonModule } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogClose, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { GalaxyConfirmationModalModule } from '@vendasta/galaxy/confirmation-modal';
import { ObjectType, TranslationModule } from '@galaxy/crm/static';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ListDefinitionInterface, ListType } from '@vendasta/bulk-actions';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatButtonToggle, MatButtonToggleGroup } from '@angular/material/button-toggle';
import { CdkTextareaAutosize } from '@angular/cdk/text-field';
import { MatInput } from '@angular/material/input';
import { GalaxyTagsModule } from '@vendasta/galaxy/tags';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { ConnectedPosition } from '@angular/cdk/overlay';
import { PopoverPositions } from '@vendasta/galaxy/popover';
import { CrmDynamicListBuilderService } from '../../dynamic-list-builder.service';
import { take, tap, combineLatest, shareReplay } from 'rxjs';
import { provideServiceFromParentContextOrCreateInTime } from '../../../injectors.utils';
import { CrmDynamicListsAccessServiceWrapper } from '../../../dynamic-lists-access.service';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';

interface DialogData {
  isEditing: boolean;
}

@Component({
  imports: [
    CommonModule,
    GalaxyConfirmationModalModule,
    MatButtonModule,
    MatIconModule,
    MatDialogClose,
    TranslationModule,
    GalaxyFormFieldModule,
    MatButtonToggleGroup,
    MatButtonToggle,
    CdkTextareaAutosize,
    MatInput,
    ReactiveFormsModule,
    GalaxyTagsModule,
    GalaxyTooltipModule,
    GalaxyLoadingSpinnerModule,
  ],
  selector: 'crm-build-list-details-modal',
  templateUrl: `./build-list-details-modal.component.html`,
  styleUrls: [`./build-list-details-modal.component.scss`],
  providers: [provideServiceFromParentContextOrCreateInTime(CrmDynamicListsAccessServiceWrapper)],
})
export class BuildListDetailsModalComponent implements OnInit {
  private readonly accessService = inject(CrmDynamicListsAccessServiceWrapper);

  public dialogRef = inject(MatDialogRef<BuildListDetailsModalComponent>);
  private readonly listBuilderService = inject(CrmDynamicListBuilderService);
  private dialogData: DialogData = inject(MAT_DIALOG_DATA);
  isEditing = this.dialogData?.isEditing || false;

  hasCompanyPermissions$ = this.accessService.hasCompanyPermissions().pipe(shareReplay(1));
  hasContactPermissions$ = this.accessService.hasContactPermissions().pipe(shareReplay(1));
  loadingPermissions = signal(true);

  ListType = ListType;

  resourceType = new FormControl<ObjectType>('Contact', { nonNullable: true });
  listType = new FormControl<ListType>(ListType.LIST_TYPE_STATIC, { nonNullable: true });
  name = new FormControl<string>('', { nonNullable: true, validators: [Validators.required] });
  description = new FormControl<string>('', { nonNullable: true });

  createForm = new FormGroup({
    resourceType: this.resourceType,
    listType: this.listType,
    name: this.name,
    description: this.description,
  });
  readonly tooltipPositions: ConnectedPosition[] = [{ ...PopoverPositions.Right }, { ...PopoverPositions.Bottom }];

  ngOnInit(): void {
    this.setInitialResourceType();

    if (!this.isEditing) {
      this.listBuilderService.editingListId = '';
      this.listBuilderService.cleanUpListDefinitionToSave();
    }

    this.listBuilderService.listWithUnsavedChanges$
      .pipe(
        take(1),
        tap((list: ListDefinitionInterface) => {
          this.createForm.setValue({
            resourceType: <ObjectType>list?.resourceType ?? this.resourceType.value,
            name: list?.name ?? '',
            listType: list?.listType ?? ListType.LIST_TYPE_STATIC,
            description: list?.description ?? '',
          });
        }),
      )
      .subscribe();
  }

  private setInitialResourceType(): void {
    combineLatest([this.hasCompanyPermissions$, this.hasContactPermissions$])
      .pipe(take(1))
      .subscribe(([hasCompany, hasContact]) => {
        if (hasContact) {
          this.resourceType.setValue('Contact');
        } else if (hasCompany) {
          this.resourceType.setValue('Company');
        }
        this.loadingPermissions.set(false);
      });
  }

  saveListDetails() {
    this.createForm.markAllAsTouched();
    if (this.createForm.invalid) {
      return;
    }
    const definitionToSave: ListDefinitionInterface = { ...this.createForm.value };
    this.listBuilderService.changeListDefinitionToSave(definitionToSave);
    this.dialogRef.close(true);
  }
}
