import { CommonModule } from '@angular/common';
import { Component, computed, inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogClose, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { GalaxyConfirmationModalModule } from '@vendasta/galaxy/confirmation-modal';
import { TranslationModule } from '@galaxy/crm/static';
import { ReactiveFormsModule } from '@angular/forms';
import { ListType } from '@vendasta/bulk-actions';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';

import { GalaxyTagsModule } from '@vendasta/galaxy/tags';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { CrmDynamicListBuilderService } from '../../dynamic-list-builder.service';
import { toSignal } from '@angular/core/rxjs-interop';
import { BlankValueComponent } from '@vendasta/galaxy/table';

interface DialogData {
  isEditing: boolean;
}

@Component({
  imports: [
    CommonModule,
    GalaxyConfirmationModalModule,
    MatButtonModule,
    MatIconModule,
    MatDialogClose,
    TranslationModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    GalaxyTagsModule,
    GalaxyTooltipModule,
    BlankValueComponent,
  ],
  selector: 'crm-view-list-details-modal',
  templateUrl: `./view-list-details-modal.component.html`,
  styleUrls: [`./view-list-details-modal.component.scss`],
  providers: [],
})
export class ViewListDetailsModalComponent {
  public dialogRef = inject(MatDialogRef<ViewListDetailsModalComponent>);
  private readonly listBuilderService = inject(CrmDynamicListBuilderService);
  private dialogData: DialogData = inject(MAT_DIALOG_DATA);

  ListType = ListType;
  isEditing = this.dialogData?.isEditing || false;

  listDetails = toSignal(this.listBuilderService.listWithUnsavedChanges$);
  listType = computed(() => {
    switch (this.listDetails()?.listType) {
      case ListType.LIST_TYPE_STATIC:
        return 'DYNAMIC_LISTS.LIST_TYPES.STATIC';
      case ListType.LIST_TYPE_DYNAMIC:
        return 'DYNAMIC_LISTS.LIST_TYPES.SMART';
      default:
        return '';
    }
  });
  resourceType = computed(() => {
    switch (this.listDetails()?.resourceType) {
      case 'Company':
        return 'COMPANY.TITLE';
      case 'Contact':
        return 'CONTACT.TITLE';
      default:
        return '';
    }
  });

  editListDetails() {
    this.dialogRef.close(true);
  }
}
