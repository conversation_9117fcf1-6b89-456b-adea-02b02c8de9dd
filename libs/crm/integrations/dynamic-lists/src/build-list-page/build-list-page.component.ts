import {
  Component,
  computed,
  inject,
  OnD<PERSON>roy,
  OnInit,
  signal,
  ViewChild,
  ViewContainerRef,
  WritableSignal,
} from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { MatButton, MatIconButton } from '@angular/material/button';

import { ConfirmationModalMaxWidth, ConfirmationModalWidth } from '@vendasta/galaxy/confirmation-modal';
import { MatDialog } from '@angular/material/dialog';
import {
  ActivityCustomizationService,
  CompanyTableCustomizationService,
  ContactTableCustomizationService,
  convertToGalaxyFilter,
  CrmFieldOptionsService,
  CrmFiltersService,
  CrmFormService,
  CrmObjectService,
  CRMTrackingService,
  ListObjectsTableComponent,
  ObjectType,
  OpportunityTableCustomizationService,
  PageAnalyticsInjectionToken,
  pageAnalyticsInjectionTokenGenerator,
  RestrictedGroupService,
  tableFiltersInjectionTokenGenerator,
  TranslateByObjectTypeService,
  TranslateForCrmObjectService,
  ValueSummaryService,
} from '@galaxy/crm/static';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { ListType } from '@vendasta/bulk-actions';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { CrmThreePanelPageModule } from '@galaxy/crm/components/three-panel-page';
import { MatIcon } from '@angular/material/icon';
import { MatCard } from '@angular/material/card';

import { FieldIdComponent, FieldOperatorComponent, FieldValueComponent } from './fields';
import { BuildListDetailsModalComponent, ViewListDetailsModalComponent } from './modals';
import { DynamicListFilterService } from './dynamic-list-filters.service';
import { CrmDynamicListBuilderService, ListSaveStatus } from './dynamic-list-builder.service';
import { FilterInterface, FilterOperator, FilterValueInterface } from '@vendasta/crm';
import { GalaxyFilterChipInjectionToken, InnerGalaxyFilterChipInjectionToken } from '@vendasta/galaxy/filter/chips';
import { combineLatest, Observable, of, startWith, Subject, switchMap, take, map, BehaviorSubject } from 'rxjs';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { provideServiceFromParentContextOrCreateInTime } from '../injectors.utils';
import { BlankValueComponent } from '@vendasta/galaxy/table';
import { ComponentCanDeactivate } from './pending-changes-guard.service';
import { FadeIn, LoadingBehavior } from '@galaxy/crm/components/animations';
import { CrmDynamicListItemTableComponent } from '../list-item-table/list-item-table.component';
import { UnaryGalaxyOperators } from '../constants';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { ListStatus } from '../list-item-table/list-item-table.service';

@Component({
  selector: 'crm-dynamic-lists-build-page',
  templateUrl: './build-list-page.component.html',
  styleUrls: ['./build-list-page.component.scss'],
  animations: [LoadingBehavior, FadeIn],
  imports: [
    CommonModule,
    TranslateModule,
    GalaxyPageModule,
    GalaxyEmptyStateModule,
    MatButton,
    GalaxyBadgeModule,
    CrmThreePanelPageModule,
    MatIcon,
    MatCard,
    MatIconButton,
    FieldIdComponent,
    FieldOperatorComponent,
    FieldValueComponent,
    RouterModule,
    BlankValueComponent,
    ListObjectsTableComponent,
    CrmDynamicListItemTableComponent,
    GalaxyAlertModule,
  ],
  providers: [
    CrmFiltersService,
    CrmObjectService,
    CrmFieldOptionsService,
    RestrictedGroupService,
    ValueSummaryService,
    ActivityCustomizationService,
    ContactTableCustomizationService,
    CompanyTableCustomizationService,
    OpportunityTableCustomizationService,
    DynamicListFilterService,
    provideServiceFromParentContextOrCreateInTime(CrmDynamicListBuilderService),
    {
      provide: InnerGalaxyFilterChipInjectionToken,
      useFactory: (comp: CrmDynamicListBuildPageComponent) => {
        return tableFiltersInjectionTokenGenerator(comp.objectType())();
      },
      deps: [CrmDynamicListBuildPageComponent],
    },
    CrmFormService,
    TranslateByObjectTypeService,
    TranslateForCrmObjectService,
    CRMTrackingService,
    {
      provide: GalaxyFilterChipInjectionToken,
      useFactory: (comp: CrmDynamicListBuildPageComponent) => {
        return tableFiltersInjectionTokenGenerator(comp.objectType())();
      },
      deps: [CrmDynamicListBuildPageComponent],
    },
    {
      provide: PageAnalyticsInjectionToken,
      useFactory: (comp: CrmDynamicListBuildPageComponent) => {
        return pageAnalyticsInjectionTokenGenerator(comp.objectType())();
      },
      deps: [CrmDynamicListBuildPageComponent],
    },
  ],
})
export class CrmDynamicListBuildPageComponent extends ComponentCanDeactivate implements OnInit, OnDestroy {
  ListType = ListType;
  ListStatus = ListStatus;

  private readonly dialog = inject(MatDialog);
  private readonly listBuilderService = inject(CrmDynamicListBuilderService);
  private readonly viewContainerRef = inject(ViewContainerRef);
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly location = inject(Location);

  readonly isLoading = toSignal(this.listBuilderService.loading$);
  readonly isEditing = toSignal(this.listBuilderService.isEditing$);
  readonly showEmptyState = signal<boolean>(false);
  readonly hasMany: WritableSignal<boolean> = signal(false);

  currentList = toSignal(this.listBuilderService.listWithUnsavedChanges$);
  filters = computed(() => {
    return this.currentList()?.filter || [];
  });
  listType = computed(() => {
    return this.currentList()?.listType ?? ListType.LIST_TYPE_STATIC;
  });
  objectType = computed(() => {
    return <ObjectType>this.currentList()?.resourceType || 'Contact';
  });
  listStatus = new BehaviorSubject<string>(this.currentList()?.status || ListStatus.READY);
  justSaved = signal(false);

  forceFiltersChanges$$ = new Subject();
  previewFilters$ = combineLatest([toObservable(this.filters), this.forceFiltersChanges$$.pipe(startWith(true))]).pipe(
    map(([filters]) => {
      const convertedFilters = filters
        .map((filter) => convertToGalaxyFilter(filter))
        .filter((f) => {
          if (UnaryGalaxyOperators.includes(f.operator)) {
            return f.fieldId && f.operator;
          }
          return f.fieldId && f.operator && f.values?.length;
        });
      return [...convertedFilters];
    }),
  );
  previewFilters = toSignal(this.previewFilters$);
  previousPage = (this.router.getCurrentNavigation()?.previousNavigation?.finalUrl || '/').toString();
  @ViewChild('tableRef') tableRef?: ListObjectsTableComponent;

  get estimatedTotalSize(): string {
    // use a count from the backend when possible, this is not ideal
    if (this.isEditing() && this.listType() === ListType.LIST_TYPE_STATIC && this.hasMany()) {
      return 'DYNAMIC_LISTS.ESTIMATED_SIZE_INFINITY';
    }
    if (this.tableRef?.totalDataMembers === Infinity) {
      return 'DYNAMIC_LISTS.ESTIMATED_SIZE_UNKNOWN';
    }
    return this.tableRef?.totalDataMembers?.toString() ?? '-';
  }

  ngOnInit(): void {
    const listId = this.route.snapshot.paramMap.get('id') ?? '';
    if (listId) {
      this.listBuilderService.editingListId = listId;
    }
  }

  ngOnDestroy(): void {
    this.listBuilderService.editingListId = '';
    this.listBuilderService.cleanUpListDefinitionToSave();
  }

  addFilter(): void {
    const filters = this.filters();
    this.listBuilderService.changeListDefinitionToSave({
      filter: [...filters, {}],
    });
  }

  updateFilter(index: number, filter: FilterInterface): void {
    const filters = this.filters();
    filters[index] = filter;
    this.forceFiltersChanges$$.next(true);
    this.listBuilderService.changeListDefinitionToSave({
      filter: [...filters],
    });
  }

  removeFilter(index: number): void {
    const filters = [...this.filters()];
    filters.splice(index, 1);
    this.listBuilderService.changeListDefinitionToSave({
      filter: filters,
    });
  }

  setFieldId(index: number, filter: FilterInterface, fieldId?: string): void {
    if (filter && filter.fieldId !== fieldId) {
      filter.fieldId = fieldId;
      this.updateFilter(index, filter);
    }
  }

  setOperator(index: number, filter: FilterInterface, operator?: FilterOperator): void {
    if (filter && filter.operator !== operator) {
      filter.operator = operator;
      this.updateFilter(index, filter);
    }
  }

  setValue(index: number, filter: FilterInterface, value: FilterValueInterface[]): void {
    if (filter && JSON.stringify(filter.values) !== JSON.stringify(value)) {
      filter.values = value;
      this.updateFilter(index, filter);
    }
  }

  private editListDetails(requestedEdit: boolean): Observable<boolean> {
    if (!requestedEdit) {
      return of(false);
    }
    return this.dialog
      .open(BuildListDetailsModalComponent, {
        width: ConfirmationModalWidth,
        maxWidth: ConfirmationModalMaxWidth,
        autoFocus: true,
        viewContainerRef: this.viewContainerRef,
        data: {
          isEditing: true,
        },
      })
      .afterClosed();
  }

  private changeRouteToEdit(): void {
    const listId = this.listBuilderService.editingListId;
    if (listId) {
      // Navigate to the lists page and replace the current history entry
      this.router.navigate(['/lists'], { replaceUrl: true }).then(() => {
        // Navigate to the edit page and replace the lists page in the history
        this.router.navigate([`/lists/edit/${listId}`], { replaceUrl: true });
      });
    }
  }

  seeListDetails(): void {
    this.dialog
      .open(ViewListDetailsModalComponent, {
        width: ConfirmationModalWidth,
        maxWidth: ConfirmationModalMaxWidth,
        autoFocus: true,
        viewContainerRef: this.viewContainerRef,
        data: {
          isEditing: this.isEditing(),
        },
      })
      .afterClosed()
      .pipe(switchMap((requestedEdit) => this.editListDetails(requestedEdit)))
      .subscribe();
  }

  saveList(): void {
    this.listBuilderService
      .saveListDefinition()
      .pipe(take(1))
      .subscribe({
        next: (savedStatus) => {
          if (savedStatus === ListSaveStatus.Created) {
            this.justSaved.set(true);
            this.changeRouteToEdit();
          }
        },
      });
  }

  canDeactivate(): Observable<boolean> {
    return this.listBuilderService.isEditing$;
  }

  updateStatus(status: string): void {
    this.listStatus.next(status);
  }

  setTotalResults(total: number): void {
    this.hasMany.set(total > 0);
  }
}
