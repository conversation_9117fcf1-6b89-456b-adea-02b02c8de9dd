import { inject, Injectable } from '@angular/core';
import { ListDefinitionsApiService, ListDefinitionInterface } from '@vendasta/bulk-actions';
import {
  BehaviorSubject,
  catchError,
  combineLatest,
  map,
  Observable,
  of,
  switchMap,
  take,
  tap,
  shareReplay,
  withLatestFrom,
} from 'rxjs';
import { CrmInjectionToken } from '@galaxy/crm/static';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { validateListFilterDefinition } from '../validators';
import { distinctUntilChanged } from 'rxjs/operators';
import { UnaryOperators } from '../constants';

export enum ListSaveStatus {
  Created = 'Created',
  Updated = 'Updated',
  Failed = 'Failed',
}

@Injectable()
export class CrmDynamicListBuilderService {
  private readonly listDefinitionsApiService = inject(ListDefinitionsApiService);
  private readonly config = inject(CrmInjectionToken);
  private readonly snackbar = inject(SnackbarService);
  private readonly currentListId$$ = new BehaviorSubject<string>('');

  private readonly namespace$ = this.config.namespace$;
  private readonly loading$$ = new BehaviorSubject<boolean>(true);
  private listDefinitionToSave$$ = new BehaviorSubject<ListDefinitionInterface>({});
  loading$ = this.loading$$.asObservable();
  currentList$ = combineLatest([this.config.namespace$, this.currentListId$$]).pipe(
    tap(() => this.loading$$.next(true)),
    switchMap(([namespace, listId]) => {
      if (!listId) {
        return of({
          listDefinitions: [{} as ListDefinitionInterface],
        });
      }
      return this.listDefinitionsApiService.getMultiListDefinition({
        namespace: namespace,
        listIds: [listId],
      });
    }),
    map((response) => response.listDefinitions[0]),
    shareReplay(1),
    tap(() => this.loading$$.next(false)),
  );
  listWithUnsavedChanges$ = combineLatest([this.listDefinitionToSave$$, this.currentList$]).pipe(
    map(([listToSave, currentList]) => {
      if (Object.keys(currentList).length !== 0) {
        return currentList;
      }
      return listToSave;
    }),
    distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b)),
    shareReplay(1),
  );
  isEditing$ = this.currentListId$$.pipe(map((listId) => !!listId));

  private createListDefinition(namespace: string, listDefinition: ListDefinitionInterface): Observable<ListSaveStatus> {
    return this.listDefinitionsApiService
      .createListDefinition({
        namespace: namespace,
        name: listDefinition.name,
        resourceType: listDefinition.resourceType,
        listType: listDefinition.listType,
        description: listDefinition.description,
        filter: listDefinition.filter,
      })
      .pipe(
        tap((response) => {
          this.currentListId$$.next(response.listId);
        }),
        map(() => ListSaveStatus.Created),
        catchError((err) => {
          console.error('Error creating list definition', err);
          return of(ListSaveStatus.Failed);
        }),
      );
  }

  private updateListDefinition(): Observable<ListSaveStatus> {
    // TODO: do when editing is allowed...
    return of(ListSaveStatus.Updated);
  }

  private showSnackMessage(status: ListSaveStatus): void {
    switch (status) {
      case ListSaveStatus.Created:
        this.snackbar.openSuccessSnack('DYNAMIC_LISTS.SUCCESS.CREATED');
        return;
      case ListSaveStatus.Updated:
        this.snackbar.openSuccessSnack('DYNAMIC_LISTS.SUCCESS.UPDATED');
        return;
      case ListSaveStatus.Failed:
      default:
        this.snackbar.openErrorSnack('DYNAMIC_LISTS.ERRORS.FAILED_TO_UPDATE');
    }
  }

  set editingListId(id: string) {
    this.currentListId$$.next(id);
  }
  get editingListId(): string {
    return this.currentListId$$.value;
  }

  cleanUpListDefinitionToSave(): void {
    this.listDefinitionToSave$$.next({});
  }

  changeListDefinitionToSave(listDefinition: ListDefinitionInterface): void {
    const currentListDefinitionToSave = this.listDefinitionToSave$$.value;
    this.listDefinitionToSave$$.next({
      ...currentListDefinitionToSave,
      ...listDefinition,
    });
  }

  private isValid(listDefinition: ListDefinitionInterface): boolean {
    try {
      validateListFilterDefinition(listDefinition);
      return true;
    } catch (e) {
      const msg = (e as Error).message;
      this.snackbar.openErrorSnack(msg);
      return false;
    }
  }

  saveListDefinition(): Observable<ListSaveStatus> {
    if (!this.isValid(this.listDefinitionToSave$$.value)) {
      return of(ListSaveStatus.Failed);
    }
    return this.listDefinitionToSave$$.pipe(
      take(1),
      map((listDefinitionToSave) => {
        const filters = listDefinitionToSave.filter;
        for (const filter of filters || []) {
          // dont store any value with unary operators in the backend
          if (filter.operator && UnaryOperators.includes(filter.operator)) {
            delete filter.values;
          }
        }
        return listDefinitionToSave;
      }),
      withLatestFrom(this.namespace$, this.currentListId$$),
      switchMap(([listDefinitionToSave, namespace, listId]) => {
        if (!listId) {
          return this.createListDefinition(namespace, listDefinitionToSave);
        }
        return this.updateListDefinition();
      }),
      tap((savedStatus) => {
        this.showSnackMessage(savedStatus);
        if (savedStatus === ListSaveStatus.Created || savedStatus === ListSaveStatus.Updated) {
          this.listDefinitionToSave$$.next({});
        }
      }),
      catchError((err) => {
        console.error('Failed updating dynamic list', err);
        return of(ListSaveStatus.Failed);
      }),
    );
  }
}
