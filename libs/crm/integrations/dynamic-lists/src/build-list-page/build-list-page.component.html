<glxy-page [pagePadding]="false">
  <glxy-page-toolbar class="mobile-toolbar">
    <glxy-page-nav>
      <glxy-page-nav-button [useHistory]="true"></glxy-page-nav-button>
    </glxy-page-nav>
    <glxy-page-title [@loadingBehavior]="isLoading() ? 'loading' : 'finished'">
      @if (!isLoading()) {
        @if (currentList().name) {
          {{ currentList().name }}
        } @else {
          <glxy-blank-value></glxy-blank-value>
        }
      }
    </glxy-page-title>
    <glxy-page-actions>
      <button mat-stroked-button (click)="seeListDetails()">
        {{ 'DYNAMIC_LISTS.ACTIONS.DETAILS' | translate }}
      </button>
      @if (!isEditing()) {
        <button mat-flat-button color="primary" (click)="saveList()">
          {{ 'ACTIONS.SAVE' | translate }}
        </button>
      }
    </glxy-page-actions>
  </glxy-page-toolbar>
  <glxy-page-below-toolbar class="mobile-below-toolbar">
    <glxy-page-extended-toolbar>
      <div class="extended-toolbar" [@loadingBehavior]="isLoading() ? 'loading' : 'finished'">
        <span class="estimated-label">{{ 'DYNAMIC_LISTS.ESTIMATED_LIST_SIZE' | translate }}:</span>
        <span class="estimated-total" @fadeIn>
          {{ estimatedTotalSize | translate }}
        </span>
        @if (currentList()?.listType) {
          @switch (currentList().listType) {
            @case (ListType.LIST_TYPE_STATIC) {
              @if (justSaved() && (listStatus | async) !== ListStatus.READY) {
                <glxy-badge [color]="'grey'">
                  {{ 'DYNAMIC_LISTS.PROCESSING' | translate }}
                </glxy-badge>
              } @else {
                <glxy-badge [color]="'green'">
                  {{ 'DYNAMIC_LISTS.LIST_TYPES.STATIC' | translate }}
                </glxy-badge>
              }
            }
            @case (ListType.LIST_TYPE_DYNAMIC) {
              <glxy-badge [color]="'green'">
                {{ 'DYNAMIC_LISTS.LIST_TYPES.SMART' | translate }}
              </glxy-badge>
            }
          }
        }
      </div>
    </glxy-page-extended-toolbar>
  </glxy-page-below-toolbar>
  <crm-three-panel-page
    [leftPanelTitle]="'DYNAMIC_LISTS.FILTERS' | translate"
    [centerPanelTitle]="'DYNAMIC_LISTS.PREVIEW' | translate"
    [showRightPanel]="false"
  >
    <crm-three-panel-page-left-panel>
      <crm-three-panel-page-content-header>
        <div class="header-container">
          <h3>{{ 'DYNAMIC_LISTS.FILTERS' | translate }}</h3>
          @if (isEditing()) {
            <glxy-alert type="tip" class="edit-alert">
              <strong>{{ 'DYNAMIC_LISTS.ACTIONS.CANNOT_EDIT' | translate }}</strong>
            </glxy-alert>
          } @else {
            <button mat-stroked-button (click)="addFilter()">
              <mat-icon>add</mat-icon>
              {{ 'DYNAMIC_LISTS.ACTIONS.ADD_FILTER' | translate }}
            </button>
          }
        </div>
      </crm-three-panel-page-content-header>
      <div class="filter-panel" [@loadingBehavior]="isLoading() ? 'loading' : 'finished'">
        <ng-container *ngTemplateOutlet="filterView"></ng-container>
      </div>
    </crm-three-panel-page-left-panel>
    <crm-three-panel-page-center-panel
      [class.empty-state]="!(currentList()?.resourceType && previewFilters()?.length > 0)"
    >
      @if (currentList()?.listType === ListType.LIST_TYPE_STATIC) {
        <ng-container *ngTemplateOutlet="staticEditItemsView"></ng-container>
      } @else {
        <ng-container *ngTemplateOutlet="preview"></ng-container>
      }
    </crm-three-panel-page-center-panel>
  </crm-three-panel-page>
</glxy-page>

<ng-template #filterView>
  @for (filter of filters(); track $index; let last = $last) {
    <mat-card @fadeIn class="filter-card" [ngClass]="{ disabled: isEditing() }">
      @if (filter) {
        <div class="form-items">
          <crm-custom-filter-field-id
            [objectType]="objectType()"
            [value]="filter.fieldId"
            (valueChange)="setFieldId($index, filter, $event)"
          ></crm-custom-filter-field-id>

          <crm-custom-filter-field-operator
            [objectType]="objectType()"
            [fieldId]="filter.fieldId"
            [value]="filter.operator"
            (valueChange)="setOperator($index, filter, $event)"
          ></crm-custom-filter-field-operator>

          <crm-custom-filter-field-value
            [objectType]="objectType()"
            [fieldId]="filter.fieldId"
            [operator]="filter.operator"
            [values]="filter.values"
            (valuesChange)="setValue($index, filter, $event)"
          ></crm-custom-filter-field-value>
        </div>
      }
      @if (!isEditing()) {
        <button
          mat-icon-button
          [attr.aria-label]="'DYNAMIC_LISTS.ACTIONS.DELETE_FILTER' | translate"
          (click)="removeFilter($index)"
        >
          <mat-icon>close</mat-icon>
        </button>
      }
    </mat-card>
    @if (!last) {
      <div class="divider" @fadeIn>{{ 'DYNAMIC_LISTS.MISC.AND' | translate }}</div>
    }
  }
</ng-template>

<ng-template #preview>
  @if (!isLoading()) {
    @if (currentList()?.resourceType && previewFilters()?.length > 0) {
      <crm-list-objects-table
        #tableRef
        [objectType]="currentList()?.resourceType"
        [forceFilters]="previewFilters()"
        [readOnlyMode]="true"
      >
      </crm-list-objects-table>
    } @else {
      <glxy-empty-state @fadeIn>
        <glxy-empty-state-hero>
          <img
            src="https://storage.googleapis.com/galaxy-libs-public-images/dynamic-lists/lists-empty-state.svg"
            [attr.alt]="'DYNAMIC_LISTS.EMPTY_STATE.EMPTY_PREVIEW' | translate"
          />
        </glxy-empty-state-hero>
        <glxy-empty-state-title>
          {{ 'DYNAMIC_LISTS.EMPTY_STATE.EMPTY_PREVIEW' | translate }}
        </glxy-empty-state-title>
        <p>
          {{ 'DYNAMIC_LISTS.EMPTY_STATE.EMPTY_PREVIEW_DESCRIPTION' | translate }}
        </p>
      </glxy-empty-state>
    }
  }
</ng-template>

<ng-template #staticEditItemsView>
  @if (!isLoading() && isEditing() && currentList()?.listType === ListType.LIST_TYPE_STATIC) {
    <crm-dynamic-list-items-table
      [listId]="currentList()?.listId"
      (statusChanged)="updateStatus($event)"
      (totalResultsChanged)="setTotalResults($event)"
    >
    </crm-dynamic-list-items-table>
  } @else {
    <ng-container *ngTemplateOutlet="preview"></ng-container>
  }
</ng-template>
