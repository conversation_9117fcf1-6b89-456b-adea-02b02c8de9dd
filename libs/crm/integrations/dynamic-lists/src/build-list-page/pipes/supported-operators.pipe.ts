import { Pipe, PipeTransform, inject } from '@angular/core';
import { Observable, map, of } from 'rxjs';
import { ObjectType } from '@galaxy/crm/static';
import { GalaxyFilterOperator } from '@vendasta/galaxy/filter/chips';
import { isAcceptedObjectType } from '../../validators';
import { DynamicListFilterService } from '../dynamic-list-filters.service';

@Pipe({
  name: 'supportedOperators',
  standalone: true,
})
export class SupportedOperatorsPipe implements PipeTransform {
  private readonly dynamicListFilterService = inject(DynamicListFilterService);

  transform(value?: string | null, objectType?: ObjectType): Observable<GalaxyFilterOperator[]> {
    if (!value) {
      return of([]);
    }
    if (!isAcceptedObjectType(objectType)) {
      return of([]);
    }
    return this.dynamicListFilterService.getObjectFilters$(objectType).pipe(
      map((filters) => filters?.find((f) => f.fieldId === value)),
      map((filter) => filter?.supportedOperators ?? []),
    );
  }
}
