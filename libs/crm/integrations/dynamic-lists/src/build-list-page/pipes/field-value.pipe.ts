import { Pipe, PipeTransform, inject } from '@angular/core';
import { FilterValueInterface } from '@vendasta/crm-integrations';
import { ObjectType } from '@galaxy/crm/static';
import { DateDefaultToLabel, RangeValueOperators } from '../../constants';
import { Observable, map, of } from 'rxjs';
import { FieldSchema, FieldType, FilterInterface } from '@vendasta/crm';
import { isAcceptedObjectType } from '../../validators';
import { DynamicListFilterService } from '../dynamic-list-filters.service';

@Pipe({
  name: 'fieldValue',
  pure: false,
  standalone: true,
})
export class FieldValuePipe implements PipeTransform {
  private readonly dynamicListFiltersService = inject(DynamicListFilterService);

  transform(filter?: FilterInterface, objectType?: ObjectType): Observable<string> {
    if (!filter) {
      return of('');
    }
    if (!filter.values?.length) {
      return of('');
    }
    if (!isAcceptedObjectType(objectType)) {
      return of('');
    }
    const schema = this.dynamicListFiltersService
      .getFieldSchema$(objectType)
      .pipe(map((fieldSchema) => fieldSchema?.find((fs) => fs.fieldId === filter.fieldId)));
    const separator = filter.operator ? (RangeValueOperators.includes(filter.operator) ? ' - ' : ', ') : ', ';
    return schema.pipe(map((fs) => filter.values?.map((value) => this.getValue(value, fs)).join(separator) ?? ''));
  }

  private getValue(value: FilterValueInterface, schema?: FieldSchema): string {
    if (!schema) {
      return '';
    }
    switch (schema.fieldType) {
      case FieldType.FIELD_TYPE_STRING:
      case FieldType.FIELD_TYPE_EMAIL:
      case FieldType.FIELD_TYPE_PHONE:
      case FieldType.FIELD_TYPE_GEOPOINT:
        return this.getStringValue(value);
      case FieldType.FIELD_TYPE_INTEGER:
        return this.getIntegerValue(value);
      case FieldType.FIELD_TYPE_DATE:
        return this.getDateValue(value);
      case FieldType.FIELD_TYPE_BOOLEAN:
        return this.getBooleanValue(value);
      case FieldType.FIELD_TYPE_FLOAT:
        return this.getFloatValue(value);
      case FieldType.FIELD_TYPE_DATETIME:
        return this.getDatetimeValue(value);
      default:
        return '';
    }
  }

  private getBooleanValue(value: FilterValueInterface): string {
    return value.boolean ? 'COMMON.YES' : 'COMMON.NO';
  }

  private getIntegerValue(value: FilterValueInterface): string {
    return value.integer?.toString() ?? '0';
  }

  private getFloatValue(value: FilterValueInterface): string {
    return value.float?.toString() ?? '0';
  }

  private getStringValue(value: FilterValueInterface): string {
    return value.string ?? '';
  }

  private getDateValue(value: FilterValueInterface): string {
    if (value.dateDefault) {
      return DateDefaultToLabel[value.dateDefault];
    }
    return value.date?.toLocaleDateString() ?? '';
  }

  private getDatetimeValue(value: FilterValueInterface): string {
    return value.date?.toLocaleString() ?? '';
  }
}
