import { Pipe, PipeTransform, inject } from '@angular/core';
import { Observable, combineLatest, map, of, switchMap } from 'rxjs';
import { ObjectType } from '@galaxy/crm/static';
import { isAcceptedObjectType } from '../../validators';
import { TranslateService } from '@ngx-translate/core';
import { DynamicListFilterService } from '../dynamic-list-filters.service';

@Pipe({
  name: 'fieldName',
  standalone: true,
})
export class FieldNamePipe implements PipeTransform {
  private readonly translateService = inject(TranslateService);
  private readonly dynamicListFilterService = inject(DynamicListFilterService);

  transform(filterId?: string, objectType?: ObjectType): Observable<string> {
    if (!filterId) {
      return of('');
    }
    if (!isAcceptedObjectType(objectType)) {
      return of('');
    }
    const filterOverride$ = this.dynamicListFilterService.getFilterOverride(filterId, objectType);
    const fieldName$ = this.dynamicListFilterService.getFieldSchema$(objectType).pipe(
      map((fieldSchema) => fieldSchema?.find((fs) => fs.fieldId === filterId)),
      map((fs) => fs?.fieldName ?? filterId),
    );
    return combineLatest([fieldName$, filterOverride$]).pipe(
      map(([fieldName, filterOverride]) => filterOverride?.title ?? fieldName),
      switchMap((fieldName) => this.translateService.stream(fieldName)),
    );
  }
}
