import { Pipe, PipeTransform, inject } from '@angular/core';
import { Observable, combineLatest, map, of } from 'rxjs';
import { UnaryOperators } from '../../constants';
import { ObjectType } from '@galaxy/crm/static';
import { FieldSchemaInterface, FieldType, FilterOperator } from '@vendasta/crm';
import { FilterInputType } from '../../interfaces';
import { isAcceptedObjectType } from '../../validators';
import { DynamicListFilterService } from '../dynamic-list-filters.service';

const SingleSelectorOperators: FilterOperator[] = [
  FilterOperator.FILTER_OPERATOR_IS,
  FilterOperator.FILTER_OPERATOR_IS_NOT,
  FilterOperator.FILTER_OPERATOR_CONTAINS,
  FilterOperator.FILTER_OPERATOR_DOES_NOT_CONTAIN,
];
const MultiSelectorOperators: FilterOperator[] = [
  FilterOperator.FILTER_OPERATOR_IS_ANY,
  FilterOperator.FILTER_OPERATOR_IS_NOT_ANY,
  FilterOperator.FILTER_OPERATOR_IS_ALL,
  FilterOperator.FILTER_OPERATOR_IS_NOT_ALL,
];

@Pipe({
  name: 'filterInputType',
  standalone: true,
})
export class FilterInputTypePipe implements PipeTransform {
  private readonly dynamicListFilterService = inject(DynamicListFilterService);

  transform(fieldId?: string, operator?: FilterOperator, objectType?: ObjectType): Observable<FilterInputType> {
    if (!fieldId || !isAcceptedObjectType(objectType)) {
      return of(FilterInputType.None);
    }
    const fieldSchema$ = this.dynamicListFilterService
      .getFieldSchema$(objectType)
      .pipe(map((fieldSchema) => fieldSchema?.find((fs) => fs.fieldId === fieldId)));
    const hasInputOverride$ = this.dynamicListFilterService
      .getFilterOverride(fieldId, objectType)
      .pipe(map((filterOverride) => !!filterOverride?.filterInput));
    return combineLatest([fieldSchema$, hasInputOverride$]).pipe(
      map(([fs, hasInputOverride]) => this.getInputType(fs, operator, hasInputOverride)),
    );
  }

  private getInputType(
    field?: FieldSchemaInterface,
    operator?: FilterOperator,
    hasInputOverride?: boolean,
  ): FilterInputType {
    if (!field || !operator) {
      return FilterInputType.None;
    }
    if (UnaryOperators.includes(operator)) {
      return FilterInputType.None;
    } else if (hasInputOverride) {
      return FilterInputType.Custom;
    } else if (field.fieldType === FieldType.FIELD_TYPE_DATE || field.fieldType === FieldType.FIELD_TYPE_DATETIME) {
      return FilterInputType.Date;
    } else if (field.fieldType === FieldType.FIELD_TYPE_FLOAT) {
      return FilterInputType.Float;
    } else if (field.fieldType === FieldType.FIELD_TYPE_INTEGER) {
      return FilterInputType.Integer;
    } else if (SingleSelectorOperators.includes(operator)) {
      switch (field.fieldType) {
        case FieldType.FIELD_TYPE_PHONE:
        case FieldType.FIELD_TYPE_EMAIL:
        case FieldType.FIELD_TYPE_STRING:
          return FilterInputType.String;
        case FieldType.FIELD_TYPE_TAG:
          return FilterInputType.Tag;
        case FieldType.FIELD_TYPE_BOOLEAN:
          return FilterInputType.Boolean;
      }
    } else if (MultiSelectorOperators.includes(operator)) {
      switch (field.fieldType) {
        case FieldType.FIELD_TYPE_TAG:
          return FilterInputType.MultiTag;
        case FieldType.FIELD_TYPE_PHONE:
        case FieldType.FIELD_TYPE_EMAIL:
        case FieldType.FIELD_TYPE_STRING:
          return FilterInputType.MultiString;
      }
    }
    return FilterInputType.None;
  }
}
