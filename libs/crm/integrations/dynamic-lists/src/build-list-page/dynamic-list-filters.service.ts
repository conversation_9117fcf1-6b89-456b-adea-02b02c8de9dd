import { Injectable, inject } from '@angular/core';
import {
  CRMFilterOverride,
  CrmFiltersService,
  CrmInjectionToken,
  CrmObjectDependencies,
  ObjectType,
} from '@galaxy/crm/static';
import { CRMFieldSchemaApiService, FieldSchema, PagedRequestOptionsInterface } from '@vendasta/crm';
import { GalaxyFilterDefinitionInterface } from '@vendasta/galaxy/filter/chips';
import { Observable, map, of, shareReplay, switchMap } from 'rxjs';

@Injectable()
export class DynamicListFilterService {
  private readonly config = inject(CrmInjectionToken);
  private readonly fieldSchemaService = inject(CRMFieldSchemaApiService);
  private readonly crmFiltersService = inject(CrmFiltersService);
  private readonly fieldSchemas = new Map<ObjectType, Observable<FieldSchema[]>>();
  private readonly filters = new Map<ObjectType, Observable<GalaxyFilterDefinitionInterface[]>>();

  getFilterOverride(fieldId: string, objectType: ObjectType): Observable<CRMFilterOverride | undefined> {
    const objectDependencies = this.objectDependencies(objectType);
    if (!objectDependencies?.filterInputOverrides$) {
      return of(undefined);
    }
    return objectDependencies.filterInputOverrides$.pipe(
      map((overrides) => overrides.find((override) => override.fieldId === fieldId)),
    );
  }

  getFieldSchema$(crmObjectType: ObjectType): Observable<FieldSchema[]> {
    if (this.fieldSchemas.has(crmObjectType)) {
      return this.fieldSchemas.get(crmObjectType)!;
    }

    const pagingOptions: PagedRequestOptionsInterface = { cursor: '', pageSize: 200 };
    const fs = this.config.namespace$.pipe(
      switchMap((namespace) => this.fieldSchemaService.listFieldSchema({ namespace, crmObjectType, pagingOptions })),
      map((response) => response?.fieldSchemas),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
    this.fieldSchemas.set(crmObjectType, fs);
    return fs;
  }

  getObjectFilters$(crmObjectType: ObjectType): Observable<GalaxyFilterDefinitionInterface[]> {
    if (this.filters.has(crmObjectType)) {
      return this.filters.get(crmObjectType)!;
    }

    const filters = this.crmFiltersService
      .listObjectFilters$('', crmObjectType)
      .pipe(shareReplay({ bufferSize: 1, refCount: true }));
    this.filters.set(crmObjectType, filters);
    return filters;
  }

  private objectDependencies(objectType: ObjectType): CrmObjectDependencies | undefined {
    switch (objectType) {
      case 'Company':
        return this.config.company;
      case 'Contact':
        return this.config.contact;
      default:
        return undefined;
    }
  }
}
