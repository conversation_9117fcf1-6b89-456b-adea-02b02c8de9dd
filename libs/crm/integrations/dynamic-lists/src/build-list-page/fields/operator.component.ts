import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { ObjectType } from '@galaxy/crm/static';
import { FilterOperator } from '@vendasta/crm';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { AsyncPipe } from '@angular/common';
import { OperatorNamePipe } from '../pipes/operator-name.pipe';
import { SupportedOperatorsPipe } from '../pipes/supported-operators.pipe';
import { MatOption } from '@angular/material/autocomplete';
import { TranslationModule } from '@galaxy/crm/static';
import { MatSelect } from '@angular/material/select';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'crm-custom-filter-field-operator',
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <glxy-form-field [showLabel]="false" bottomSpacing="none">
      <mat-select placeholder="Relation" [formControl]="formControl">
        @for (operator of fieldId | supportedOperators: objectType | async; track $index) {
          <mat-option [value]="operator">{{ operator | operatorName | translate }}</mat-option>
        }
      </mat-select>
    </glxy-form-field>
  `,
  imports: [
    GalaxyFormFieldModule,
    AsyncPipe,
    OperatorNamePipe,
    MatOption,
    ReactiveFormsModule,
    SupportedOperatorsPipe,
    TranslationModule,
    MatSelect,
  ],
})
export class FieldOperatorComponent {
  private _fieldId = '';
  @Input({ required: true }) set fieldId(fieldId: string) {
    this._fieldId = fieldId;
    if (!fieldId) {
      this.formControl.disable();
    } else {
      this.formControl.enable();
    }
  }
  get fieldId(): string {
    return this._fieldId;
  }
  @Input({ required: true }) objectType?: ObjectType;
  @Input()
  set value(value: FilterOperator | undefined) {
    if (this.formControl.value !== value) {
      this.formControl.setValue(value ?? FilterOperator.FILTER_OPERATOR_INVALID);
    }
  }
  @Output() valueChange = new EventEmitter<FilterOperator>();

  protected readonly formControl = new FormControl<FilterOperator>(
    { value: FilterOperator.FILTER_OPERATOR_INVALID, disabled: true },
    { nonNullable: true },
  );

  constructor() {
    this.formControl.valueChanges.pipe(takeUntilDestroyed()).subscribe((value) => {
      if (value !== FilterOperator.FILTER_OPERATOR_INVALID) {
        this.valueChange.emit(value);
      }
    });
  }
}
