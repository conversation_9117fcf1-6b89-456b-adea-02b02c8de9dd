import { Component, EventEmitter, Input, Output } from '@angular/core';
import {
  GalaxyBooleanFilterInputComponent,
  GalaxyCustomFilterInputComponent,
  GalaxyDateFilterInputComponent,
  GalaxyFilterOperator,
  GalaxyFilterValueInterface,
  GalaxyFloatFilterInputComponent,
  GalaxyIntegerFilterInputComponent,
  GalaxyStringFilterInputComponent,
  GalaxyTagsFilterInputComponent,
} from '@vendasta/galaxy/filter/chips';
import { FilterOperator, FilterValueInterface } from '@vendasta/crm';
import { FilterInputType } from '../../interfaces';
import {
  ObjectType,
  convertFromGalaxyFilterValues,
  convertToGalaxyFilterOperator,
  convertToGalaxyFilterValues,
} from '@galaxy/crm/static';
import { AsyncPipe } from '@angular/common';
import { FilterInputTypePipe } from '../pipes/filter-input-type.pipe';
import { FilterOverridePipe } from '../pipes/filter-override.pipe';

@Component({
  selector: 'crm-custom-filter-field-value',
  template: `
    @switch (fieldId | filterInputType: operator : objectType | async) {
      @case (FilterInputType.String) {
        <glxy-string-filter-input
          [initialValue]="initialValue"
          [fieldId]="fieldId"
          (valueChanges)="handleValueChanges($event)"
        ></glxy-string-filter-input>
      }
      @case (FilterInputType.MultiString) {
        <glxy-tags-filter-input
          [initialValue]="initialValue"
          [tagsFieldId]="fieldId"
          (valueChanges)="handleValueChanges($event)"
        ></glxy-tags-filter-input>
      }
      @case (FilterInputType.Tag) {
        <glxy-tags-filter-input
          [initialValue]="initialValue"
          (valueChanges)="handleValueChanges($event)"
          [tagsFieldId]="fieldId"
          [maxTags]="1"
        ></glxy-tags-filter-input>
      }
      @case (FilterInputType.MultiTag) {
        <glxy-tags-filter-input
          [initialValue]="initialValue"
          (valueChanges)="handleValueChanges($event)"
          [tagsFieldId]="fieldId"
        ></glxy-tags-filter-input>
      }
      @case (FilterInputType.Date) {
        <glxy-date-filter-input
          [initialValue]="initialValue"
          [operator]="glxyOperator"
          (valueChanges)="handleValueChanges($event)"
        ></glxy-date-filter-input>
      }
      @case (FilterInputType.Float) {
        <glxy-float-filter-input
          [initialValue]="initialValue"
          [operator]="glxyOperator"
          (valueChanges)="handleValueChanges($event)"
        ></glxy-float-filter-input>
      }
      @case (FilterInputType.Integer) {
        <glxy-integer-filter-input
          [initialValue]="initialValue"
          [operator]="glxyOperator"
          (valueChanges)="handleValueChanges($event)"
        ></glxy-integer-filter-input>
      }
      @case (FilterInputType.Boolean) {
        <glxy-boolean-filter-input
          [initialValue]="initialValue"
          (valueChanges)="handleValueChanges($event)"
        ></glxy-boolean-filter-input>
      }
      @case (FilterInputType.Custom) {
        @if (fieldId | filterOverride: objectType | async; as filterOverride) {
          <glxy-custom-filter-input
            [initialValue]="initialValue"
            [filterOverride]="filterOverride"
            [operator]="glxyOperator"
            (valueChanges)="handleValueChanges($event)"
          ></glxy-custom-filter-input>
        }
      }
    }
  `,
  imports: [
    GalaxyStringFilterInputComponent,
    GalaxyTagsFilterInputComponent,
    GalaxyDateFilterInputComponent,
    GalaxyIntegerFilterInputComponent,
    GalaxyBooleanFilterInputComponent,
    GalaxyCustomFilterInputComponent,
    AsyncPipe,
    FilterInputTypePipe,
    GalaxyFloatFilterInputComponent,
    FilterOverridePipe,
  ],
})
export class FieldValueComponent {
  private _fieldId?: string;
  @Input({ required: true })
  set fieldId(value: string | undefined) {
    this._fieldId = value;
  }
  get fieldId(): string {
    return this._fieldId ?? '';
  }

  private _operator?: FilterOperator;
  @Input({ required: true })
  set operator(value: FilterOperator | undefined) {
    this._operator = value;
    this.glxyOperator = convertToGalaxyFilterOperator(this._operator ?? FilterOperator.FILTER_OPERATOR_INVALID);
  }
  get operator(): FilterOperator | undefined {
    return this._operator;
  }

  @Input({ required: true }) objectType?: ObjectType;

  @Input()
  set values(values: FilterValueInterface[] | undefined) {
    this.initialValue = values ? convertToGalaxyFilterValues(values) : [];
  }
  @Output() valuesChange = new EventEmitter<FilterValueInterface[]>();

  protected readonly FilterInputType = FilterInputType;
  protected glxyOperator: GalaxyFilterOperator = GalaxyFilterOperator.FILTER_OPERATOR_INVALID;
  protected initialValue: GalaxyFilterValueInterface[] = [];

  handleValueChanges(value?: GalaxyFilterValueInterface[]): void {
    this.valuesChange.emit(value ? convertFromGalaxyFilterValues(value) : undefined);
  }
}
