import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  Output,
  ViewChild,
  inject,
  input,
} from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { GalaxyFilterDefinitionInterface } from '@vendasta/galaxy/filter/chips';
import { BehaviorSubject, combineLatest, map, switchMap } from 'rxjs';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { ObjectType, TranslationModule } from '@galaxy/crm/static';
import { AsyncPipe } from '@angular/common';
import { MatAutocomplete, MatAutocompleteTrigger, MatOption } from '@angular/material/autocomplete';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { DynamicListFilterService } from '../dynamic-list-filters.service';
import { MatInput } from '@angular/material/input';

@Component({
  selector: 'crm-custom-filter-field-id',
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <glxy-form-field [showLabel]="false" bottomSpacing="none">
      <input
        #input
        type="text"
        [placeholder]="'DYNAMIC_LISTS.FILTERS_EDITOR.SELECT_FIELD' | translate"
        matInput
        [formControl]="formControl"
        [matAutocomplete]="auto"
        (input)="handleInput($event)"
        (focus)="handleFocus($event)"
      />
      <mat-autocomplete requireSelection="true" #auto="matAutocomplete" [displayWith]="displayFn">
        @for (option of options$ | async; track $index) {
          <mat-option [value]="option">{{ option.fieldName || option.fieldId }}</mat-option>
        }
      </mat-autocomplete>
    </glxy-form-field>
  `,
  imports: [
    AsyncPipe,
    MatAutocompleteTrigger,
    ReactiveFormsModule,
    GalaxyFormFieldModule,
    MatAutocomplete,
    MatOption,
    MatInput,
    TranslationModule,
  ],
})
export class FieldIdComponent {
  objectType = input.required<ObjectType>();
  @Input()
  set value(value: string | undefined) {
    this.initialValue$$.next(value ?? '');
  }
  @Output() valueChange = new EventEmitter<string>();

  @ViewChild('input') input?: ElementRef<HTMLInputElement>;

  private readonly dynamicListFiltersService = inject(DynamicListFilterService);
  private readonly searchTerm$$ = new BehaviorSubject<string>('');
  private readonly initialValue$$ = new BehaviorSubject<string>('');
  private supportedFilters$ = toObservable(this.objectType).pipe(
    switchMap((objectType) => this.dynamicListFiltersService.getObjectFilters$(objectType)),
  );

  protected readonly formControl = new FormControl<GalaxyFilterDefinitionInterface | undefined>(undefined);
  protected options$ = combineLatest([this.supportedFilters$, this.searchTerm$$]).pipe(
    map(([options, searchTerm]) =>
      options.filter((option) => option.fieldName?.toLowerCase().includes(searchTerm.toLowerCase())),
    ),
  );

  constructor() {
    combineLatest([this.initialValue$$, this.supportedFilters$])
      .pipe(takeUntilDestroyed())
      .subscribe(([value, options]) => this.formControl.setValue(options.find((option) => option.fieldId === value)));
    this.formControl.valueChanges
      .pipe(takeUntilDestroyed())
      .subscribe((value) => this.valueChange.emit(value?.fieldId));
  }

  handleInput(event: Event): void {
    this.applyFilter((event.target as HTMLInputElement).value);
  }

  handleFocus(event: FocusEvent): void {
    this.applyFilter((event.target as HTMLInputElement).value);
  }

  displayFn(field?: GalaxyFilterDefinitionInterface): string {
    return field?.fieldName || field?.fieldId || '';
  }

  private applyFilter(searchTerm?: string): void {
    this.searchTerm$$.next(searchTerm ?? '');
  }
}
