import { UnaryOperators } from './constants';
import { ObjectType } from '@galaxy/crm/static';
import {
  EmptyListFiltersError,
  MissingFieldError,
  MissingOperator<PERSON>rror,
  MissingValueError,
  MissingNameError,
  InvalidResourceTypeError,
  InvalidListTypeError,
} from './errors';
import { ListDefinitionInterface } from '@vendasta/bulk-actions';
import { FilterInterface } from '@vendasta/bulk-actions/lib/_internal/interfaces/galaxy-filters.interface';

export function validateListFilterDefinition(listDefinition: ListDefinitionInterface): void {
  if (!listDefinition.name) {
    throw new MissingNameError();
  }
  if (!listDefinition.listType) {
    throw new InvalidListTypeError();
  }
  if (!isAcceptedObjectType(listDefinition?.resourceType)) {
    throw new InvalidResourceTypeError();
  }
  if (listDefinition.listType === 2 && (!listDefinition.filter || !listDefinition.filter.length)) {
    throw new EmptyListFiltersError();
  }
  if (listDefinition.filter) {
    for (const filter of listDefinition.filter) {
      validateFilter(filter);
    }
  }
}

export function validateFilter(filter: FilterInterface): void {
  if (!filter?.fieldId) {
    throw new MissingFieldError();
  }
  if (!filter?.operator) {
    throw new MissingOperatorError();
  }
  if (!UnaryOperators.includes(filter.operator) && !filter.values?.length) {
    throw new MissingValueError();
  }
}

export function isAcceptedObjectType(value: any): value is ObjectType {
  return value === 'Company' || value === 'Contact';
}
