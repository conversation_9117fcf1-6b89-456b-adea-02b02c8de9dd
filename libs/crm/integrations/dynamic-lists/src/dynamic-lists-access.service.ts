import { inject, Injectable } from '@angular/core';
import { CrmInjectionToken } from '@galaxy/crm/static';
import { Observable, of } from 'rxjs';

@Injectable()
export class CrmDynamicListsAccessServiceWrapper {
  private readonly config = inject(CrmInjectionToken);

  private hasCompanyPermissions$ =
    this.config.services?.accessService?.canAccessCrmObjectType$?.('Company') || of(false);
  private hasContactPermissions$ =
    this.config.services?.accessService?.canAccessCrmObjectType$?.('Contact') || of(false);

  hasCompanyPermissions(): Observable<boolean> {
    return this.hasCompanyPermissions$;
  }

  hasContactPermissions(): Observable<boolean> {
    return this.hasContactPermissions$;
  }
}
