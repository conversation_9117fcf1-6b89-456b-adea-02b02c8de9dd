<glxy-table-container
  @fadeIn
  [dataSource]="dataSource"
  [columns]="columns$ | async"
  [showSelection]="false"
  [pageSizeOptions]="[25, 50, 100]"
  [pageSize]="25"
  [border]="false"
  [fullWidth]="true"
>
  <!-- Advanced sorting is not yet implemented, turn off for now -->
  <glxy-table-content-header
    [showColumnArrange]="true"
    [columnArrangeType]="'simple'"
    [showFilters]="true"
    [showFiltersOpen]="true"
    [showFiltersApplied]="(dataSource.filtersApplied$ | async) ?? false"
    [showSearch]="false"
    [showSort]="false"
  >
    <div filters-area class="filters-section">
      <glxy-filter-chips (filtersChanged)="onFilterChanged($event)"></glxy-filter-chips>
    </div>
  </glxy-table-content-header>

  <table mat-table [@loadingBehavior]="(dataSource.loading$ | async) ? 'loading' : 'finished'">
    <ng-container matColumnDef="listId">
      <th mat-header-cell *matHeaderCellDef>{{ 'DYNAMIC_LISTS.COLUMNS.ID' | translate }}</th>
      <td mat-cell *matCellDef="let element">
        <a routerLink="../edit/{{ element.listId }}">{{ element.listId }}</a>
      </td>
    </ng-container>

    <ng-container matColumnDef="name">
      <th mat-header-cell *matHeaderCellDef>{{ 'DYNAMIC_LISTS.COLUMNS.NAME' | translate }}</th>
      <td mat-cell *matCellDef="let element">
        <a routerLink="../edit/{{ element.listId }}">{{ element.name }}</a>
      </td>
    </ng-container>

    <ng-container matColumnDef="description">
      <th mat-header-cell *matHeaderCellDef>{{ 'DYNAMIC_LISTS.COLUMNS.DESCRIPTION' | translate }}</th>
      <td mat-cell *matCellDef="let element">{{ element.description }}</td>
    </ng-container>

    <ng-container matColumnDef="resourceType">
      <th mat-header-cell *matHeaderCellDef>{{ 'DYNAMIC_LISTS.COLUMNS.RESOURCE_TYPE' | translate }}</th>
      <td mat-cell *matCellDef="let element">{{ element.resourceType }}</td>
    </ng-container>

    <ng-container matColumnDef="listType">
      <th mat-header-cell *matHeaderCellDef>{{ 'DYNAMIC_LISTS.COLUMNS.LIST_TYPE' | translate }}</th>
      <td mat-cell *matCellDef="let element">
        @if (element.listType === ListType.LIST_TYPE_DYNAMIC) {
          {{ 'DYNAMIC_LISTS.LIST_TYPES.SMART' | translate }}
        } @else {
          {{ 'DYNAMIC_LISTS.LIST_TYPES.STATIC' | translate }}
        }
      </td>
    </ng-container>

    <ng-container matColumnDef="status">
      <th mat-header-cell *matHeaderCellDef>{{ 'DYNAMIC_LISTS.COLUMNS.STATUS' | translate }}</th>
      <td mat-cell *matCellDef="let element">{{ element.status }}</td>
    </ng-container>

    <ng-container matColumnDef="created">
      <th mat-header-cell *matHeaderCellDef>{{ 'DYNAMIC_LISTS.COLUMNS.CREATED' | translate }}</th>
      <td mat-cell *matCellDef="let element">{{ element.created | glxyDate }}</td>
    </ng-container>

    <ng-container matColumnDef="updated">
      <th mat-header-cell *matHeaderCellDef>{{ 'DYNAMIC_LISTS.COLUMNS.UPDATED' | translate }}</th>
      <td mat-cell *matCellDef="let element">{{ element.updated | glxyDate }}</td>
    </ng-container>

    <ng-container matColumnDef="actions">
      <th mat-header-cell *matHeaderCellDef></th>
      <td mat-cell *matCellDef="let row">
        <button mat-icon-button [matMenuTriggerFor]="menu">
          <mat-icon>more_vert</mat-icon>
        </button>
        <mat-menu #menu="matMenu">
          @if (showStartAutomationButton$ | async) {
            <button mat-menu-item (click)="startAutomation(row, row.resourceType)">
              <span>{{ 'DYNAMIC_LISTS.ACTIONS.START_AUTOMATION' | translate }}</span>
            </button>
          }
          <button mat-menu-item (click)="delete(row.listId)">
            <span>{{ 'DYNAMIC_LISTS.ACTIONS.DELETE' | translate }}</span>
          </button>
        </mat-menu>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="[]"></tr>
    <tr mat-row *matRowDef="let row; columns: []"></tr>
  </table>
</glxy-table-container>
