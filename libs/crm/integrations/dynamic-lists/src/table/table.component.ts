import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { CrmInjectionToken } from '@galaxy/crm/static';
import { GalaxyFilterChipsComponent } from '@vendasta/galaxy/filter/chips/src/galaxy-filter-chips.component';
import { GalaxyColumnDef, GalaxyDataSource, GalaxyTableModule } from '@vendasta/galaxy/table';
import {
  Mat<PERSON>ell,
  MatCellDef,
  MatColumnDef,
  MatHeaderCell,
  MatHeaderCellDef,
  MatHeaderRow,
  MatHeaderRowDef,
  MatRow,
  MatRowDef,
  MatTable,
} from '@angular/material/table';
import { MatIcon } from '@angular/material/icon';
import { MatIconButton } from '@angular/material/button';
import { <PERSON><PERSON><PERSON>u, MatMenuItem, MatMenuTrigger } from '@angular/material/menu';

import { BehaviorSubject, map, Observable, of, switchMap, take } from 'rxjs';
import { ListDefinitionInterface } from '@vendasta/bulk-actions/lib/_internal/interfaces/list-definitions.interface';
import { CrmDynamicListsTableService } from './table.service';
import { GalaxyFilterChipInjectionToken, GalaxyFilterInterface } from '@vendasta/galaxy/filter/chips';

import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { ListType } from '@vendasta/bulk-actions';

import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { RouterLink } from '@angular/router';
import { FadeIn, LoadingBehavior } from '@galaxy/crm/components/animations';
import { AutomationActionsService } from '@galaxy/crm/integrations/automation';
import { Row } from '@vendasta/galaxy/table';

@Component({
  selector: 'crm-dynamic-lists-table',
  templateUrl: './table.component.html',
  styleUrls: ['./table.component.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    GalaxyPageModule,
    GalaxyEmptyStateModule,
    GalaxyFilterChipsComponent,
    GalaxyTableModule,
    MatCell,
    MatCellDef,
    MatColumnDef,
    MatHeaderCell,
    MatHeaderRow,
    MatHeaderRowDef,
    MatIcon,
    MatIconButton,
    MatMenu,
    MatMenuItem,
    MatRow,
    MatRowDef,
    MatTable,
    MatMenuTrigger,
    MatHeaderCellDef,
    GalaxyPipesModule,
    RouterLink,
  ],
  animations: [LoadingBehavior, FadeIn],
  providers: [
    CrmDynamicListsTableService,
    {
      provide: GalaxyFilterChipInjectionToken,
      useExisting: CrmDynamicListsTableService,
    },
  ],
})
export class CrmDynamicListsTableComponent implements OnInit {
  protected readonly ListType = ListType;

  dataSource: GalaxyDataSource<ListDefinitionInterface, GalaxyFilterInterface, void> = {} as GalaxyDataSource<
    ListDefinitionInterface,
    GalaxyFilterInterface,
    void
  >;
  columns$?: Observable<GalaxyColumnDef[]>;
  columnMode$$ = new BehaviorSubject<'simple' | 'advanced'>('simple');

  tableService = inject(CrmDynamicListsTableService);
  deleteModal = inject(OpenConfirmationModalService);
  translateService = inject(TranslateService);
  snackbarService = inject(SnackbarService);
  automationService = inject(AutomationActionsService);
  private readonly config = inject(CrmInjectionToken);

  showStartAutomationButton$ = this.config.lists?.canStartManualAutomation$ ?? of(false);

  ngOnInit(): void {
    this.dataSource = this.tableService.getDataSource();
    this.columns$ = this.tableService.getColumns$().pipe(map((cols) => [...cols]));
  }

  changeMode() {
    if (this.columnMode$$.value === 'simple') {
      this.columnMode$$.next('advanced');
    } else {
      this.columnMode$$.next('simple');
    }
  }

  onFilterChanged(filters: GalaxyFilterInterface[]): void {
    this.tableService.updateFilters(filters);
  }

  delete(rowId: string): void {
    this.deleteModal
      .openModal({
        type: 'warn',
        title: this.translateService.instant('DYNAMIC_LISTS.ACTIONS.DELETE_CONFIRMATION.TITLE'),
        message: this.translateService.instant('DYNAMIC_LISTS.ACTIONS.DELETE_CONFIRMATION.MESSAGE'),
        hideCancel: false,
        confirmButtonText: this.translateService.instant('DYNAMIC_LISTS.ACTIONS.DELETE_CONFIRMATION.CONFIRM'),
        cancelButtonText: this.translateService.instant('DYNAMIC_LISTS.ACTIONS.DELETE_CONFIRMATION.CANCEL'),
        actionOnEnterKey: true,
        cancelOnEscapeKeyOrBackgroundClick: true,
      })
      .pipe(
        take(1),
        switchMap((userDidAction: boolean) => {
          if (userDidAction) {
            return this.tableService.delete(rowId);
          }
          return of(null);
        }),
      )
      .subscribe({
        next: (resp) => {
          if (resp) {
            this.snackbarService.openSuccessSnack(
              this.translateService.instant('DYNAMIC_LISTS.ACTIONS.DELETE_CONFIRMATION.SUCCESS'),
            );
          }
        },
        error: (error) => {
          console.error(error);
          this.snackbarService.openErrorSnack(
            this.translateService.instant('DYNAMIC_LISTS.ACTIONS.DELETE_CONFIRMATION.ERROR'),
          );
        },
      });
  }

  startAutomation(row: ListDefinitionInterface, objectType: string) {
    const rowValue = [{ id: row.listId } as Row];
    this.config.namespace$.pipe(take(1)).subscribe((namespace) => {
      this.automationService.openStartManualAutomationDialog(namespace, objectType, rowValue, false, true);
    });
  }
}
