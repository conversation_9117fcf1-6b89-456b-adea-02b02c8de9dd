import { inject, ModuleWithProviders, NgModule, Provider } from '@angular/core';
import {
  ActivityAssigneeService,
  ActivityObjectService,
  ActivityService,
  AutocompleteService,
  CompanyBatchGetterService,
  CompanyFiltersService,
  CompanyProfileCardsPanelService,
  CompanyService,
  CompanyTableCustomizationService,
  ContactService,
  ContactTableCustomizationService,
  CrmAssociationService,
  CrmFieldOptionsService,
  CrmFieldService,
  CrmFiltersService,
  CrmFormService,
  CrmInjectionToken,
  CrmMultilocationInjectionToken,
  CrmObjectService,
  CrmTableStateService,
  CRMTrackingService,
  DefaultAssociationActionsService,
  ListObjectsTableService,
  OpportunityTableCustomizationService,
  PAGE_ROUTES,
  RendererSelectorService,
  RestrictedGroupService,
  TaskTableCustomizationServiceToken,
  TranslateByObjectTypeService,
  TranslateForCrmObjectService,
  TranslationModule,
  UpdaterService,
  ValidatorFactoryService,
  ValueSummaryService,
} from '@galaxy/crm/static';
import { BulkImportStepperService } from './multi-object-bulk-import/bulk-import-stepper.service';
import { FieldMappingService } from './multi-object-bulk-import/field-mapping-step/field-mapping.service';
import { CsvParserService } from './multi-object-bulk-import/file-upload-step/csv-parser.service';
import { GoogleMapsModule } from '@angular/google-maps';

import { CompanyAdminNotesCardComponent } from './crm-components/profile/profile-cards-panel/company-to-contact-associations/company-admin-notes-associations-card/company-admin-notes-card.component';
import { CompanyUtmAttributesCardComponent } from './crm-components/profile/profile-cards-panel/company-to-contact-associations/company-UTM-attributes-associations-card/company-utm-attributes-card.component';
import { CampaignI18nModule as CampaignTranslationModule } from '@galaxy/campaign';
import { TaskTableCustomizationService } from './activity/task/services/task-table-customization.service';
import { TaskQueueService } from './crm-components/task-queue/task-queue.service';
import { FeatureFlagGuard } from './feature-flag-guard.service';
import { Routes } from '@angular/router';
import { CRMExportService, CRMOpportunityApiService } from '@vendasta/crm';
import { CacheFactory, CacheStrategy } from '@galaxy/crm/components/cache';
import { MEETING_ANALYSIS_NAMESPACE_INJECTION_TOKEN$ } from '@galaxy/meeting-analysis/static';
import { PermissionsGuard } from './permissions-guard.service';

export { CompanyAdminNotesCardComponent };

export { CompanyUtmAttributesCardComponent };

export const CRM_ROUTES: Routes = [
  {
    path: '',
    redirectTo: `${PAGE_ROUTES.CONTACT.ROOT}/${PAGE_ROUTES.CONTACT.SUBROUTES.LIST}`,
    pathMatch: 'full',
  },
  {
    path: PAGE_ROUTES.CONTACT.ROOT,
    loadChildren: () => import('./contact/contact.module').then((m) => m.CrmContactModule),
    canActivate: [PermissionsGuard],
    data: { objectType: 'contacts', pageId: 'crm' }, // pageId is the Atlas sidebar navigation link ID used in the business app
  },
  {
    path: PAGE_ROUTES.COMPANY.ROOT,
    loadChildren: () => import('./company/company.module').then((m) => m.CrmCompanyModule),
    canActivate: [PermissionsGuard],
    data: { objectType: 'companies', pageId: 'nav-crm-companies' }, // pageId is the Atlas sidebar navigation link ID used in the business app
  },
  {
    path: PAGE_ROUTES.OPPORTUNITY.ROOT,
    loadChildren: () => import('./opportunity/opportunity.module').then((m) => m.CrmOpportunityModule),
    canActivate: [PermissionsGuard],
    data: { objectType: 'opportunities', pageId: 'nav-crm-opportunities' }, // pageId is the Atlas sidebar navigation link ID used in the business app
  },
  {
    path: PAGE_ROUTES.TASK.ROOT,
    loadChildren: () => import('./activity/task/task.module').then((m) => m.CrmTaskModule),
    canActivate: [PermissionsGuard],
    data: { objectType: 'activities' },
  },
  {
    path: PAGE_ROUTES.ACTIVITIES.ROOT,
    loadChildren: () => import('./activity/feed/activities.module').then((m) => m.CrmActivityFeedModule),
    canActivate: [PermissionsGuard],
    data: { objectType: 'activities' },
  },
  {
    path: PAGE_ROUTES.PIPELINE_SETTINGS.ROOT,
    loadChildren: () => import('./pipeline-settings/pipeline-settings.module').then((m) => m.PipelineSettingsModule),
    canActivate: [PermissionsGuard],
    data: { objectType: 'opportunities' },
  },
  {
    path: PAGE_ROUTES.CUSTOM_OBJECT.ROOT,
    loadChildren: () => import('./custom-object/custom-object.module').then((m) => m.CrmCustomObjectModule),
    canActivate: [FeatureFlagGuard],
    data: { featureFlag: 'crm_custom_objects' },
  },
  {
    path: PAGE_ROUTES.PERMISSION_REQUIRED,
    loadComponent: () =>
      import('./crm-components/permission-required/permission-required.component').then(
        (m) => m.PermissionRequiredComponent,
      ),
  },
  {
    path: PAGE_ROUTES.BULK_IMPORT.ROOT,
    loadComponent: () => import('./bulk-import-page.component').then((m) => m.CrmBulkImportComponent),
  },
  {
    path: '**',
    loadComponent: () => import('./page-not-found.component').then((m) => m.CrmPageNotFoundComponent),
  },
];

export interface CrmModuleOptions {
  cache?: boolean;
}

@NgModule({
  imports: [TranslationModule, GoogleMapsModule, CampaignTranslationModule],
  providers: [
    BulkImportStepperService,
    FieldMappingService,
    CsvParserService,
    CrmFieldOptionsService,
    CrmFiltersService,
    CrmFormService,
    ListObjectsTableService,
    ValueSummaryService,
    ActivityObjectService,
    ContactService,
    CompanyService,
    CrmObjectService,
    CompanyBatchGetterService,
    CrmAssociationService,
    CRMTrackingService,
    CrmFieldService,
    ActivityService,
    CompanyProfileCardsPanelService,
    TranslateByObjectTypeService,
    TranslateForCrmObjectService,
    RendererSelectorService,
    AutocompleteService,
    UpdaterService,
    ValidatorFactoryService,
    RestrictedGroupService,
    ContactTableCustomizationService,
    CompanyTableCustomizationService,
    OpportunityTableCustomizationService,
    CrmTableStateService,
    DefaultAssociationActionsService,
    CompanyFiltersService,
    ActivityAssigneeService,
    PermissionsGuard,
    FeatureFlagGuard,
    {
      provide: TaskTableCustomizationServiceToken,
      useClass: TaskTableCustomizationService,
    },
    {
      provide: MEETING_ANALYSIS_NAMESPACE_INJECTION_TOKEN$,
      useFactory: () => inject(CrmInjectionToken).namespace$,
      deps: [CrmInjectionToken],
    },
    {
      provide: CrmMultilocationInjectionToken,
      useFactory: () => inject(CrmMultilocationInjectionToken),
      deps: [CrmInjectionToken],
    },
    TaskQueueService,
    CRMExportService,
  ],
})
export class CrmModule {
  static withOptions(options: CrmModuleOptions): ModuleWithProviders<CrmModule> {
    const providers: Provider[] = [];
    if (options.cache) {
      providers.push(...cacheProviders());
    }
    return { ngModule: CrmModule, providers };
  }
}

function cacheProviders(): Provider[] {
  const cacheStrategy: CacheStrategy = { maxAge: 60000, observable: { share: true }, autoCleanup: true };
  return [
    {
      provide: CRMOpportunityApiService,
      useClass: CacheFactory.provide(CRMOpportunityApiService, {
        methods: [{ method: 'listOpportunitiesFilters', strategy: cacheStrategy }],
      }),
    },
  ];
}
