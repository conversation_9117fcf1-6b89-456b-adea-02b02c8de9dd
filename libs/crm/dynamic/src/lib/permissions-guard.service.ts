import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, UrlTree } from '@angular/router';
import { IAMService, UserIdentifier } from '@vendasta/iamv2';
import { PermissionsApiService } from '@vendasta/platform-users';
import { Observable, combineLatest, map, of, switchMap } from 'rxjs';
import { catchError } from 'rxjs/operators';
import {
  CrmInjectionToken,
  CrmMultilocationInjectionToken,
  CrmMultiLocationDependencies,
  MultiLocationContext,
  PAGE_ROUTES,
} from '@galaxy/crm/static';

@Injectable()
export class PermissionsGuard {
  private readonly config = inject(CrmInjectionToken);
  private readonly multiLocationConfig: CrmMultiLocationDependencies = inject(CrmMultilocationInjectionToken);
  private readonly iamService = inject(IAMService);
  private readonly permissionsApiService = inject(PermissionsApiService);
  private readonly router = inject(Router);

  private checkPermissions(
    objectType: string,
    partnerId: string,
    userId: string,
    accountGroupId: string,
    routePrefix: string,
  ): Observable<boolean | UrlTree> {
    return this.permissionsApiService.getPermissions({ partnerId, userId }).pipe(
      map((resp) => {
        let hasAccess = false;
        const accountGroupDefined = accountGroupId != undefined && accountGroupId !== '';
        const accountGroupPermission = resp.permissions?.namespace?.some((item) => item.namespaceId === accountGroupId);
        const partnerPermission = resp.permissions?.namespace?.some((item) => item.namespaceId === partnerId);
        if (partnerPermission || (accountGroupDefined && accountGroupPermission)) {
          hasAccess = objectType in resp.permissions;
        }
        return hasAccess ? true : this.router.createUrlTree([`${routePrefix}/${PAGE_ROUTES.PERMISSION_REQUIRED}`]);
      }),
      catchError(() => {
        return of(this.router.createUrlTree([`${routePrefix}/${PAGE_ROUTES.PERMISSION_REQUIRED}`]));
      }),
    );
  }

  canActivate(route: ActivatedRouteSnapshot): Observable<boolean | UrlTree> {
    const objectType = route.data['objectType'];
    const currentUser$ = this.config.currentUserId$.pipe(
      switchMap((userId) => {
        const uniqueUserIdentifiers = [new UserIdentifier({ userId: userId })];
        return this.iamService.getMultiUsers(uniqueUserIdentifiers);
      }),
      map((response) => response && response[0]),
      catchError(() => {
        return of(null);
      }),
    );

    const multiLocationContext$ = this.multiLocationConfig.multiLocationContext$ ?? of({} as MultiLocationContext);

    return combineLatest([
      currentUser$,
      this.config.currentUserId$,
      this.config.namespace$,
      this.config.parentNamespace$,
      multiLocationContext$,
    ]).pipe(
      switchMap(([user, userId, namespace, parentNamespace, context]) => {
        const roles = user?.roles || [];
        const hasDeveloperRole = 'developer' in roles;
        const isSuperAdmin = roles['partner']?.attributes?.attributes['is_super_admin']?.boolAttribute === true;

        if (hasDeveloperRole || isSuperAdmin) {
          return of(true);
        }

        if (context.groupId && context.accountGroupsIds && context.accountGroupsIds.length > 0) {
          return this.multiLocationConfig.routePrefix$.pipe(
            switchMap((routePrefix) => {
              const routeWithObject = routePrefix + '/' + objectType;
              return this.checkPermissions(
                objectType,
                parentNamespace as string,
                userId as string,
                namespace as string,
                routeWithObject as string,
              );
            }),
          );
        }

        return this.config.routePrefix$.pipe(
          switchMap((routePrefix) => {
            const targetNamespace = parentNamespace || namespace;
            return this.checkPermissions(
              objectType,
              targetNamespace as string,
              userId as string,
              namespace as string,
              routePrefix as string,
            );
          }),
        );
      }),
    );
  }
}
