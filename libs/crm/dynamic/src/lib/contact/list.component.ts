import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { ObjectType, PAGE_ROUTES, ListObjectsPageComponent, CrmInjectionToken } from '@galaxy/crm/static';
import { of } from 'rxjs';

@Component({
  // requires selector to prevent ID collision
  selector: 'crm-list-contacts',
  template: `
    <crm-list-objects-page
      [objectType]="objectType"
      [createObjectRoute]="createObjectRoute"
      [importCSVRoute]="importCSVRoute"
      [presetFilters]="presetFilters$ | async"
    ></crm-list-objects-page>
  `,
  imports: [CommonModule, ListObjectsPageComponent],
})
export class ListContactsPageComponent {
  private readonly config = inject(CrmInjectionToken);

  PAGE_ROUTES = PAGE_ROUTES;
  objectType: ObjectType = 'Contact';
  createObjectRoute = `${PAGE_ROUTES.CONTACT.ROOT}/${PAGE_ROUTES.CONTACT.SUBROUTES.CREATE}`;
  importCSVRoute = `${PAGE_ROUTES.BULK_IMPORT.ROOT}`;

  protected readonly presetFilters$ = this.config.contact?.presetFilters$ ?? of([]);
}
