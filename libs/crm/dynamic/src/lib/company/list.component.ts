import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ObjectType, PAGE_ROUTES, ListObjectsPageComponent, CrmInjectionToken } from '@galaxy/crm/static';
import { PageActionsDirective } from '@galaxy/crm/static';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { firstValueFrom, Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';

@Component({
  // requires selector to prevent ID collision
  selector: 'crm-list-companies',
  template: `
    <crm-list-objects-page
      [objectType]="objectType"
      [createObjectRoute]="createObjectRoute"
      [accountPageRoute]="accountPageRoute"
      [importCSVRoute]="importCSVRoute"
      [presetFilters]="presetFilters$ | async"
    >
      <crm-page-actions *ngIf="prospectObjectRoute$ | async as prospectRoute">
        <a
          (click)="handleOpenProspectorClick(prospectRoute)"
          [attr.data-action]="'clicked-prospect-crm-' + objectType.toLowerCase()"
          mat-button
          color="primary"
          data-testid="prospect-crm-object-button"
        >
          <mat-icon *ngIf="(this.hasAccessToProspectorFeature$ | async) === false">arrow_circle_up</mat-icon>
          {{ 'COMPANY.PROSPECT_OBJECT_BUTTON' | translate }}
        </a>
      </crm-page-actions>
    </crm-list-objects-page>
  `,
  imports: [
    ListObjectsPageComponent,
    CommonModule,
    PageActionsDirective,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
  ],
})
export class ListCompaniesPageComponent {
  private readonly config = inject(CrmInjectionToken);
  private readonly router = inject(Router);

  PAGE_ROUTES = PAGE_ROUTES;
  objectType: ObjectType = 'Company';
  createObjectRoute = `${PAGE_ROUTES.COMPANY.ROOT}/${PAGE_ROUTES.COMPANY.SUBROUTES.CREATE}`;
  accountPageRoute = PAGE_ROUTES.COMPANY.ACCOUNT;
  importCSVRoute = `${PAGE_ROUTES.BULK_IMPORT.ROOT}`;
  protected readonly presetFilters$ = this.config.company?.presetFilters$ ?? of([]);
  protected hasAccessToProspectorFeature$ = this.config.hasAccessToFeature$?.('crm-lead-prospector') ?? of(false);

  private showLeadProspector$ = this.config.showLeadProspector$ ?? of(false);
  protected prospectObjectRoute$: Observable<string> = this.showLeadProspector$.pipe(
    map((hasAccess) => {
      if (hasAccess) {
        return `/${PAGE_ROUTES.PROSPECT.ROOT}/${PAGE_ROUTES.PROSPECT.SUBROUTES.BUSINESSES}`;
      } else {
        return '';
      }
    }),
  );

  protected handleOpenProspectorClick(route: string): void {
    firstValueFrom(this.hasAccessToProspectorFeature$).then((hasAccess) => {
      if (hasAccess) {
        this.router.navigateByUrl(route);
      } else {
        this.config.openRestrictedDialog?.('crm-lead-prospector');
      }
    });
  }
}
