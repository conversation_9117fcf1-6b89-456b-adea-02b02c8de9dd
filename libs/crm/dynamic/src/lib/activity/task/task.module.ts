import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {
  ActivityType,
  ObjectType,
  PAGE_ROUTES,
  PageAnalyticsInjectionToken,
  pageAnalyticsInjectionTokenGenerator,
  tableFiltersInjectionTokenGenerator,
  FieldOptionsInjectionToken as ModelDrivenFormFieldOptionsInjectionToken,
  modelDrivenFormFieldOptionsInjectionTokenGenerator,
} from '@galaxy/crm/static';
import { GalaxyFilterChipInjectionToken } from '@vendasta/galaxy/filter/chips';
import { FeatureFlagGuard } from '../../feature-flag-guard.service';

const OBJECT_TYPE: ObjectType = 'Activity';
const ACTIVITY_TYPE: ActivityType = 'Task';

const OBJECT_ROUTES: Routes = [
  {
    path: '',
    redirectTo: PAGE_ROUTES.TASK.SUBROUTES.LIST,
    pathMatch: 'full',
  },
  {
    path: PAGE_ROUTES.TASK.SUBROUTES.LIST,
    loadComponent: () => import('./list.component').then((m) => m.ListTasksPageComponent),
    data: { view: 'list' },
  },
  {
    path: PAGE_ROUTES.TASK.SUBROUTES.BOARD,
    loadComponent: () => import('./list.component').then((m) => m.ListTasksPageComponent),
    canActivate: [FeatureFlagGuard],
    data: { featureFlag: 'crm_object_board_view', view: 'board' },
  },
];

@NgModule({
  imports: [RouterModule.forChild(OBJECT_ROUTES)],
  exports: [RouterModule],
  providers: [
    {
      provide: ModelDrivenFormFieldOptionsInjectionToken,
      useFactory: modelDrivenFormFieldOptionsInjectionTokenGenerator(OBJECT_TYPE),
    },
    {
      provide: GalaxyFilterChipInjectionToken,
      useFactory: tableFiltersInjectionTokenGenerator(OBJECT_TYPE, ACTIVITY_TYPE),
    },
    {
      provide: PageAnalyticsInjectionToken,
      useFactory: pageAnalyticsInjectionTokenGenerator(OBJECT_TYPE),
    },
  ],
})
export class CrmTaskModule {}
