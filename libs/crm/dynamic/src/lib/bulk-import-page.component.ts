import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { PAGE_ROUTES, TranslationModule } from '@galaxy/crm/static';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { Router, RouterModule } from '@angular/router';
import { CrmBulkImportStepperComponent } from './multi-object-bulk-import/bulk-import-stepper.component';
import { CrmDependencies, CrmInjectionToken } from '@galaxy/crm/static';

@Component({
  selector: 'crm-bulk-import',
  template: `
    <glxy-page [pagePadding]="true">
      <glxy-page-toolbar>
        <glxy-page-nav>
          <glxy-page-nav-button
            [attr.data-action]="'clicked-back-from-crm-multi-bulk-import'"
            previousPageTitle="{{ previousPageTitle | translate }}"
            [previousPageUrl]="(routePrefix$ | async) + '/' + previousPageUrl"
          ></glxy-page-nav-button>
        </glxy-page-nav>
        <glxy-page-title>{{ 'BULK_IMPORT.TITLE' | translate }}</glxy-page-title>
      </glxy-page-toolbar>

      <glxy-page-wrapper>
        <crm-bulk-import-stepper [previousPageUrl]="previousPageUrl"></crm-bulk-import-stepper>
      </glxy-page-wrapper>
    </glxy-page>
  `,
  providers: [],
  imports: [CommonModule, TranslationModule, GalaxyPageModule, RouterModule, CrmBulkImportStepperComponent],
})
export class CrmBulkImportComponent {
  PAGE_ROUTES = PAGE_ROUTES;
  routePrefix$ = this.config.routePrefix$;
  previousPageUrl!: string;
  previousPageTitle!: string;

  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    private router: Router,
  ) {
    const navigation = this.router.getCurrentNavigation();
    let previousPageObjectType = '';
    if (navigation && navigation.extras.state) {
      previousPageObjectType = navigation.extras.state['previousPageObjectType'];
    }
    this.getPreviousPageInfo(previousPageObjectType);
  }

  getPreviousPageInfo(previousPageObjectType: string) {
    switch (previousPageObjectType) {
      case 'Company':
        this.previousPageUrl = PAGE_ROUTES.COMPANY.ROOT;
        this.previousPageTitle = 'COMPANY.TITLE';
        break;
      case 'Opportunity':
        this.previousPageUrl = PAGE_ROUTES.OPPORTUNITY.ROOT;
        this.previousPageTitle = 'OPPORTUNITY.TITLE';
        break;
      default:
        this.previousPageUrl = PAGE_ROUTES.CONTACT.ROOT;
        this.previousPageTitle = 'CONTACT.TITLE';
        break;
    }
  }
}
