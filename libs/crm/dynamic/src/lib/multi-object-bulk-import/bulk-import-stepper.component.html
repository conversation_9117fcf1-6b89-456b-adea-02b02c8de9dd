<mat-vertical-stepper class="mat-elevation-z0" [linear]="true" #stepper>
  @let fileUploadCompleted = fileUploadCompleted$ | async;
  <mat-step [completed]="fileUploadCompleted">
    <ng-template matStepLabel>
      <span class="stepper-title">{{ 'BULK_IMPORT.UPLOAD_CSV_STEP.TITLE' | translate }}</span>
    </ng-template>
    <crm-file-upload-step></crm-file-upload-step>
    <div>
      <button
        mat-flat-button
        color="primary"
        [attr.data-action]="'clicked-next-crm-file-upload-completed-for-multi'"
        [disabled]="!fileUploadCompleted"
        (click)="nextStep('fileUploadCompleted')"
      >
        {{ 'ACTIONS.NEXT' | translate }}
      </button>
    </div>
  </mat-step>
  <mat-step [completed]="fieldMappingCompleted$ | async">
    <ng-template matStepLabel>
      <span class="stepper-title">{{ 'BULK_IMPORT.MAP_FIELDS_STEP.TITLE' | translate }}</span>
    </ng-template>
    <crm-field-mapping-step (fieldMappingsErrors)="updateFieldMappingsErrors($event)"></crm-field-mapping-step>
    <div>
      <button
        mat-stroked-button
        matStepperPrevious
        [attr.data-action]="'clicked-back-crm-field-mapping-step-for-multi'"
      >
        {{ 'ACTIONS.BACK' | translate }}
      </button>
      <button
        mat-flat-button
        color="primary"
        class="field-mapping-next-button"
        [glxyTooltip]="fieldMappingsErrors()"
        [glxyTooltipDisabled]="fieldMappingsErrors() === ''"
        [disabled]="(fieldMappingCompleted$ | async) === false"
        [attr.data-action]="'clicked-next-crm-field-mapping-completed-for-multi'"
        (click)="nextStep('fieldMappingCompleted')"
      >
        {{ 'ACTIONS.NEXT' | translate }}
      </button>
    </div>
  </mat-step>
  <mat-step>
    <ng-template matStepLabel>
      <span class="stepper-title">{{ 'BULK_IMPORT.REVIEW_IMPORT_STEP.TITLE' | translate }}</span>
    </ng-template>
    <p>
      {{ 'BULK_IMPORT.REVIEW_IMPORT_STEP.INSTRUCTIONS' | translate }}
      <br />
      {{
        'BULK_IMPORT.REVIEW_IMPORT_STEP.ADDING_CUSTOMERS_FROM_FILENAME'
          | translate
            : {
                estimatedText: estimatedText$ | async,
                rows: csvRows$ | async,
                csvFilename: csvFilename$ | async,
              }
      }}
    </p>
    <glxy-form-field>
      <mat-checkbox
        [checked]="true"
        (change)="updateDuplicateIsChecked.set($event.checked)"
        class="stepper-duplicates-checkbox"
      >
        {{ 'BULK_IMPORT.REVIEW_IMPORT_STEP.UPDATE_DUPLICATES' | translate }}
        <extended>{{ 'BULK_IMPORT.REVIEW_IMPORT_STEP.UPDATE_DUPLICATES_INSTRUCTIONS' | translate }}</extended>
      </mat-checkbox>
    </glxy-form-field>

    <div>
      <button
        mat-stroked-button
        matStepperPrevious
        [attr.data-action]="'clicked-back-crm-review-import-step-for-multi'"
      >
        {{ 'ACTIONS.BACK' | translate }}
      </button>
      <button
        mat-flat-button
        color="primary"
        (click)="startImport()"
        [attr.data-action]="'clicked-finish-crm-import-for-multi'"
      >
        {{ 'ACTIONS.FINISH' | translate }}
      </button>
    </div>
  </mat-step>
</mat-vertical-stepper>
