<p>
  {{ 'BULK_IMPORT.UPLOAD_CSV_STEP.INSTRUCTIONS' | translate }}
</p>
<p>
  <li [innerHTML]="'BULK_IMPORT.UPLOAD_CSV_STEP.REQUIREMENTS.CONTACT' | translate"></li>
  <li>{{ 'BULK_IMPORT.UPLOAD_CSV_STEP.REQUIREMENTS.COMPANY' | translate }}</li>
  <li>{{ 'BULK_IMPORT.UPLOAD_CSV_STEP.REQUIREMENTS.UNIQUE_HEADER_NAMES' | translate }}</li>
  @if (isPartnerCenter) {
    <li [innerHTML]="'BULK_IMPORT.UPLOAD_CSV_STEP.REQUIREMENTS.SALESPERSON_MAPPING' | translate"></li>
  }
  <li>{{ 'BULK_IMPORT.UPLOAD_CSV_STEP.REQUIREMENTS.PHONE_NUMBER_RECOMMENDATION' | translate }}</li>
</p>
<p>
  <strong>{{ 'BULK_IMPORT.UPLOAD_CSV_STEP.MAXIMUM_FILE_SIZE' | translate }}</strong>
</p>
<p>
  <a [href]="sampleFileUrl" target="_blank">
    <mat-icon color="primary">save_alt</mat-icon>
    <span class="downloadLink">
      {{ 'BULK_IMPORT.UPLOAD_CSV_STEP.DOWNLOAD_CSV_TEMPLATE' | translate }}
    </span>
  </a>
</p>
<div class="file-upload">
  <div [hidden]="files.length !== 0">
    <glxy-uploader
      #glxyUploader
      [accept]="'text/csv'"
      [uploadUrl]="fileUploadURL$ | async"
      [layout]="'button-only'"
      [maxFiles]="maxFiles"
      [maxFileSize]="maxFileSize"
      (fileUploadErrored)="onError($event)"
      (fileUploaded)="onUpload($event)"
      (filesChanged)="onFilesChanged($event)"
    ></glxy-uploader>
    <div class="error-message">
      {{ 'BULK_IMPORT.UPLOAD_CSV_STEP.REQUIRED' | translate }}
    </div>
  </div>
  <glxy-uploader-list
    [hidden]="files.length === 0"
    [files]="files"
    (fileDeleted)="glxyUploader.deleteFile($event)"
  ></glxy-uploader-list>
</div>
