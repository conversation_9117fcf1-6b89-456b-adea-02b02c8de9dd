import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { CRMFileUploadURLsService } from '@vendasta/crm';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  FileInfo,
  FileUploadError,
  FileUploadStatus,
  GALAXY_UPLOADER_SERVICE_TOKEN,
  GalaxyImageUploaderService,
  GalaxyUploaderModule,
} from '@vendasta/galaxy/uploader';
import { map, take } from 'rxjs/operators';
import { TranslationModule } from '@galaxy/crm/static';
import { BulkImportStepperService } from '../bulk-import-stepper.service';
import { CsvParserService } from './csv-parser.service';
import { CrmDependencies, CrmInjectionToken } from '@galaxy/crm/static';
import { MatIcon } from '@angular/material/icon';
import { FieldMappingService } from '../field-mapping-step/field-mapping.service';

@Component({
  selector: 'crm-file-upload-step',
  templateUrl: 'file-upload-step.component.html',
  providers: [{ provide: GALAXY_UPLOADER_SERVICE_TOKEN, useClass: GalaxyImageUploaderService }, CsvParserService],
  styleUrls: ['file-upload-step.component.scss'],
  imports: [CommonModule, TranslationModule, MatFormFieldModule, GalaxyUploaderModule, MatIcon],
})
export class CrmFileUploadStepComponent {
  private readonly config: CrmDependencies = inject(CrmInjectionToken);
  private readonly stepperService = inject(BulkImportStepperService);
  private readonly fieldMapping = inject(FieldMappingService);
  private readonly fileUploadURL = inject(CRMFileUploadURLsService);
  private readonly snack = inject(SnackbarService);
  private readonly parser = inject(CsvParserService);

  isPartnerCenter = this.config.appID === 'partner-center-client';
  sampleFileUrl = this.isPartnerCenter
    ? 'https://storage.googleapis.com/crmaas-public/PartnerBulkImportTemplate.csv'
    : 'https://storage.googleapis.com/crmaas-public/BulkImportTemplate.csv';
  files: FileInfo[] = [];

  maxFiles = 1;
  maxFileSize = 5 * 1024 ** 2;
  fileUploadURL$ = this.config.namespace$.pipe(
    map((namespace) => {
      return this.fileUploadURL.getUploadFileURLForObject(undefined, namespace);
    }),
  );

  onUpload(event: FileInfo): void {
    this.snack.openSuccessSnack('BULK_IMPORT.UPLOAD_CSV_STEP.SUCCESSFUL_UPLOAD', {
      interpolateTranslateParams: { filename: event.name },
    });
  }

  onError(event: FileUploadError): void {
    this.snack.openErrorSnack(event.error.message);
  }

  onFilesChanged(files: FileInfo[]): void {
    if (files.length > 0 && files[0].status === FileUploadStatus.Success) {
      this.completeStep();
      this.parseCSV(files[0].file as File);
    } else {
      this.blockStep();
    }
    this.files = files;
  }

  completeStep(): void {
    this.stepperService.completeStep('fileUploadCompleted', true);
  }

  blockStep(): void {
    this.stepperService.completeStep('fileUploadCompleted', false);
  }

  private parseCSV(file: File) {
    this.parser
      .parseCSV('multi', file.name)
      .pipe(take(1))
      .subscribe((res) => {
        this.fieldMapping.resetImportTypeValues();

        this.stepperService.setCSVFilename(file.name);
        this.stepperService.setCSVRows(res.rows, res.isEstimate);
        this.stepperService.setCSVFirstRow(res.firstRow);
        this.stepperService.setCSVHeaderMappings(res.rowMapping);
      });
  }
}
