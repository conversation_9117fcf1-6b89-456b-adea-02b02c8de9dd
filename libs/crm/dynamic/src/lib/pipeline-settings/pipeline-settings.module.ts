import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDivider } from '@angular/material/divider';
import { MatIcon } from '@angular/material/icon';
import { MatInput } from '@angular/material/input';
import { RouterOutlet, Routes } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyNavItemComponent } from '@vendasta/galaxy/nav';
import { GalaxyNavLayoutModule } from '@vendasta/galaxy/nav-layout';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyPageNavModule } from '@vendasta/galaxy/page-nav';
import { GalaxyStickyFooterModule } from '@vendasta/galaxy/sticky-footer';
import { PipelineSettingsFormComponent } from './pipeline-settings-form/pipeline-settings-form.component';
import { PipelineSettingsComponent } from './pipeline-settings.component';
import { RouterModule } from '@angular/router';
import { PipelineSettingsService } from './pipeline-settings.service';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { FeatureFlagGuard } from '../feature-flag-guard.service';

export const PipelineSettingsRoutes: Routes = [
  {
    path: '',
    loadComponent: () => import('./pipeline-settings.component').then((m) => m.PipelineSettingsComponent),
    children: [
      {
        path: ':pipelineId',
        loadComponent: () =>
          import('./pipeline-settings-form/pipeline-settings-form.component').then(
            (m) => m.PipelineSettingsFormComponent,
          ),
        canDeactivate: [(c: PipelineSettingsFormComponent) => c?.canDeactivate()],
      },
      {
        path: '',
        loadComponent: () =>
          import('./pipeline-settings-form/pipeline-settings-form.component').then(
            (m) => m.PipelineSettingsFormComponent,
          ),
      },
    ],
    canActivate: [FeatureFlagGuard],
    data: { featureFlag: 'crm_object_board_view' },
  },
];
@NgModule({
  imports: [
    RouterModule.forChild(PipelineSettingsRoutes),
    CommonModule,
    GalaxyPageModule,
    TranslateModule,
    MatButtonModule,
    GalaxyStickyFooterModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    MatInput,
    MatIcon,
    MatCardModule,
    MatDivider,
    GalaxyLoadingSpinnerModule,
    GalaxyEmptyStateModule,
    RouterOutlet,
    GalaxyPageNavModule,
    GalaxyNavLayoutModule,
    GalaxyNavItemComponent,
    GalaxyButtonLoadingIndicatorModule,
    PipelineSettingsComponent,
    PipelineSettingsFormComponent,
  ],
  providers: [PipelineSettingsService],
  exports: [RouterModule],
})
export class PipelineSettingsModule {}
