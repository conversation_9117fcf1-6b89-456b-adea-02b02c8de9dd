import { CommonModule } from '@angular/common';
import { Component, Inject, Input, OnInit, Optional, signal } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Params, Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { PageService } from '@vendasta/galaxy/page/src/page.service';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { BehaviorSubject, Observable, forkJoin, of, combineLatest } from 'rxjs';
import { catchError, map, shareReplay, switchMap, take, tap } from 'rxjs/operators';
import {
  CrmDependencies,
  CrmInjectionToken,
  FieldInterface,
  ObjectType,
  PAGE_ROUTES,
  SNACKBAR_DURATION,
  TranslationModule,
  CrmFormService,
  CRMTrackingService,
  FieldConfigInterface,
  SavingStatus,
  LoadingStatus,
  CrmModelDrivenFormComponent,
  CrmObjectInjectionToken,
  CrmObjectDependencies,
  TranslateForCrmObjectPipe,
  TranslateForCrmObjectService,
  CrmMultilocationInjectionToken,
  CrmMultiLocationDependencies,
  CrmObjectManagementTableChangesService,
} from '@galaxy/crm/static';
import { toSignal } from '@angular/core/rxjs-interop';
import { FieldValueInterface } from '@vendasta/crm';

@Component({
  selector: 'crm-edit-object',
  templateUrl: './edit-object.component.html',
  imports: [
    CommonModule,
    TranslateModule,
    TranslationModule,
    GalaxyPageModule,
    MatButtonModule,
    MatIconModule,
    CrmModelDrivenFormComponent,
    RouterModule,
    TranslateForCrmObjectPipe,
  ],
})
export class CrmEditObjectComponent implements OnInit {
  @Input({ required: true }) objectType!: ObjectType;

  PAGE_ROUTES = PAGE_ROUTES;
  routePrefix$ = this.config.routePrefix$;

  fieldConfig$: Observable<FieldConfigInterface> = of({});
  LoadingStatus = LoadingStatus;
  SavingStatus = SavingStatus;
  loadingStatus$$ = new BehaviorSubject<LoadingStatus>(LoadingStatus.LOADING);
  savingStatus$$ = new BehaviorSubject<SavingStatus>(SavingStatus.NOT_SAVING);

  previousPageRoute$!: Observable<string>;
  previousPageTitle$!: Observable<string>;
  defaultPageUrl$!: Observable<string>;

  overrideHistory = signal(false);

  objectSubtype$ = this.crmObjectDependencies?.objectSubtype$ ?? of('');

  crmObjectId$ = this.route.params.pipe(
    map((params: Params) => params['crmObjectId']),
    shareReplay(1),
  );
  private readonly returnUrl$ = this.route.queryParams.pipe(
    map((params: Params) => params['returnUrl'] ?? ''),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  isMultiLocation = toSignal(this.multiLocationConfig.isMultiLocation$ ?? of(false));

  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    @Inject(CrmMultilocationInjectionToken) private readonly multiLocationConfig: CrmMultiLocationDependencies,
    private readonly crmFormService: CrmFormService,
    private readonly snackService: SnackbarService,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly trackingService: CRMTrackingService,
    private readonly translateForCrmObjectService: TranslateForCrmObjectService,
    private readonly pageService: PageService,
    private readonly crmObjectManagementTableChangesService: CrmObjectManagementTableChangesService,
    @Optional()
    @Inject(CrmObjectInjectionToken)
    private readonly crmObjectDependencies: CrmObjectDependencies,
  ) {}

  ngOnInit(): void {
    const fieldSchemas$ = combineLatest([this.crmObjectId$, this.objectSubtype$]).pipe(
      switchMap(([crmObjectId, crmObjectSubtype]) =>
        this.crmFormService.listAllFormFieldsInitializedWithCrmObject$(this.objectType, crmObjectId, crmObjectSubtype),
      ),
      take(1),
    );
    const fieldGroups$ = this.crmFormService
      .listAllObjectFormFieldGroups$(this.objectType, this.objectSubtype$)
      .pipe(take(1));

    this.fieldConfig$ = this.crmObjectId$.pipe(
      tap(() => this.loadingStatus$$.next(LoadingStatus.LOADING)),
      switchMap(() => forkJoin([fieldSchemas$, fieldGroups$])),
      tap(([fieldSchemas, fieldGroups]) => {
        const empty = fieldSchemas.length === 0 || fieldGroups.length === 0;
        this.loadingStatus$$.next(empty ? LoadingStatus.EMPTY : LoadingStatus.OK);
      }),
      map(([fieldSchemas, fieldGroups]) => {
        return {
          fieldSchemas: fieldSchemas,
          fieldGroups: fieldGroups,
        };
      }),
      catchError((error) => {
        this.loadingStatus$$.next(LoadingStatus.ERROR);
        this.errorSnackbar(error, 'ERRORS.GENERIC_LOAD_ERROR');
        return of({} as FieldConfigInterface);
      }),
      shareReplay(1),
    );

    if (this.crmObjectDependencies) {
      this.defaultPageUrl$ = this.crmObjectDependencies.defaultPageUrl$ ?? of('');
    } else {
      let routePrefix: Observable<string>;
      if (this.isMultiLocation()) {
        routePrefix = this.multiLocationConfig.routePrefix$;
      } else {
        routePrefix = this.config.routePrefix$;
      }
      this.defaultPageUrl$ = routePrefix.pipe(
        map((routePrefix) => {
          switch (this.objectType) {
            case 'Contact':
              return `${routePrefix}/${PAGE_ROUTES.CONTACT.ROOT}`;
            case 'Company':
              return `${routePrefix}/${PAGE_ROUTES.COMPANY.ROOT}`;
            default:
              return '';
          }
        }),
      );
    }
    this.previousPageRoute$ = this.defaultPageUrl$.pipe(
      switchMap((defaultUrl) => {
        return this.returnUrl$.pipe(
          map((returnUrl) => {
            if (this.pageService.getPreviousPageUrl(defaultUrl) === '/' || returnUrl) {
              this.overrideHistory.set(true);
            }
            return returnUrl || defaultUrl;
          }),
        );
      }),
    );

    this.previousPageTitle$ = this.returnUrl$.pipe(
      switchMap((returnUrl) => {
        if (returnUrl) {
          return of('');
        }
        return this.translateForCrmObjectService.getTranslationForCrmObject(this.objectType, 'TITLE');
      }),
    );
  }

  onSave(fields: FieldInterface[]): void {
    combineLatest([this.crmObjectId$, this.objectSubtype$])
      .pipe(
        take(1),
        tap(() => this.savingStatus$$.next(SavingStatus.SAVING)),
        switchMap(([crmObjectId, objectSubtype]) =>
          this.crmFormService
            .validateAndUpdateCrmObject$(this.objectType, crmObjectId, fields, objectSubtype)
            .pipe(map((updatedValue) => [updatedValue, objectSubtype] as [FieldValueInterface, string])),
        ),
        tap(([newObject, objectSubtype]) => {
          this.crmObjectManagementTableChangesService.addUpdatedCrmObject(
            this.objectType,
            newObject ?? {},
            objectSubtype,
          );
        }),
        catchError((err) => {
          this.savingStatus$$.next(SavingStatus.ERROR);
          throw err;
        }),
        tap(() => this.savingStatus$$.next(SavingStatus.OK)),
        switchMap(() =>
          this.translateForCrmObjectService.getTranslationForCrmObject(this.objectType, 'EDIT_PAGE.SAVE.SUCCESS'),
        ),
      )
      .subscribe({
        next: (success) => {
          this.trackingService.trackEvent(this.objectType, 'edited-crm-object');
          this.snackService.openSuccessSnack(success, {
            duration: SNACKBAR_DURATION,
          });
          this.navigateBack();
        },
        error: (error) => {
          this.trackingService.trackEvent(this.objectType, 'failed-crm-edit');
          this.errorSnackbar(error, 'ERRORS.GENERIC_EDIT_MESSAGE');
        },
      });
  }

  private errorSnackbar(error: any | null, defaultMessage: string) {
    let message: string | null = null;
    if (error && error.message) {
      message = error.message;
    }
    this.snackService.openErrorSnack(message ?? defaultMessage, {
      duration: SNACKBAR_DURATION,
    });
  }

  onCancel(): void {
    this.trackingService.trackEvent(this.objectType, 'cancelled-crm-edit');
    this.navigateBack();
  }

  private navigateBack(): void {
    this.previousPageRoute$.pipe(take(1)).subscribe((previousPageRoute) => {
      this.defaultPageUrl$
        .pipe(
          map((defaultPageUrl) => {
            return defaultPageUrl;
          }),
          take(1),
        )
        .subscribe((defaultPageUrl) => {
          previousPageRoute = this.overrideHistory()
            ? (previousPageRoute ?? defaultPageUrl)
            : (this.pageService.getPreviousPageUrl(defaultPageUrl) ?? previousPageRoute);

          this.pageService.pop();
          const urlTree = this.router.parseUrl(previousPageRoute);
          const queryParamIndex = previousPageRoute?.indexOf('?') ?? 0;
          const path = queryParamIndex > 0 ? previousPageRoute.substring(0, queryParamIndex) : previousPageRoute;
          if (path) {
            this.router.navigate([path], { queryParams: urlTree.queryParams, fragment: urlTree.fragment ?? undefined });
          }
        });
    });
  }
}
