@use 'design-tokens' as *;

mat-icon {
  font-size: $font-preset-4-size;
  height: $font-preset-4-size;
  width: $font-preset-4-size;
}

.business-review-rating,
.business-review-count,
.claim-status,
.business-address,
.business-phone {
  @include text-preset-5;
  display: inline-flex;
}

.claim-icon {
  font-size: $font-preset-5-size;
}
.positive {
  color: $glxy-green-700;
}
.negative {
  color: $glxy-red-700;
}
.rating {
  color: $glxy-yellow-700;
}

.ranking-container {
  padding-left: $spacing-3;
  padding-bottom: $spacing-3;
  padding-right: $spacing-3;
  display: flex;
  flex-direction: column;
}

.description {
  margin-bottom: $spacing-2;
  margin-left: $spacing-3;
}

.business {
  display: flex;
  align-items: center;
}

.tab-container {
  min-height: 450px;
}

mat-divider {
  margin-left: $spacing-3;
}

.mat-divider-horizontal {
  width: 90%;
}

.business-item {
  padding: $spacing-3;
}

.local-seo-map {
  height: 350px;
}

.last-business {
  border-top-style: dashed;
}

.business-chip {
  background: $glxy-red-50;
  color: $glxy-red-800;
  padding: $spacing-1 $spacing-2;
  border-radius: 40%;
  font-size: $font-preset-5-size;
  font-weight: 500;
  max-width: min-content;
}

.rank-chip {
  background: $glxy-red-50;
  color: $glxy-red-800;
  padding: $spacing-1 $spacing-2;
  border-radius: 50%;
  font-size: $font-preset-5-size;
  font-weight: 500;
  max-width: min-content;
  margin-left: $spacing-1;
}

.top-performer {
  background: $glxy-green-50;
  color: $glxy-green-500;
}

.average-performer {
  background: $glxy-yellow-50;
  color: $glxy-yellow-600;
}

.business-not-found {
  font-size: $font-preset-5-size;
  color: $glxy-red-800;
  padding: $spacing-3;
}

.empty-state-container {
  padding: $spacing-3;
}

.map-info-text {
  padding: $spacing-3;
  background-color: $glxy-blue-50;
  @include text-preset-4;
  color: $secondary-font-color;
  display: flex;
  align-items: center;

  mat-icon {
    margin-right: $spacing-1;
    font-size: $font-preset-2-size;
    height: 20px;
    width: 20px;
  }
}

.table-row {
  align-items: center;
}

.divider {
  color: $primary-text-color;
  padding: 0 $spacing-1;
}

.disabled-link {
  pointer-events: none;
  color: $black;
}

.no-results-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
}

.updated-date {
  @include text-preset-5;
  color: $tertiary-font-color;
  margin-top: auto;
  margin-left: auto;
}

.radius-in-progress-badge {
  margin-left: $spacing-3;
  margin-bottom: $spacing-2;
}

.map-align {
  padding: $spacing-3;
  width: 600px;
}

.local-seo-container {
  height: fit-content;
}
