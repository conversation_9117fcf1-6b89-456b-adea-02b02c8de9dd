@use 'design-tokens' as *;
@import '../constants';

.stream-item-write-comment textarea {
  width: 100%;
  min-height: 61px;
  height: 38px;
  flex-grow: 1;
  margin-right: 5px;
}

@media screen and (min-width: $media--phone-minimum) {
  .stream-item-write-comment textarea {
    min-height: 38px;
  }
}

.comment-box {
  width: 100%;
}

textarea {
  min-height: 62px;
  line-height: 1.5;
  vertical-align: top;
  resize: vertical;
  font: inherit;
  padding: 6px;
}

.stream-item-write-comment {
  display: flex;
}

.stream-item-user-image {
  width: $left-column-width;
  flex-shrink: 0;
  padding-left: 5px;
}

@media screen and (max-width: $media--tablet-minimum) {
  .stream-item-user-image {
    width: $mobile-left-column-width;
  }
}

@media screen and (min-width: $media--phone-minimum) {
  .stream-item-write-comment {
    flex-direction: row;
  }
}

.stream-item-slide-out {
  overflow: hidden;
  transition: max-height 0.8s cubic-bezier(0, 1, 0.5, 1);
  max-height: 0;
}

.stream-item-comments-list {
  padding: 0;
  margin: 0;
  list-style: none;
}

.stream-item-comments-header {
  font-weight: bold;
  text-align: center;
}

.loading-container {
  width: 16px;
  height: 16px;
  margin: auto;
}
