@use 'design-tokens' as *;
@import '../constants';

.stream-item {
  display: flex;
  margin-bottom: 16px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.stream-item-main-section {
  display: flex;
}

.stream-item-type {
  width: $left-column-width;
  flex-shrink: 0;

  .stream-item-image {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: $light-gray center;
    background-size: cover;
  }

  img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: $light-gray;
  }

  .stream-item-icon {
    width: 50px;
    height: 50px;

    mat-icon {
      font-size: 42px;
      width: 42px;
      height: 42px;
      padding: 4px;
      color: $gray;
    }
  }
}

@media screen and (max-width: $media--tablet-minimum) {
  .stream-item-type {
    width: $mobile-left-column-width;

    .stream-item-image {
      width: 36px;
      height: 36px;
    }

    img {
      width: 36px;
      height: 36px;
    }

    .stream-item-icon {
      width: 36px;
      height: 36px;

      mat-icon {
        font-size: 32px;
        width: 32px;
        height: 32px;
        padding: 2px;
        color: $gray;
      }
    }
  }
}

.stream-item-container {
  position: relative;
  float: left;
  width: 100%;
  max-width: 800px;
  min-height: 50px;
}

.stream-item-footer {
  font-size: 12px;
  line-height: 1;
  color: $gray;

  a {
    margin-left: 10px;
  }
}

.stream-item-actions {
  width: 100%;
  padding: 8px 8px 8px $mobile-left-column-width;
}

@media screen and (min-width: $media--tablet-minimum) {
  .stream-item-actions {
    width: 50%;
    padding: 8px 20px 8px $left-column-width;
  }
}

.stream-item-reactions {
  padding: 6px 0;
  display: flex;
  justify-content: space-between;
}

@media screen and (max-width: $media--tablet-minimum) {
  .stream-item-reactions {
    font-size: 14px;
  }
}

.has-reacted {
  color: $blue;
}

::ng-deep .like-tooltip {
  white-space: pre-line;
  font-size: 12px;
  background: $darker-gray;
  margin-left: 8px !important;
}

.option-container {
  display: inline-flex;
  align-items: center;
  color: $gray;

  &.is-subscribed {
    color: $blue;
  }

  mat-icon {
    margin-right: 4px;
  }

  &.has-reacted {
    color: $blue;
  }

  &:hover {
    cursor: pointer;
  }
}

.like-container span:last-child {
  padding-left: 5px;
}

.read-more {
  cursor: pointer;
  margin: 8px 0;
  font-size: 12px;
}

.body {
  overflow: hidden;
}

.fade-out {
  position: relative;
  &:after {
    content: '';
    position: absolute;
    top: 160px;
    left: 0;
    height: 40px;
    width: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0), white);
  }
}

.compact-post {
  padding: 12.5px 12.5px 12.5px 30px;
  display: flex;
  cursor: pointer;

  &:hover {
    background-color: $glxy-blue-50;
  }

  .stream-item-type {
    width: auto;
  }
  .stream-item-icon {
    height: 24px;
    width: 24px;
    margin-right: 6px;

    mat-icon {
      font-size: 24px;
      height: 24px;
      width: 24px;
      padding: 0;
    }
  }

  .compact-body {
    padding-bottom: 5px;
  }
}
