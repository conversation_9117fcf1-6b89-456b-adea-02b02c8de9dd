@use 'design-tokens' as *;
@import 'breaks';

:host {
  display: inline-block;
}

.filter {
  display: flex;
  flex-direction: row;
  align-items: center;
  border-radius: 16px;
  margin: 4px;
  padding: 0 0 0 8px;
  color: rgba(0, 0, 0, 0.54);
  background: #eaeaea;
  cursor: pointer;
  transition: all 0.15s;
  user-select: none;
  width: fit-content;

  @include respond-to(mobile) {
    border-radius: 6px;
    font-size: 14px;
  }

  .icon {
    vertical-align: middle;
  }

  .title {
    font-weight: bold;
  }
}

.filter:hover {
  box-shadow:
    0px 3px 1px -2px rgba(0, 0, 0, 0.2),
    0px 2px 2px 0px rgba(0, 0, 0, 0.14),
    0px 1px 5px 0px rgba(0, 0, 0, 0.12);
}
