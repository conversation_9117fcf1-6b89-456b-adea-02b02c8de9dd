@use 'design-tokens' as *;
@import '../constants';

.stream-item-comment {
  display: flex;
  padding-top: 8px;
  padding-bottom: 8px;
  overflow: hidden;
}

.stream-item-comment-image {
  width: $left-column-width;
  flex-shrink: 0;
  padding-left: 5px;
}

@media screen and (max-width: $media--tablet-minimum) {
  .stream-item-comment-image {
    width: $mobile-left-column-width;
  }
}

.stream-item-comment-text {
  font-size: 14px;
  line-height: 1.2;
  word-wrap: break-word;
  flex: 1;
}

.comment-user-name {
  font-weight: bold;
}

.stream-item-comment-meta {
  margin-top: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: $gray;
}
