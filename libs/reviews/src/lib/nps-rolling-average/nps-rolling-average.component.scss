@use 'design-tokens' as *;
@import 'hide-sizes';

.container-nps {
  border: 1px solid $border-color;
  border-radius: $spacing-1;
  width: 100%;
  height: 100%;
}

.nps-rolling-graph-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 0 !important;
  padding-top: 0;
}

.nps-rolling-graph-title {
  @include text-preset-3;
  font-weight: 400;
  margin-bottom: 0;
}

.nps-rolling-graph-content {
  margin-top: -13px;
  padding: 0;
}

.nps-tool-tip {
  color: $tertiary-text-color;
  margin: $spacing-2 0;
  font-weight: 400;
  @include text-preset-1;
  cursor: pointer;
}
