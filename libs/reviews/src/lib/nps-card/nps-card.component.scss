@import 'design-tokens';

:host {
  display: block;
  font-family: $default-font-family;
  ::ng-deep {
    .mat-mdc-card-header mat-card-title {
      margin-bottom: $spacing-1;
    }
  }
}

.nps-score-card {
  max-width: 745px;
  margin: 0 auto $spacing-4;
}

.nps-score-card-header {
  border-bottom: none !important;
  margin-bottom: 0px;
}

.nps-score-card-title {
  @include text-preset-4;
  font-weight: 500;
  color: $link-color;
  margin-bottom: $spacing-2;
}

.ml-nps-score-card-title {
  @include text-preset-4;
  font-weight: 500;
  color: $primary-text-color;
  margin-bottom: $spacing-1;
}

.nps-score-badge-left-time {
  display: flex;
  align-items: center;
}

.nps-score-time-wrapper {
  display: flex;
  flex-direction: column;
  align-items: baseline;
  margin: 0;
}

.nps-score-left-time {
  font-weight: 300;
  color: $tertiary-text-color;
  padding-left: $spacing-1;
}

.nps-review-content {
  font-weight: 300;
  margin-top: $spacing-2;
  margin-bottom: $spacing-2;
}

.nps-card-contact-info {
  display: flex;
  gap: 30px;
  background-color: $primary-background-color;
  color: $link-color;
  padding: 15px;
  border-radius: 0 0 5px 5px;
  @include text-preset-4;
  font-weight: 300;
  margin-top: 0;
}

.resend-nps {
  @include text-preset-4;
  color: $secondary-text-color;
  display: flex;
  justify-content: space-between;
  background-color: $error-background-color;
  margin-top: 0;
  border-radius: 0 0 $spacing-1 $spacing-1;
}

.resend-text {
  flex-shrink: 1;
}

.resend-button {
  @include text-preset-4;
  font-weight: 500;
  color: $secondary-text-color;
  height: auto;
  min-height: 36px;
  border: 1px solid $field-border-color;
}

.resent-date {
  @include text-preset-4;
  color: $secondary-text-color;
  background-color: $warn-background-color;
  margin-top: 0;
  border-radius: 0 0 $spacing-1 $spacing-1;
}
