@use 'design-tokens' as *;
@import 'hide-sizes';

.npschart {
  display: flex;
  align-items: center; /* Center the chart vertically */
}

.common-label-styling {
  width: 35%;
  height: 20%;
  margin: 5px 2px 5px 10px;
}

.calculated-overall-score {
  @include text-preset-1;
}

.container-nps {
  border: 1px solid $border-color;
  border-radius: 3px;
  width: 100%;
  height: 100%;
}

.overall-chart-content {
  width: 70%;
}

:host .overall-chart-content ::ng-deep .apexcharts-legend.apexcharts-align-center.apx-legend-position-right {
  padding: 0 !important;
  right: 0 !important;
  top: 7px !important;
  gap: $spacing-1 !important;
}

:host .overall-chart-content ::ng-deep .apexcharts-legend-marker {
  display: none !important;
}

:host .overall-chart-content ::ng-deep .apexcharts-legend-text {
  padding: 0 !important;
  margin: 0 !important;
}

.nps-tool-tip {
  color: $tertiary-text-color;
  margin: $spacing-2 0;
  font-weight: 400;
  @include text-preset-1;
  cursor: pointer;
}

.nps-overall-chart-title {
  @include text-preset-3;
  font-weight: 400;
  margin-bottom: 0;
  flex: 1;
}

.nps-overall-chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 0 !important;
  padding-top: 0;
}

.score-container {
  display: flex;
  flex-direction: column;
  gap: 1px;
  padding-bottom: 13px;
  width: 30%;
}

.total-rating {
  @include text-preset-4;
  font-weight: 400;
  align-self: center;
  color: $secondary-text-color;
  text-wrap: nowrap;
}

.nps-card-content {
  padding: $spacing-2 !important;
  margin-top: $spacing-2;
  margin-left: $spacing-1;
}
