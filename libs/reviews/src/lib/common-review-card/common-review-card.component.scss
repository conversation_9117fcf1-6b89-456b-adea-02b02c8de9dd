@use 'design-tokens' as *;

$breakpoint-xs: 600px;

.flex-column {
  display: flex;
  flex-direction: column;
}

.review-card {
  max-width: 900px;
  margin: 0 auto 24px;
  transition: none;
  box-shadow: none;

  .review-card-header-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;

    .review-card-header-content-title {
      padding-right: 36px;
    }
  }

  glxy-button-loading-indicator {
    mat-icon {
      vertical-align: middle;
    }
  }

  .mat-mdc-button {
    margin-right: 8px;
  }

  .mat-mdc-raised-button {
    margin-right: 8px;
  }

  .recommendation-icon {
    vertical-align: middle;
    margin-left: 2px;
  }
}

.mat-mdc-card-title {
  .reviewer-name {
    font-weight: 500;
  }

  .review-action {
    font-weight: 400;
  }

  .product-url {
    font-size: 14px;
    line-height: 24px;

    .business-address {
      color: $gray;
    }
  }
}

.review-title {
  @include text-preset-3--bold;
  margin-bottom: $spacing-2;
}

.footer {
  display: flex;
  flex-direction: row-reverse;
  padding: 8px;
}

.card-secondary-actions {
  flex-grow: 1;
}

.card-tertiary-actions {
  position: absolute;
  top: 10px;
  right: 10px;
}

.tertiary-action-icon {
  padding-left: 10px;
  color: $gray;

  &:hover {
    color: $blue;
  }
}

.stars {
  margin-bottom: 1em;
}

.star {
  color: $light-gray;
}

.star-lit {
  color: $yellow;
}

.comments {
  .comment-count {
    color: $blue;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }

  .comment-feed {
    max-height: 300px;
    overflow: auto;
    transition: 0.2s max-height ease-in-out;

    &.collapsed {
      max-height: 0;
      overflow: hidden;
    }
  }
}

.comment {
  margin: 1em 0;

  .comment-content {
    background-color: $secondary-background-color;
    padding: 10px 10px 16px 10px;
    transition: 0.2s height ease-in-out;

    .comment-text {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }

    &.collapsed {
      padding: 0;
      max-height: 0;
      overflow: hidden;
      visibility: hidden;
    }
  }

  .comment-editor {
    transition:
      0.2s height ease-in-out,
      0.2s padding-top ease-in-out;

    textarea {
      max-height: 150px;
    }

    &.collapsed {
      max-height: 0;
      padding-top: 0;
      overflow: hidden;
    }
  }
}

.comment-by {
  font-weight: 500;
  margin-right: 8px;
}

.comment-meta {
  margin-top: 4px;
  color: $gray;
}

.last-failed-review-comment {
  margin: 1em 0;

  .last-failed-review-content {
    display: flex;
    flex-direction: row;
    gap: $spacing-2;
    background-color: $light-red;
    padding: 10px;
    transition: 0.2s height ease-in-out;

    .last-failed-review-comment-error-icon {
      color: $dark-red;
    }

    .last-failed-review-comment-error {
      margin-right: 8px;
      font-weight: bold;
      color: $black;
    }

    .last-failed-review-comment-meta {
      margin-top: 4px;
      color: $gray;
    }

    &.collapsed {
      padding: 0;
      max-height: 0;
      overflow: hidden;
      visibility: hidden;
    }
  }
}

.responder {
  padding-top: 24px;
  transition:
    0.2s max-height ease-in-out,
    0.2s padding-top ease-in-out;

  &.collapsed {
    max-height: 0;
    overflow: hidden;
    padding-top: 0;
  }
}

.response-box {
  //mat-field
  width: 100%;
  border: 1px solid $border-color;
  padding: 8px;
  border-radius: 5px;

  ::ng-deep {
    .mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label,
    .mat-form-field-empty.mat-form-field-label {
      display: inline-block;
      width: auto;
      padding-bottom: 4px;
      padding: 4px;
      left: 4px;
      background-color: $white;
    }

    .mat-form-field-underline {
      display: none;
    }
  }

  .response-area {
    width: 100%;
    padding: 7px 5px;

    .response-input {
      width: 100%;
      border: none;
      overflow: auto;
      padding: 0px;
      font-size: 14px;
      font-weight: 400;
      line-height: 1.4;
      font-family: inherit;
      outline: none;
      background-color: $card-background-color;
      color: $primary-font-color;
    }
  }

  .response-box-bottom-row {
    display: flex;
    align-items: center;

    .response-templates {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .response-chips {
        margin: 10px 5px 0px 0px;

        .response-chip {
          margin-right: 5px;
          border-color: $border-color;
          border-radius: 24px;
          line-height: 24px;
          font-size: 12px;

          .ai-icon {
            width: $spacing-3;
            height: $spacing-3;
            margin-bottom: 2px;
            margin-right: $spacing-1;
          }
        }
      }

      .shuffle-templates {
        margin-top: 10px;
      }
    }

    .copy-to-clip-board-button {
      margin-top: 10px;

      .copy-to-clip-board-icon {
        vertical-align: middle;
      }
    }
  }
}

.response-hint {
  position: relative;
  top: 5px;
}

.response-warning {
  margin-top: 5px;
}

.response-actions {
  text-align: right;
}

.badges {
  margin: 1em 0;
}

.badge {
  //badges can move to their own file later
  display: inline-block;
  vertical-align: middle;
  line-height: 1;
  white-space: nowrap;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: $gray;
  background-color: $lighter-gray;
  margin-right: 8px;

  mat-icon {
    vertical-align: middle;
  }
}

.badge-menu {
  padding: 0 12px;
  cursor: pointer;
}

.badge--red,
.badge--warn {
  color: $red;
  background-color: $light-red;
}

.badge--yellow,
.badge--caution {
  color: $yellow;
  background-color: $light-yellow;
}

.badge--green,
.badge--positive {
  color: $green;
  background-color: $light-green;
}

.badge--blue,
.badge--primary {
  color: $blue;
  background-color: $light-blue;
}

.badge--gray {
  color: $dark-gray;
  background-color: $light-gray;
}

.secondary-button--text {
  color: $blue;
}

.task-manager-response {
  margin: 1em 0;
  background-color: $secondary-background-color;
  padding: 5px 10px 10px;
  transition:
    0.2s max-height ease-in-out,
    0.2s padding-top ease-in-out;

  &.collapsed {
    max-height: 0;
    overflow: hidden;
    padding-top: 0;
    background-color: white;
  }

  .task-manager-response-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}

.task-manager-response--divider {
  padding-top: 24px;
}

.task-manager-response--title {
  margin: 5px 0 8px 0;
  display: flex;

  b {
    margin-left: 8px;
  }
}

.task-manager-response--feedback {
  margin: 20px 16px 9px 16px;
  display: flex;
  flex-direction: column;

  .feedback-content {
    display: flex;
    justify-content: flex-start;
    align-content: center;
  }

  div {
    margin-bottom: 8px;

    b {
      margin-left: 6px;
    }
  }
}

.task-manager-response--button {
  button {
    margin: 10px 8px 10px 0;
  }

  .approve-response {
    color: white;
    background-color: #43a047;
  }
}

.task-manager-response--text {
  margin-bottom: 8px;
}

.deleted {
  opacity: 0.3;
}

.deleted-info {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
  border: 1px solid $light-gray;
  border-radius: 2px;
  opacity: 1;

  .deleted-info-text {
    display: flex;
    margin: 16px 76px 16px 16px;
    color: $dark-gray;
    @media screen and (max-width: $breakpoint-xs) {
      margin-right: 16px;
    }
  }

  .deleted-info-mat-divider {
    $deleted-divider-margin: 16px;
    margin-left: $deleted-divider-margin;
    margin-right: $deleted-divider-margin;
    width: calc(100% - 2 * #{$deleted-divider-margin});
    height: 1px;
  }

  .deleted-info-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-left: 8px;
    margin-bottom: 16px;
    margin-right: 16px;
    @media screen and (max-width: $breakpoint-xs) {
      margin-left: 16px;
    }
    @media screen and (min-width: $breakpoint-xs) and (max-width: $media--tablet-minimum) {
      flex-direction: column;
      justify-content: center;
      gap: $spacing-3;
    }
  }

  .deleted-info-button-mobile {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: $spacing-3;
    @media screen and (max-width: $breakpoint-xs) {
      width: 100% !important;
    }
    @media screen and (min-width: $breakpoint-xs) and (max-width: $media--tablet-minimum) {
      flex-direction: column;
      justify-content: center;
      align-items: stretch;
    }
  }

  .collapsed-colour {
    color: $primary-accent-color;
  }

  .expanded-colour {
    color: $primary-background-color;
    background-color: $primary-accent-color;
  }
}

.menu-disabled {
  pointer-events: none;
}

.edited-review-text {
  line-height: 19px;
  margin-top: 4px;
  color: $dark-gray;
}

.edit-button {
  margin-top: $negative-1;
  margin-right: $negative-1;
}

.connect-info {
  width: 100%;
  margin-bottom: 16px;
  border: 1px solid $light-gray;
  border-radius: 2px;
  opacity: 1;

  .connect-info-content {
    display: flex;
  }

  .connect-info-icon {
    margin-left: 12px;
    margin-top: 12px;
    color: $gray;
  }

  .connect-info-text {
    margin: 16px 0px 16px 5px;
    color: $dark-gray;
    @media screen and (max-width: $breakpoint-xs) {
      margin-right: 16px;
    }
  }

  .connect-info-button {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: $spacing-3;
    margin-left: 8px;
    margin-bottom: 16px;
    margin-right: 16px;

    @media screen and (max-width: $media--tablet-minimum) {
      flex-direction: column;
      justify-content: center;
      align-items: stretch;
    }
  }
}

.digital-agent-buttons {
  padding-top: 16px;
  display: flex;

  .digital-agent-button-container {
    min-width: 150px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.not-connected-banner {
  padding: 22px;
  margin: 6px 0 6px 0;
  background-color: $light-yellow;
  border: 1px solid $yellow;
  display: flex;
  align-items: center;

  mat-icon {
    color: $dark-yellow;
  }
}

.inline-icon-padding {
  margin-right: 8px;
}

.repman-settings-link {
  padding-left: 16px;
}

.action-buttons {
  display: flex;

  .action-buttons-container {
    min-width: 150px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.suggest-response-connect-button {
  margin-right: 16px;
}

.suggest-response-copy-button {
  margin-right: 16px;
}

.request-approval-button {
  margin-right: 16px;
}

.no-click {
  color: rgba(0, 0, 0, 0.54);
  text-decoration: none;
  pointer-events: none;
}
