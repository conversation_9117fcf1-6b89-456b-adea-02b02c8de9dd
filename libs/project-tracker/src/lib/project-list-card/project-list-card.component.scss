@use 'sass:math';
@use 'design-tokens' as *;

.empty-conversation-list {
  min-height: 180px;
}

.project {
  display: flex;
  flex-direction: row;
  width: 100%;
  margin: 16px 0px;
  align-items: center;

  .project-icon {
    min-width: 40px;
    margin-right: $spacing-3;
  }
  .project-info {
    font-size: $font-preset-3-size;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    .project-description {
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .project-status-date {
      font-size: $font-preset-5-size;
      color: $gray;
    }

    .project-status {
      display: flex;
      align-items: center;

      .project-status-badge {
        .blue-chip {
          background: $light-blue;
          color: $dark-blue;
          border: 1px solid $dark-blue;
        }
        .yellow-chip {
          background: $light-yellow;
          color: $dark-yellow;
          border: 1px solid $dark-yellow;
        }
        .green-chip {
          background: $light-green;
          color: $dark-green;
          border: 1px solid $dark-green;
        }
      }
    }
  }
}
