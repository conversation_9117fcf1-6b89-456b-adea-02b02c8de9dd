<div [ngClass]="{ row: !stacked, 'stacked-row': stacked }">
  <div class="name" [ngClass]="{ col: !stacked, 'col-xs-4': !stacked, 'stacked-name': stacked }">
    {{ row.asyncTranslatedName | async }}
  </div>
  <div
    [ngClass]="{
      col: !stacked,
      'col-xs-8': !stacked,
      red: row.color === rowColor.RED,
      green: row.color === rowColor.GREEN,
    }"
    class="value"
  >
    @if (row.id === 'status') {
      <smb-invoicing-payment-status-chip
        [status]="row.value"
        [disputeResponseDue]="row.dispute?.evidenceDetails?.dueBy"
      ></smb-invoicing-payment-status-chip>
    } @else {
      @if (!!row.valuePrefixImage && !!row.valuePrefixImage.src) {
        <img
          [src]="row.valuePrefixImage.src"
          [alt]="row.valuePrefixImage.alt"
          [attr.width]="row.valuePrefixImage.width"
          [attr.height]="row.valuePrefixImage.height"
          class="prefix-image"
        />
      } @else if (!!row.valuePrefixIcon) {
        <mat-icon class="prefix-image">{{ row.valuePrefixIcon }}</mat-icon>
      }
      <div>
        @if (!row.routerLink) {
          <span>
            @if (row.value) {
              {{ row.value }}
            } @else {
              {{ row.asyncValue | async }}
            }
          </span>
        }
        @if (!!row.routerLink) {
          <a [routerLink]="row.routerLink">
            @if (row.value) {
              {{ row.value }}
            } @else {
              {{ row.asyncValue | async }}
            }
          </a>
        }
        @if (row.secondaryValue) {
          <div class="secondary-value">
            {{ row.secondaryValue }}
          </div>
        }
      </div>
    }
  </div>
</div>
