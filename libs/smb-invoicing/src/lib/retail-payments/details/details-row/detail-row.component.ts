import { Component, Input } from '@angular/core';
import { AsyncPipe, NgClass } from '@angular/common';
import { RouterLink } from '@angular/router';
import { PaymentStatusChipComponent } from '../../../payment-status-chip/payment-status-chip.component';
import { Dispute } from '@galaxy/billing';
import { Observable } from 'rxjs';
import { MatIcon } from '@angular/material/icon';

export enum Color {
  RED,
  GREEN,
}

export interface Image {
  src: string;
  alt: string;
  width?: number;
  height?: number;
}

export interface Row {
  id: string;
  asyncTranslatedName?: Observable<string>;
  value?: string;
  asyncValue?: Observable<string>;
  secondaryValue?: string;
  valuePrefixImage?: Image;
  valuePrefixIcon?: string;
  color?: Color;
  routerLink?: string[];
  dispute?: Dispute;
}

@Component({
  selector: 'smb-invoicing-payment-detail-row',
  templateUrl: './detail-row.component.html',
  styleUrls: ['./detail-row.component.scss'],
  standalone: true,
  imports: [NgClass, RouterLink, PaymentStatusChipComponent, AsyncPipe, MatIcon],
})
export class DetailRowComponent {
  @Input() row: Row;
  // Whether or not the title and value should be stacked or appear
  // on the same line.
  @Input() stacked: boolean;
  rowColor = Color;
}
