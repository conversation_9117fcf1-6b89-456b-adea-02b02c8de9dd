<mat-card appearance="outlined">
  <mat-card-header>
    <mat-card-title>
      {{ 'PAYMENT_PAGE.PAYMENT_DETAILS' | translate }}
    </mat-card-title>
    <div class="spacer"></div>
    <div class="object-id">{{ payment.id }}</div>
  </mat-card-header>
  <mat-card-content>
    @for (row of rows; track row.id) {
      <smb-invoicing-payment-detail-row [row]="row"></smb-invoicing-payment-detail-row>
    }
    @if (showInvoicePaymentCTA) {
      <glxy-alert class="alert-banner" type="info">
        <span>{{ 'PAYMENT_PAGE.RETRY_PAYMENT_DESCRIPTION' | translate }}</span>
        <button mat-stroked-button class="cta" [routerLink]="'/invoices/' + payment.referenceId + '/edit'">
          {{ 'PAYMENT_PAGE.GO_TO_INVOICE' | translate }}
        </button>
      </glxy-alert>
    }
    @if (!viewConfig.hideBusinessReference) {
      <smb-invoicing-payment-business-page [businessId]="payment.customerId"></smb-invoicing-payment-business-page>
    }
  </mat-card-content>
</mat-card>
