<glxy-table-container
  [dataSource]="dataSource"
  [columns]="visibleColumns"
  [pageSizeOptions]="pageSizeOptions"
  [pageSize]="defaultPageSize"
  [ngClass]="{ 'remove-upper-border': displayTableContentsOnly() }"
  [border]="!displayTableContentsOnly()"
>
  @if (!displayTableContentsOnly() && !isEmptyState()) {
    <glxy-table-content-header
      [showFilters]="true"
      [showExport]="true"
      [showSearch]="false"
      [showSort]="false"
      [showColumnArrange]="false"
      (export)="openExportDialog()"
    >
      <div filters-area class="filters-section">
        <glxy-filter-chips (filtersChanged)="onFilterChanged($event)" />
      </div>
    </glxy-table-content-header>
  }

  <table mat-table>
    <tr mat-header-row *matHeaderRowDef="[]"></tr>

    <tr
      mat-row
      *matRowDef="let row; columns: []"
      class="clickable-row"
      (click)="transactionClickEvent($event, row)"
    ></tr>

    <ng-container matColumnDef="{{ TransactionColumns.STATUS }}">
      <th mat-header-cell *matHeaderCellDef class="left-align">
        {{ 'PAYMENTS_PAGE.STATUS' | translate }}
      </th>
      <td mat-cell *matCellDef="let p">
        <smb-invoicing-payment-status-chip
          [status]="p.status"
          [hoverText]="
            p.declineReasonDescription
              ? (p.declineReasonDescription | translate) + ' ' + (p.declineReasonNextSteps | translate)
              : p.failureMessage
          "
          [disputeResponseDue]="p.dispute?.evidenceDetails?.dueBy"
        ></smb-invoicing-payment-status-chip>
      </td>
    </ng-container>

    <ng-container matColumnDef="{{ TransactionColumns.DESCRIPTION }}">
      <th mat-header-cell *matHeaderCellDef class="description-col" class="left-align">
        {{ 'PAYMENTS_PAGE.DESCRIPTION' | translate }}
      </th>
      <td mat-cell *matCellDef="let p" class="description-col" class="left-align">
        @if (canClickTransactionReference()) {
          <a (click)="transactionReferenceClickEvent($event, p)">
            @if (
              p.referenceType === transactionTypeEnum.INVOICE || p.referenceType === transactionTypeEnum.REFUND_INVOICE
            ) {
              <span>{{ p.description | padInvoiceNumber }}</span>
            } @else {
              <span>{{ p.description }}</span>
            }
          </a>
        } @else {
          <span>
            @if (
              p.referenceType === transactionTypeEnum.INVOICE || p.referenceType === transactionTypeEnum.REFUND_INVOICE
            ) {
              <span>{{ p.description | padInvoiceNumber }}</span>
            } @else {
              <span>{{ p.description }}</span>
            }
          </span>
        }
        <div *ngIf="p.referenceType === transactionTypeEnum.INVOICE" class="description-type">
          {{ 'PAYMENTS_PAGE.INVOICE' | translate }}
        </div>
        <div *ngIf="p.referenceType === transactionTypeEnum.REFUND_INVOICE" class="description-type">
          {{ 'PAYMENTS_PAGE.INVOICE_REFUND' | translate }}
        </div>
        <div *ngIf="p.referenceType === transactionTypeEnum.SALES_ORDER" class="description-type">
          {{ 'PAYMENTS_PAGE.ORDER' | translate }}
        </div>
        <div *ngIf="p.referenceType === transactionTypeEnum.REFUND_SALES_ORDER" class="description-type">
          {{ 'PAYMENTS_PAGE.ORDER_REFUND' | translate }}
        </div>
        <div *ngIf="p.referenceType === transactionTypeEnum.DISPUTE" class="description-type">
          {{ 'PAYMENTS_PAGE.DISPUTE' | translate }}
        </div>
        <div *ngIf="p.referenceType === transactionTypeEnum.REFUND_FAILURE" class="description-type">
          {{ 'PAYMENTS_PAGE.REFUND_FAILURE' | translate }}
        </div>
        <div *ngIf="p.referenceType === transactionTypeEnum.PAYOUT_FAILURE" class="description-type">
          {{ 'PAYMENTS_PAGE.PAYOUT_FAILURE' | translate }}
        </div>
        <div *ngIf="p.referenceType === transactionTypeEnum.UNSET">
          {{ p.description }}
        </div>
      </td>
    </ng-container>

    <ng-container matColumnDef="{{ TransactionColumns.CUSTOMER_ID }}">
      <th mat-header-cell *matHeaderCellDef class="customer-col" class="left-align">
        {{ customerTitleTranslationKey() | translate }}
      </th>
      <td mat-cell *matCellDef="let p" class="customer-col left-align">
        <a (click)="customerClickEvent($event, p.customerId)" class="truncated-text">
          <span>{{ p.customerName || p.customerId }}</span>
        </a>
      </td>
    </ng-container>

    <ng-container matColumnDef="{{ TransactionColumns.CREATED }}">
      <th mat-header-cell *matHeaderCellDef class="left-align">
        {{ 'PAYMENTS_PAGE.CREATED' | translate }}
      </th>
      <td mat-cell *matCellDef="let p" class="left-align">
        <div>{{ p.created | glxyDate }}</div>
        <span class="created-time">
          {{ p.created | glxyDate: DateFormat.shortTime }}
        </span>
      </td>
    </ng-container>

    <ng-container matColumnDef="{{ TransactionColumns.PAYMENT_METHOD }}">
      <th mat-header-cell *matHeaderCellDef class="left-align">
        {{ 'PAYMENTS_PAGE.PAYMENT_METHOD' | translate }}
      </th>
      <td mat-cell *matCellDef="let p" class="left-align">
        @if (p.paymentMethodDetailsType === cardPaymentMethodType && !!p.cardDetails) {
          <div class="payment-method">
            <div class="card-img" *ngIf="imageUrls[p.cardDetails.cardType]">
              <img [src]="imageUrls[p.cardDetails.cardType]" alt="{{ p.cardDetails.cardType }}" height="25" />
            </div>
            <div>
              <span class="bullets">&bull;&bull;&bull;&bull;</span>
              {{ p.cardDetails.lastFourDigits }}
            </div>
          </div>
        } @else if (p.paymentMethodDetailsType === achDebitPaymentMethodType && !!p.achDetails) {
          <div class="payment-method">
            <div class="card-img">
              <mat-icon>account_balance</mat-icon>
            </div>
            <div>
              <span class="bullets">&bull;&bull;&bull;&bull;</span>
              {{ p.achDetails.lastFourDigits }}
            </div>
          </div>
        } @else if (p.paymentMethodDetailsType === acssDebitPaymentMethodType && !!p.acssDetails) {
          <div class="payment-method">
            <div class="card-img">
              <mat-icon>account_balance</mat-icon>
            </div>
            <div>
              <span class="bullets">&bull;&bull;&bull;&bull;</span>
              {{ p.acssDetails.lastFourDigits }}
            </div>
          </div>
        } @else {
          <div class="missing">&mdash;</div>
        }
      </td>
    </ng-container>

    <ng-container matColumnDef="{{ TransactionColumns.AMOUNT }}">
      <th mat-header-cell *matHeaderCellDef class="right-align">
        {{ 'PAYMENTS_PAGE.AMOUNT' | translate }}
      </th>
      <td mat-cell *matCellDef="let p" class="right-align">
        <span [style.font-weight]="netShown ? 400 : 600" class="amount">
          <billing-ui-simple-price-display
            [price]="p.amount || 0"
            [currencyCode]="p.currencyCode"
            [align]="'end'"
            [alwaysShowNumber]="true"
          >
          </billing-ui-simple-price-display>
        </span>
      </td>
    </ng-container>

    <ng-container matColumnDef="{{ TransactionColumns.FEE }}">
      <th mat-header-cell *matHeaderCellDef class="right-align">
        {{ 'PAYMENTS_PAGE.FEE' | translate }}
      </th>
      <td mat-cell *matCellDef="let p" class="right-align">
        <span class="amount">
          {{
            -(
              (p?.applicationFee > 0 ? p.applicationFee / 100 : 0) +
              (p?.paymentFacilitatorFee > 0 ? p.paymentFacilitatorFee / 100 : 0)
            ) | glxyCurrency: p.currencyCode : null
          }}
        </span>
      </td>
    </ng-container>

    <ng-container matColumnDef="{{ TransactionColumns.NET }}">
      <th mat-header-cell *matHeaderCellDef class="right-align">
        {{ 'PAYMENTS_PAGE.NET' | translate }}
      </th>
      <td mat-cell *matCellDef="let p" class="right-align">
        <span [style.font-weight]="netShown ? 600 : 400">
          {{
            (p.amount - (p?.applicationFee || 0) - (p?.paymentFacilitatorFee || 0)) / 100
              | glxyCurrency: p.currencyCode : null
          }}
        </span>
      </td>
    </ng-container>

    <ng-container matColumnDef="{{ TransactionColumns.ACTIONS }}">
      <th mat-header-cell *matHeaderCellDef></th>
      <td mat-cell *matCellDef="let p">
        <smb-invoicing-transaction-row-actions
          [row]="p"
          [displayIssueCreditNoteAction]="p?.referenceType === transactionTypeEnum.INVOICE"
          (reload)="reload()"
          (issueCreditNote)="issueCreditNoteHandler($event)"
          (viewInvoice)="viewInvoiceHandler($event)"
          *ngIf="
            (p.referenceType === transactionTypeEnum.INVOICE || p.referenceType === transactionTypeEnum.SALES_ORDER) &&
            (isRefundableStatus(p.status) || isRefundableDisputeStatus(p.dispute?.status))
          "
        ></smb-invoicing-transaction-row-actions>
      </td>
    </ng-container>
  </table>

  @if (isEmptyState()) {
    <glxy-empty-state class="empty-state-container">
      <glxy-empty-state-hero>
        <mat-icon>money</mat-icon>
      </glxy-empty-state-hero>
      <glxy-empty-state-title>
        {{ 'PAYMENTS_PAGE.NO_PAYMENTS_TITLE' | translate }}
      </glxy-empty-state-title>
      <p>
        {{ 'PAYMENTS_PAGE.NO_PAYMENTS_BODY' | translate }}
      </p>
      <glxy-empty-state-actions>
        <a mat-flat-button color="primary" (click)="emptyStatePrimaryCallToActionEvent()">
          {{ 'PAYMENTS_PAGE.NO_PAYMENTS_PRIMARY_CTA' | translate }}
        </a>
        @if (hasEmptyStateSecondaryCallToAction()) {
          <a mat-stroked-button (click)="emptyStateSecondaryCallToActionEvent()">
            {{ 'PAYMENTS_PAGE.NO_PAYMENTS_SECONDARY_CTA' | translate }}
          </a>
        }
      </glxy-empty-state-actions>
    </glxy-empty-state>
  }
</glxy-table-container>
