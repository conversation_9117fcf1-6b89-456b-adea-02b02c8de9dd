import { CommonModule, DatePipe } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  EventEmitter,
  Inject,
  input,
  Input,
  OnInit,
  Optional,
  Output,
  ViewChild,
} from '@angular/core';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import {
  DeclineReasons,
  isRefundableStatus,
  isRefundableDisputeStatus,
  PaymentService,
  RetailPaymentMethodType,
  Transaction,
  TransactionReferenceType,
} from '@galaxy/billing';
import { DateFormat } from '@vendasta/galaxy/utility/date-utils';
import { skip } from 'rxjs/operators';
import { TransactionFilters, TransactionsGalaxyPaginatedAPI } from './transactions-table-datasource';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { VaMaterialTableModule } from '@vendasta/uikit';
import { GalaxyDatepickerModule } from '@vendasta/galaxy/datepicker';
import { MatMenuModule } from '@angular/material/menu';
import { BillingUiModule } from '@vendasta/billing-ui';
import { TransactionRowActionsComponent } from './transaction-row-actions/transaction-row-actions.component';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { PadInvoiceNumberPipe } from '../../shared/pipes';
import { PaymentStatusChipComponent } from '../../payment-status-chip/payment-status-chip.component';
import { CUSTOMER_DATA_SERVICE, CustomerDataService } from '../../customer/customer-data';
import { paymentCardImageUrls } from '../../shared/image-src';
import { GalaxyColumnDef, GalaxyDataSource, GalaxyTableModule } from '@vendasta/galaxy/table';
import { ExportDialogComponent } from '../../export-financial-reports/components/export-dialog/export-dialog.component';
import { ExportDialogData } from '../../export-financial-reports/models';
import { GalaxyFilterChipInjectionToken } from '@vendasta/galaxy/filter/chips';
import { TransactionsFilterService } from './transactions-table-filter-service.service';
import { GalaxyFilterChipsModule } from '@vendasta/galaxy/filter/chips';

export enum TransactionColumns {
  STATUS = 'status',
  DESCRIPTION = 'description',
  CUSTOMER_ID = 'customerId',
  CREATED = 'created',
  PAYMENT_METHOD = 'payment-method',
  AMOUNT = 'amount',
  FEE = 'fee',
  NET = 'net',
  ACTIONS = 'actions',
}

@Component({
  standalone: true,
  selector: 'smb-invoicing-transactions-table',
  templateUrl: './transactions-table.component.html',
  styleUrls: ['./transactions-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    DatePipe,
    TransactionsFilterService,
    {
      provide: GalaxyFilterChipInjectionToken,
      useExisting: TransactionsFilterService,
    },
  ],
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    MatProgressBarModule,
    MatTableModule,
    MatPaginatorModule,
    MatIconModule,
    MatButtonModule,
    MatDialogModule,
    GalaxyPipesModule,
    GalaxyEmptyStateModule,
    GalaxyFilterChipsModule,
    VaMaterialTableModule,
    PaymentStatusChipComponent,
    GalaxyDatepickerModule,
    MatMenuModule,
    BillingUiModule,
    TransactionRowActionsComponent,
    PadInvoiceNumberPipe,
    GalaxyTableModule,
  ],
})
export class TransactionsTableComponent implements OnInit {
  public defaultPageSize = 25;
  pageSizeOptions = [25, 50, 100];
  TransactionColumns = TransactionColumns;

  columnDefinitions: { [key: string]: GalaxyColumnDef } = {
    [TransactionColumns.STATUS]: {
      id: TransactionColumns.STATUS,
    },
    [TransactionColumns.DESCRIPTION]: {
      id: TransactionColumns.DESCRIPTION,
    },
    [TransactionColumns.CUSTOMER_ID]: {
      id: TransactionColumns.CUSTOMER_ID,
    },
    [TransactionColumns.CREATED]: {
      id: TransactionColumns.CREATED,
    },
    [TransactionColumns.PAYMENT_METHOD]: {
      id: TransactionColumns.PAYMENT_METHOD,
    },
    [TransactionColumns.AMOUNT]: {
      id: TransactionColumns.AMOUNT,
    },
    [TransactionColumns.FEE]: {
      id: TransactionColumns.FEE,
    },
    [TransactionColumns.NET]: {
      id: TransactionColumns.NET,
    },
    [TransactionColumns.ACTIONS]: {
      id: TransactionColumns.ACTIONS,
    },
  };

  visibleColumns: GalaxyColumnDef[] = [];

  DateFormat = DateFormat;
  imageUrls = paymentCardImageUrls;
  isRefundableStatus = isRefundableStatus;
  isRefundableDisputeStatus = isRefundableDisputeStatus;

  startDate: Date | null = null;
  endDate: Date | null = null;

  cardPaymentMethodType = RetailPaymentMethodType.Card;
  achDebitPaymentMethodType = RetailPaymentMethodType.ACHDebit;
  acssDebitPaymentMethodType = RetailPaymentMethodType.ACSSDebit;

  @ViewChild(MatPaginator) paginator: MatPaginator;

  @Input() displayedColumns = [
    TransactionColumns.DESCRIPTION,
    TransactionColumns.CUSTOMER_ID,
    TransactionColumns.CREATED,
    TransactionColumns.PAYMENT_METHOD,
    TransactionColumns.STATUS,
    TransactionColumns.AMOUNT,
    TransactionColumns.FEE,
    TransactionColumns.NET,
    TransactionColumns.ACTIONS,
  ];
  @Input() filters: TransactionFilters;
  merchantId = input.required<string>();
  hasEmptyStateSecondaryCallToAction = input<boolean>(true);
  canClickTransactionReference = input<boolean>(true);
  displayTableBorder = input<boolean>(true);
  customerTitleTranslationKey = input<string>('PAYMENTS_PAGE.CUSTOMER');

  @Output() issueCreditNote = new EventEmitter<string>();
  @Output() customerClicked = new EventEmitter<string>();
  @Output() transactionReferenceClicked = new EventEmitter<Transaction>();
  @Output() transactionClicked = new EventEmitter<Transaction>();
  @Output() emptyStatePrimaryCallToAction = new EventEmitter<boolean>();
  @Output() emptyStateSecondaryCallToAction = new EventEmitter<boolean>();

  // removes the header/footer
  readonly displayTableContentsOnly = input<boolean>(false);

  paginatedAPI: TransactionsGalaxyPaginatedAPI;
  dataSource: GalaxyDataSource<Transaction>;

  // transactionTypeEnum included for use in template
  transactionTypeEnum = TransactionReferenceType;

  netShown: boolean = this.displayedColumns.some((v) => v === 'net');

  constructor(
    private paymentService: PaymentService,
    @Optional() @Inject(CUSTOMER_DATA_SERVICE) private customerDataService: CustomerDataService,
    public dialog: MatDialog,
    private datePipe: DatePipe,
    private destroyRef: DestroyRef,
    private router: Router,
  ) {
    toObservable(this.merchantId)
      .pipe(skip(1), takeUntilDestroyed(this.destroyRef))
      .subscribe((merchantId) => {
        this.paginatedAPI?.updateMerchantId(merchantId);
      });
  }

  ngOnInit(): void {
    this.paginatedAPI = new TransactionsGalaxyPaginatedAPI(
      this.paymentService,
      this.datePipe,
      this.filters,
      this.customerDataService,
    );
    this.paginatedAPI.updateMerchantId(this.merchantId());

    this.dataSource = new GalaxyDataSource<Transaction>(this.paginatedAPI);

    this.visibleColumns = this.displayedColumns
      .filter((col) => this.columnDefinitions[col])
      .map((col) => this.columnDefinitions[col]);
  }

  getNextSteps(failureCode: string, failureMessage: string): string {
    let message = '';
    const declineReason = DeclineReasons.get(failureCode);
    if (declineReason) {
      message = `${declineReason.description} ${declineReason.nextSteps}`;
    } else {
      message = failureMessage;
    }
    return message;
  }

  openExportDialog(): void {
    this.dialog.open(ExportDialogComponent, {
      width: '650px',
      data: {
        merchantId: this.merchantId(),
        startInterval: this.startDate,
        endInterval: this.endDate,
      } as ExportDialogData,
    });
  }

  onFilterChanged(filters): void {
    this.dataSource.setFilters(filters);
  }

  isEmptyState(): boolean {
    return this.dataSource.state.totalDataMembers === 0;
  }

  reload(): void {
    this.paginatedAPI.reload();
  }

  public customerClickEvent(event: Event, customerId: string): void {
    this.customerClicked.emit(customerId);
    this.consumeEvent(event);
  }

  public transactionReferenceClickEvent(event: Event, transaction: Transaction): void {
    this.transactionReferenceClicked.emit(transaction);
    this.consumeEvent(event);
  }

  public transactionClickEvent(event: Event, transaction: Transaction): void {
    this.transactionClicked.emit(transaction);
    this.consumeEvent(event);
  }

  public issueCreditNoteHandler(referenceId: string): void {
    this.issueCreditNote.emit(referenceId);
  }

  public viewInvoiceHandler(referenceId: string): void {
    this.router.navigate([`/invoices/${referenceId}/edit`]);
  }

  public emptyStatePrimaryCallToActionEvent(): void {
    this.emptyStatePrimaryCallToAction.emit(true);
  }

  public emptyStateSecondaryCallToActionEvent(): void {
    this.emptyStateSecondaryCallToAction.emit(true);
  }

  public consumeEvent(event: Event): void {
    event.stopPropagation();
  }
}
