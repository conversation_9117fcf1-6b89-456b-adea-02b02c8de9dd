<ng-container *ngIf="(loading$$ | async) === false">
  <ng-container *ngIf="canAcceptPayment$ | async; else onboardingState">
    <ng-container *ngIf="(hasActiveDisputes$ | async) === true">
      @if (paymentFacilitatorType() === 'CUSTOM_STRIPE') {
        <glxy-alert
          class="alert-banner"
          type="error"
          [showAction]="true"
          [actionTitle]="'PAYMENTS_PAGE.DISPUTE_ACTION' | translate"
          (actionClick)="openDisputeActionClicked()"
        >
          {{ 'PAYMENTS_PAGE.DISPUTE_ALERT' | translate }}
        </glxy-alert>
      } @else {
        <glxy-alert
          class="alert-banner"
          type="error"
          [showAction]="true"
          [actionTitle]="'PAYMENTS_PAGE.MANAGE_DISPUTES_IN_STRIPE' | translate"
          actionExternalLink="https://dashboard.stripe.com/payments?status[0]=lost&status[1]=won&status[2]=dispute_under_review&status[3]=dispute_needs_response&status[4]=closed&status[5]=inquiry_under_review&status[6]=inquiry_needs_response"
        >
          {{ 'PAYMENTS_PAGE.DISPUTE_ALERT' | translate }}
        </glxy-alert>
      }
    </ng-container>
    <div class="mat-elevation-z0">
      <smb-invoicing-transactions-table
        [merchantId]="merchantId()"
        [filters]="paymentsFilters()"
        [displayedColumns]="displayedColumns()"
        [hasEmptyStateSecondaryCallToAction]="hasEmptyStateSecondaryCallToAction()"
        [canClickTransactionReference]="canClickTransactionReference()"
        [displayTableBorder]="displayTableBorder()"
        [customerTitleTranslationKey]="customerTitleTranslationKey()"
        (issueCreditNote)="issueCreditNoteHandler($event)"
        (customerClicked)="customerClickedHandler($event)"
        (transactionReferenceClicked)="transactionReferenceClickedHandler($event)"
        (transactionClicked)="transactionClickedHandler($event)"
        (emptyStatePrimaryCallToAction)="emptyStatePrimaryCallToActionHandler()"
        (emptyStateSecondaryCallToAction)="emptyStateSecondaryCallToActionHandler()"
      ></smb-invoicing-transactions-table>
    </div>
  </ng-container>
  <ng-template #onboardingState>
    <smb-invoicing-retail-payments-onboarding
      [isSupportedRegion]="supportedRegion$ | async"
      description="BILLING.PAYMENTS_ONBOARDING.CTA.DESCRIPTION.PAYMENTS_PAGE"
      (onboardingUnsupportedRegionClicked)="onboardingStateContactSupportActionClicked($event)"
      (onboardingCallToActionClicked)="onboardingStateSetupVendastaPaymentsActionClicked($event)"
    ></smb-invoicing-retail-payments-onboarding>
  </ng-template>
</ng-container>
