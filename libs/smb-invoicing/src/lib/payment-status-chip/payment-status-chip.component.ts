import { Component, computed, input, OnInit, Signal, ViewChild } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { RetailPaymentStatus, DisputeStatus, ExtendedRetailPaymentStatus, BillingI18nModule } from '@galaxy/billing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { Observable, of } from 'rxjs';
import { AsyncPipe } from '@angular/common';
import { GalaxyPopoverModule, PopoverComponent } from '@vendasta/galaxy/popover';

@Component({
  standalone: true,
  selector: 'smb-invoicing-payment-status-chip',
  templateUrl: './payment-status-chip.component.html',
  styleUrls: ['./payment-status-chip.component.scss'],
  imports: [
    BillingI18nModule,
    GalaxyPopoverModule,
    GalaxyTooltipModule,
    MatIconModule,
    TranslateModule,
    GalaxyBadgeModule,
    AsyncPipe,
  ],
})
export class PaymentStatusChipComponent implements OnInit {
  @ViewChild('popover') popover: PopoverComponent;

  private _statusColorMap = {
    [RetailPaymentStatus.Succeeded]: 'blue',
    [DisputeStatus.WON]: 'green',
    [DisputeStatus.LOST]: 'red',
    [DisputeStatus.NEEDS_RESPONSE]: 'yellow',
    [DisputeStatus.WARNING_NEEDS_RESPONSE]: 'yellow',
    [DisputeStatus.UNDER_REVIEW]: 'grey',
    [DisputeStatus.WARNING_UNDER_REVIEW]: 'grey',
    [DisputeStatus.WARNING_CLOSED]: 'grey',
    [RetailPaymentStatus.Disputed]: 'red',
  };

  status = input<ExtendedRetailPaymentStatus>();
  disputeResponseDue = input<Date>();
  hoverText = input<string>();
  color = computed(() => this._statusColorMap[this.status()] || 'grey');
  statusTooltipTranslation: Signal<Observable<string>>;
  statusTranslation: Signal<Observable<string>>;
  showPopover = false;

  constructor(private translateService: TranslateService) {}

  ngOnInit(): void {
    this.statusTooltipTranslation = computed(() => {
      switch (this.status()) {
        case DisputeStatus.WARNING_UNDER_REVIEW:
          return this.translateService.stream('PAYMENT_STATUS_BADGE.TOOLTIP.DISPUTE_WARNING_UNDER_REVIEW');
        case DisputeStatus.WARNING_NEEDS_RESPONSE:
          return this.translateService.stream('PAYMENT_STATUS_BADGE.TOOLTIP.DISPUTE_WARNING_NEEDS_RESPONSE');
        case DisputeStatus.WARNING_CLOSED:
          return this.translateService.stream('PAYMENT_STATUS_BADGE.TOOLTIP.DISPUTE_WARNING_CLOSED');
        default:
          return of('');
      }
    });

    this.statusTranslation = computed(() => {
      switch (this.status()) {
        case RetailPaymentStatus.Succeeded:
          return this.translateService.stream('PAYMENT_STATUS_BADGE.SUCCEEDED');
        case RetailPaymentStatus.Failed:
          return this.translateService.stream('PAYMENT_STATUS_BADGE.FAILED');
        case RetailPaymentStatus.Pending:
          return this.translateService.stream('PAYMENT_STATUS_BADGE.PENDING');
        case RetailPaymentStatus.Refunded:
          return this.translateService.stream('PAYMENT_STATUS_BADGE.REFUNDED');
        case RetailPaymentStatus.PartiallyRefunded:
          return this.translateService.stream('PAYMENT_STATUS_BADGE.PARTIALLY_REFUNDED');
        case RetailPaymentStatus.Disputed:
          return this.translateService.stream('PAYMENT_STATUS_BADGE.DISPUTED');
        case DisputeStatus.WON:
          return this.translateService.stream('PAYMENT_STATUS_BADGE.DISPUTE_WON');
        case DisputeStatus.LOST:
          return this.translateService.stream('PAYMENT_STATUS_BADGE.DISPUTE_LOST');
        case DisputeStatus.NEEDS_RESPONSE:
          return this.disputeResponseDue()
            ? this._getDueByTranslation(this.disputeResponseDue())
            : this.translateService.stream('PAYMENT_STATUS_BADGE.DISPUTE_NEEDS_RESPONSE');
        case DisputeStatus.UNDER_REVIEW:
          return this.translateService.stream('PAYMENT_STATUS_BADGE.DISPUTE_UNDER_REVIEW');
        case DisputeStatus.WARNING_UNDER_REVIEW:
          return this.translateService.stream('PAYMENT_STATUS_BADGE.DISPUTE_WARNING_UNDER_REVIEW');
        case DisputeStatus.WARNING_NEEDS_RESPONSE:
          return this.disputeResponseDue()
            ? this._getDueByTranslation(this.disputeResponseDue())
            : this.translateService.stream('PAYMENT_STATUS_BADGE.DISPUTE_WARNING_NEEDS_RESPONSE');
        case DisputeStatus.WARNING_CLOSED:
          return this.translateService.stream('PAYMENT_STATUS_BADGE.DISPUTE_WARNING_CLOSED');
        default:
          return this.translateService.stream('PAYMENT_STATUS_BADGE.UNKNOWN');
      }
    });
  }

  private _getDueByTranslation(dueBy: Date): Observable<string> {
    const now = new Date();
    if (dueBy < now) {
      return this.translateService.stream('PAYMENT_STATUS_BADGE.DISPUTE_DEADLINE_MISSED');
    }

    const hoursRemaining = Math.floor((dueBy.getTime() - now.getTime()) / (1000 * 60 * 60));
    if (hoursRemaining < 24) {
      if (hoursRemaining === 1) {
        return this.translateService.stream('PAYMENT_STATUS_BADGE.DISPUTE_DUE_IN_HOUR', { hours: hoursRemaining });
      }
      return this.translateService.stream('PAYMENT_STATUS_BADGE.DISPUTE_DUE_IN_HOURS', { hours: hoursRemaining });
    }

    const daysRemaining = Math.floor(hoursRemaining / 24);
    if (daysRemaining === 1) {
      return this.translateService.stream('PAYMENT_STATUS_BADGE.DISPUTE_DUE_IN_DAY', { days: daysRemaining });
    }
    return this.translateService.stream('PAYMENT_STATUS_BADGE.DISPUTE_DUE_IN_DAYS', { days: daysRemaining });
  }

  public openPopover() {
    if (!this.hoverText()) {
      return;
    }
    this.popover.open();
  }

  public closePopover() {
    if (!this.hoverText()) {
      return;
    }
    this.popover.close();
  }

  public togglePopover(event: Event) {
    event.stopPropagation();
    if (!this.hoverText()) {
      return;
    }
    this.popover.isOpen ? this.popover.close() : this.popover.open();
  }
}
