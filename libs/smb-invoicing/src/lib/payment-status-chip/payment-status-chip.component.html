<div [glxyPopover]="popover" (click)="togglePopover($event)" (mouseenter)="openPopover()" (mouseleave)="closePopover()">
  <glxy-badge [color]="color()">
    {{ statusTranslation() | async }}
    @if ((statusTooltipTranslation() | async) !== '') {
      <mat-icon class="info-icon" [glxyTooltip]="statusTooltipTranslation() | async" [highContrast]="false">
        info_outline
      </mat-icon>
    }
  </glxy-badge>
</div>

<glxy-popover #popover [isOpen]="false">
  <div>{{ hoverText() }}</div>
</glxy-popover>
