@use 'design-tokens' as *;
@import 'components/button-toggle-group';

:host {
  display: block;
  height: 100%;
  width: 100%;
}

:host ::ng-deep glxy-avatar {
  min-width: 32px;
}

.mat-column-name {
  min-width: 200px;
  padding-left: 0;
  padding-right: 0;
}

.product-cell {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 12px 0;

  va-icon {
    margin-top: $spacing-2;
    margin-bottom: $spacing-2;
  }

  .product-name-type {
    margin-left: $spacing-3;
    flex-direction: column;
    align-content: center;

    .product-edition {
      display: flex;
      flex-wrap: wrap;
      row-gap: 8px;
    }

    .product-name {
      font-weight: bold;
      padding-right: $spacing-2;

      span {
        display: inline-block;
      }
    }

    .item-id {
      font-size: 11px;
      font-style: italic;
      text-overflow: ellipsis;
      max-width: 170px;
      overflow: hidden;
      white-space: nowrap;
      display: inline-block;
    }
  }
}

.quantity-column {
  glxy-form-field {
    padding-top: $spacing-3;
    width: 75px;
  }
}

.mat-column-wholesale {
  text-align: right;
  padding-left: 0;
}

.mat-column-retail {
  text-align: right;
}

.edition-shimmer {
  float: right;
  height: 30px;
  width: 130px;
}

.shimmer {
  float: right;
  height: $spacing-4;
  width: 116px;
}

.after-search-container {
  height: 100%;
  display: flex;
  align-items: center;
  gap: $spacing-2;
}

.mat-mdc-row.mat-row-disabled {
  background-color: $lighter-grey;
  &:hover {
    background-color: $lighter-grey;
  }
}

:host ::ng-deep .glxy-table-filter-and-search {
  .filter-search-group {
    flex-basis: min-content;
    flex-grow: 1;
  }
}

.filter-toggle {
  padding-right: $spacing-2;

  .item-type-filter {
    display: flex;
    justify-content: space-between;
  }
}
