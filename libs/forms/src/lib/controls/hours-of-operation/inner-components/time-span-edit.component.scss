@import 'design-tokens';

:host {
  flex-direction: column;
  min-width: 310px;
  width: 100%;
}

.time-span {
  &__row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: $spacing-3;
  }
  &__label {
    @include text-preset-4--bold;
    margin-bottom: $spacing-2;
  }
  &__picker {
    width: 100%;
    display: flex;
    flex-direction: column;
  }
}

.time-span__row_error_message {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-bottom: 10px;
  max-width: 305px;
  font-size: 12px;
}

.time-span__description {
  width: 100%;
}

.delete-button {
  margin-top: $spacing-4;
}

.notes-only-display {
  display: flex;
  flex: 1;
  align-items: center;
  height: 40px;
}
