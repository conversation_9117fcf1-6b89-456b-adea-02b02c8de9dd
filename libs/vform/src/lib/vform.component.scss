@use 'design-tokens' as *;
@import 'shimmer';

//@include bounded-inputs();

.text-separator-container {
  padding: 8px;
  width: 100%;
  background: $light-blue;
  border: 1px solid $primary-accent-color;
}

.v-form-container {
  .v-form {
    margin-bottom: 2em;

    .v-form-section {
      padding: 0.4em 0;

      .section-title {
        text-transform: uppercase;
        border-bottom: 1px solid #ccc;
        padding: 0.4em 0;
        margin: 0.4em 0 0.8em;
        font-size: 16px;
        font-weight: 500;
        width: 460px;
      }
      .section-description {
        padding: 0 0 16px;
        color: $gray;
      }
      .radio-label {
        padding: 1em 0 0;
      }
      .v-form-field {
        display: flex;
        flex-flow: column nowrap;
        max-width: 500px;

        &:last-child {
          margin-bottom: 0;
        }

        .field {
          display: flex;
          flex-flow: row wrap;
          justify-content: flex-start;
          align-items: center;
          forms-input-tags {
            margin: 1em 0 0;
          }
          mat-radio-group {
            display: flex;
            flex-flow: column nowrap;
            margin-top: 0.4em;
            mat-radio-button {
              padding: 0.4em 0;
            }
          }
          .locked {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin: 0 2em;
            color: #888888;
            mat-icon {
              font-size: 18px;
              height: 18px;
              width: 18px;
              margin-right: 10px;
            }
          }
        }
        .hint {
          &.indented {
            padding-left: 2em;
          }
          mat-hint {
            font-size: 12px;
          }
        }
        .checkboxes-container {
          .options {
            padding-left: 16px;
            padding-top: 4px;
            mat-checkbox {
              display: block;
            }
          }
        }
      }
      &:first-child.section-title {
        margin: 0 0 0.8em;
      }
      &:only-child {
        padding: 0;
      }
    }
    &.compact-form {
      .v-form-section {
        .section-title {
          font-size: 14px;
          margin-bottom: 4px;
        }
        .v-form-field {
          margin-bottom: -16px;

          // Hidden
          //  Do not show anything for hidden
          &.type-16 {
            display: none;
          }
          // Checkboxes
          //   Checkboxes margin has to be less than -16 otherwise they overlap
          &.type-1 {
            margin-bottom: -8px;
          }
        }
      }
    }
  }
  .v-form-buttons {
    display: flex;
    .left-buttons {
      flex-grow: 1;
    }
  }
  &.faded {
    opacity: 0.2;
    transition: opacity 0.2s ease-in-out;
  }
}

.v-form-loading {
  display: flex;
  flex-flow: column nowrap;

  div {
    height: 32px;
    margin: 0.5em 0;
  }
}

.stencil-shimmer {
  border-radius: 0;
}
