@use 'design-tokens' as *;

// //need theses styles until we can use galaxy select
:host ::ng-deep .mat-form-field-flex > .mat-form-field-infix {
  padding: 0.8em 0px !important;
}

:host ::ng-deep .mat-form-field-label-wrapper {
  top: -1.5em;
}

:host ::ng-deep .mat-form-field-wrapper {
  padding-bottom: 0;
  padding-top: 4px;
}

:host ::ng-deep .mat-mdc-form-field-subscript-wrapper {
  display: none;
}

:host
  ::ng-deep
  .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float
  .mat-form-field-label {
  transform: translateY(-1.1em) scale(0.75);
}

:host ::ng-deep .mat-form-field-outline {
  background-color: $white;
  border-radius: 5px;
}

:host ::ng-deep .glxy-page-title-container {
  border-style: none !important;
}

#action-search {
  flex-grow: 1;
  justify-content: center;
  padding: $spacing-2;
}

.section {
  @include text-preset-3--bold;
}

.explore-list {
  list-style: none;
  margin: 0;
  padding: 0 8px 0 0;
}

.explore-item {
  @include text-preset-4;
  height: 30px;
  vertical-align: middle;
  line-height: 30px;
  span {
    padding-left: 4px;
  }
}

.explore-item:hover {
  background-color: $glxy-grey-300;
  cursor: pointer;
}

.filter-section {
  height: 100%;
  overflow: scroll;
  width: 21%;
  display: flex;
}

@media screen and (max-width: $mobile-breakpoint-max) {
  .filter-section {
    display: none;
  }

  .modal-filter-button {
    display: flex !important;
    flex-direction: row;
    align-items: center;
    box-sizing: border-box;
    background-color: $secondary-background-color;

    .filter-icon {
      margin: 0px;
    }
  }
}

.modal-filter-button {
  display: none;
}

.selector-icon {
  font-size: 14px;
}

.content-height-with-banner {
  height: 100%;
}
.content-height-with-no-banner {
  height: 100%;
}

.content-width-with-banner {
  display: flex;
  flex-direction: column;
  width: 79%;
}
.content-width-with-no-banner {
  width: calc(100% - 256px);
}

.cat-content-container {
  @media screen and (max-width: $mobile-breakpoint-max) {
    flex-direction: column;
  }

  display: flex;
  flex-direction: row;
}

.content-container {
  @media screen and (max-width: $mobile-breakpoint-max) {
    width: 100%;
  }
}

.cat-container {
  @media screen and (max-width: $mobile-breakpoint-max) {
    width: 100%;
  }
}

.section {
  color: $glxy-grey-700;
}

.lib-redirect-banner-flex {
  display: flex;
  width: 100%;
  height: 18%;
}

.lib-discover-products-content-flex {
  display: flex;
  width: 100%;
  height: 82%;
}

.lib-discover-products-content-flex-no-banner {
  display: flex;
  width: 100%;
  height: 100%;
}

.selected {
  @include text-preset-4--bold;
  font-weight: 700;
  line-height: 30px;
  border-left: 2px solid $glxy-blue-800;
  background-color: $glxy-blue-50;
  color: $glxy-blue-700 !important;
}

.page-help {
  color: $gray;
}

.page-help-opened {
  color: $glxy-blue-700;
  background-color: $light-blue;
}

.discover-products-search-field {
  width: 50%;
}
