{"affected": {"defaultBase": "master"}, "tasksRunnerOptions": {"default": {"options": {"canTrackAnalytics": false, "showUsageWarnings": true, "cacheableOperations": ["build-storybook"]}}}, "cli": {"analytics": true}, "defaultProject": "observatory", "generators": {"@nx/angular:application": {"e2eTestRunner": "cypress", "linter": "eslint", "style": "scss", "unitTestRunner": "jest", "strict": false}, "@nx/angular:library": {"linter": "eslint", "unitTestRunner": "jest", "strict": true}, "@nx/angular:component": {"style": "scss", "displayBlock": true, "standalone": true}, "@schematics/angular:component": {"style": "scss", "displayBlock": true, "standalone": true}, "@schematics/angular:directive": {"standalone": true}, "@schematics/angular:pipe": {"standalone": true}}, "$schema": "./node_modules/nx/schemas/nx-schema.json", "targetDefaults": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "cache": true}, "serve": {"executor": "@angular-builders/custom-esbuild:dev-server", "options": {"buildTarget": "{projectName}:build:devServer", "ssl": true, "sslKey": "localhost.key", "sslCert": "localhost.crt", "host": "127.0.0.1"}, "configurations": {"production": {"buildTarget": "{projectName}:build:devServer"}, "fast": {"buildTarget": "{projectName}:build:fast"}, "development": {"buildTarget": "{projectName}:build:devServer"}}}, "e2e": {"inputs": ["default", "^production"], "cache": true}, "integration": {"inputs": ["default", "^production"]}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "cache": true}, "build-storybook": {"inputs": ["default", "^production", "{projectRoot}/.storybook/**/*", "{projectRoot}/tsconfig.storybook.json"], "cache": true}, "component-test": {"inputs": ["default", "^production"], "cache": true}, "@nx/jest:jest": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "cache": true, "options": {"passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "@nx/eslint:lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "cache": true}, "@angular-devkit/build-angular:application": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "@angular-devkit/build-angular:browser": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "sharedGlobals": [], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/.storybook/**/*", "!{projectRoot}/**/*.stories.@(js|jsx|ts|tsx|mdx)", "!{projectRoot}/cypress/**/*", "!{projectRoot}/**/*.cy.[jt]s?(x)", "!{projectRoot}/cypress.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/tsconfig.storybook.json"]}, "defaultBase": "master", "nxCloudAccessToken": "MDdkYmM1ZDktOWZiOC00Zjk2LTg3NTgtOWFiMzEwZjQxMjIyfHJlYWQtd3JpdGU=", "parallel": 3, "plugins": [{"plugin": "@nx/storybook/plugin", "options": {"serveStorybookTargetName": "serve:storybook", "buildStorybookTargetName": "build:storybook", "testStorybookTargetName": "test:storybook", "staticStorybookTargetName": "static:storybook"}}], "useLegacyCache": true}