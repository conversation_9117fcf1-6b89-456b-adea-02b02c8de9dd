import { Injectable, inject } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import {
  ListingProfileApiService,
  ProjectionFilter,
  SEOApiService,
  GetActiveSEOAddonsRequest,
  GetMultiAccountGroupRequest,
  GetMultiAccountGroupResponse,
} from '@vendasta/listing-products';
import {
  AccountsService,
  AppAndAddonActivationStatus,
  ListAppAndAddonActivationStatusFilter,
} from '@vendasta/accounts/legacy';

export interface ConsolidatedDataItem {
  businessId: string;
  listingProfile: {
    richData: {
      seoKeywords: string[];
      syncingSeoKeywords: string[];
    };
  };
  accountGroup: {
    napData: {
      companyName: string;
      address: string;
      city: string;
      state: string;
      zip: string;
      country: string;
    };
    externalIdentifiers: {
      partnerId: string;
      marketId: string;
    };
  };
  seoSettings: {
    favoriteKeywords: string[];
  };
}

export interface ConsolidatedDataResponse {
  consolidatedData: ConsolidatedDataItem[];
}

@Injectable({
  providedIn: 'root',
})
export class ConsolidatedDataService {
  private translate = inject(TranslateService);
  private listingProfileService = inject(ListingProfileApiService);
  private accountGroupService = inject(AccountGroupApiService);
  private seoService = inject(SEOApiService);
  private accountsService = inject(AccountsService);

  async getConsolidatedData(businessIds: string[]): Promise<ConsolidatedDataResponse> {
    try {
      const multiAccountGroupRequest = new GetMultiAccountGroupRequest({
        businessIds: businessIds,
        projectionFilter: new ProjectionFilter({
          externalIdentifiers: true,
          richData: true,
          napData: true,
          googleAttributes: false,
          googleAttributesMetadata: false,
          bingAttributes: false,
          bingAttributesMetadata: false,
          legacyProductDetails: false,
          socialUrls: false,
          businessHours: false,
        }),
        readFilter: {
          includeDeleted: false,
        },
        languageCode: this.translate.currentLang || this.translate.defaultLang || 'en',
      });

      const multiAccountGroupResponse = await firstValueFrom(
        this.listingProfileService.getMultiAccountGroup(multiAccountGroupRequest)
      );
      
      return this.transformMultiAccountGroupResponse(multiAccountGroupResponse);
    } catch (error) {
      console.error('Failed to get consolidated data:', error);
      console.warn('Falling back to empty response due to API error');
      return { consolidatedData: [] };
    }
  }

  private transformMultiAccountGroupResponse(
    multiAccountGroupResponse: GetMultiAccountGroupResponse,
  ): ConsolidatedDataResponse {
    if (!multiAccountGroupResponse.consolidatedData) {
      return { consolidatedData: [] };
    }

    const transformedData: ConsolidatedDataItem[] = multiAccountGroupResponse.consolidatedData
      .filter((container) => container.businessId)
      .map((container) => {
        const listingProfile = container.listingProfile;
        const accountGroup = container.accountGroup;

        return {
          businessId: container.businessId || '',
          listingProfile: {
            richData: {
              seoKeywords: listingProfile?.richData?.seoKeywords || [],
              syncingSeoKeywords: listingProfile?.richData?.syncingSeoKeywords || [],
            },
          },
          accountGroup: {
            napData: {
              companyName: accountGroup?.napData?.companyName || '',
              address: accountGroup?.napData?.address || '',
              city: accountGroup?.napData?.city || '',
              state: accountGroup?.napData?.state || '',
              zip: accountGroup?.napData?.zip || '',
              country: accountGroup?.napData?.country || '',
            },
            externalIdentifiers: {
              partnerId: accountGroup?.externalIdentifiers?.partnerId || '',
              marketId: accountGroup?.externalIdentifiers?.marketId || '',
            },
          },
          seoSettings: {
            favoriteKeywords: container.seoSettings?.favoriteKeywords || [],
          },
        };
      })
      .filter((item): item is ConsolidatedDataItem => item !== null);

    return { consolidatedData: transformedData };
  }

  extractBusinessKeywordDataFromConsolidated(
    consolidatedResponse: ConsolidatedDataResponse,
  ): Promise<Map<string, any>> {
    const businessDataMap = new Map<string, any>();

    if (!consolidatedResponse?.consolidatedData) {
      console.warn('No consolidated data available');
      return Promise.resolve(businessDataMap);
    }

    const promises = consolidatedResponse.consolidatedData.map(async (item) => {
      if (!item.businessId) {
        console.warn('Skipping item without businessId');
        return null;
      }

      const existingKeywords = (item.listingProfile?.richData?.seoKeywords || []).filter((k) => k && k.trim() !== '');
      const favoriteKeywords = (item.seoSettings?.favoriteKeywords || []).filter((k) => k && k.trim() !== '');
      const syncingKeywords = (item.listingProfile?.richData?.syncingSeoKeywords || []).filter(
        (k) => k && k.trim() !== '',
      );

      const { isPro, keywordLimit } = await this.getBusinessEditionInfo(item.businessId);

      const businessData = {
        businessId: item.businessId,
        businessName: item.accountGroup?.napData?.companyName || `Business ${item.businessId}`,
        address: this.formatAddress(item.accountGroup?.napData || {}),
        existingKeywords,
        favoriteKeywords,
        syncingKeywords,
        keywordLimit,
        isPro,
        partnerId: item.accountGroup?.externalIdentifiers?.partnerId || '',
        marketId: item.accountGroup?.externalIdentifiers?.marketId || '',
      };

      return { businessId: item.businessId, businessData };
    });

    return Promise.all(promises).then((results) => {
      results.forEach((result) => {
        if (result) {
          businessDataMap.set(result.businessId, result.businessData);
        }
      });
      return businessDataMap;
    });
  }

  private async getBusinessEditionInfo(businessId: string): Promise<{ isPro: boolean; keywordLimit: number }> {
    try {
      const DEMO_PAID_EDITION = 'EDITION-MXWLTQPN';
      const PROD_PAID_EDITION = 'EDITION-CFH5CKHC';
      const ADDON_INCREMENT = 15;
      const FREE_EDITION_KEYWORDS = 3;
      const PAID_EDITION_KEYWORDS = 15;

      const filters: ListAppAndAddonActivationStatusFilter = {
        appIds: ['MS'],
        statuses: [AppAndAddonActivationStatus.ACTIVATED, AppAndAddonActivationStatus.CANCELED],
      };

      const activations = await firstValueFrom(
        this.accountsService.listAppsAndAddonsActivationStatusesForBusiness(businessId, filters),
      );

      let isPro = false;
      if (activations?.length > 0) {
        const editionId = activations[0].editionId;
        isPro = editionId === DEMO_PAID_EDITION || editionId === PROD_PAID_EDITION;
      }

      let keywordLimit = isPro ? PAID_EDITION_KEYWORDS : FREE_EDITION_KEYWORDS;

      try {
        const addOnsResponse = await firstValueFrom(
          this.seoService.getActiveSeoAddons(
            new GetActiveSEOAddonsRequest({
              businessId: businessId,
            }),
          ),
        );

        if (addOnsResponse?.activeAddons?.length > 0) {
          const addonCount = addOnsResponse.activeAddons[0].count;
          const limitIncrement = addonCount * ADDON_INCREMENT;
          keywordLimit += limitIncrement;
        }
      } catch (error) {
        console.warn(`Could not get add-ons for business ${businessId}:`, error);
      }

      return { isPro, keywordLimit };
    } catch (error) {
      console.warn(`Could not get edition info for business ${businessId}:`, error);
      return { isPro: false, keywordLimit: 3 };
    }
  }

  private formatAddress(napData: any): string {
    if (!napData) {
      return '';
    }

    const parts = [napData.address, napData.city, napData.state, napData.zip, napData.country].filter(Boolean);
    return parts.join(', ');
  }
}
